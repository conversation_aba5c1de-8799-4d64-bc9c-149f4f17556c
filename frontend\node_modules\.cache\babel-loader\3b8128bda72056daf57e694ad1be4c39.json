{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M7 17h14V3H7v14zm4.25-5.53l1.96 2.36 2.75-3.54L19.5 15h-11l2.75-3.53z\",\n  opacity: \".3\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M1 21c0 1.1.9 2 2 2h16v-2H3V5H1v16zM21 1H7c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 16H7V3h14v14zm-5.04-6.71l-2.75 3.54-1.96-2.36L8.5 15h11z\"\n})), 'FilterTwoTone');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "Fragment", "d", "opacity"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/FilterTwoTone.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M7 17h14V3H7v14zm4.25-5.53l1.96 2.36 2.75-3.54L19.5 15h-11l2.75-3.53z\",\n  opacity: \".3\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M1 21c0 1.1.9 2 2 2h16v-2H3V5H1v16zM21 1H7c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 16H7V3h14v14zm-5.04-6.71l-2.75 3.54-1.96-2.36L8.5 15h11z\"\n})), 'FilterTwoTone');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAACF,KAAK,CAACG,QAAQ,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC5HE,CAAC,EAAE,uEAAuE;EAC1EC,OAAO,EAAE;AACX,CAAC,CAAC,EAAE,aAAaL,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC3CE,CAAC,EAAE;AACL,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}