{"ast": null, "code": "// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nvar getRandomValues;\nvar rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation. Also,\n    // find the complete implementation of crypto (msCrypto) on IE11.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== 'undefined' && typeof msCrypto.getRandomValues === 'function' && msCrypto.getRandomValues.bind(msCrypto);\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n  return getRandomValues(rnds8);\n}", "map": {"version": 3, "names": ["getRandomValues", "rnds8", "Uint8Array", "rng", "crypto", "bind", "msCrypto", "Error"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/uuid/dist/esm-browser/rng.js"], "sourcesContent": ["// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nvar getRandomValues;\nvar rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation. Also,\n    // find the complete implementation of crypto (msCrypto) on IE11.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== 'undefined' && typeof msCrypto.getRandomValues === 'function' && msCrypto.getRandomValues.bind(msCrypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,eAAe;AACnB,IAAIC,KAAK,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;AAC9B,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC5B;EACA,IAAI,CAACH,eAAe,EAAE;IACpB;IACA;IACAA,eAAe,GAAG,OAAOI,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACJ,eAAe,IAAII,MAAM,CAACJ,eAAe,CAACK,IAAI,CAACD,MAAM,CAAC,IAAI,OAAOE,QAAQ,KAAK,WAAW,IAAI,OAAOA,QAAQ,CAACN,eAAe,KAAK,UAAU,IAAIM,QAAQ,CAACN,eAAe,CAACK,IAAI,CAACC,QAAQ,CAAC;IAEhP,IAAI,CAACN,eAAe,EAAE;MACpB,MAAM,IAAIO,KAAK,CAAC,0GAA0G,CAAC;IAC7H;EACF;EAEA,OAAOP,eAAe,CAACC,KAAK,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}