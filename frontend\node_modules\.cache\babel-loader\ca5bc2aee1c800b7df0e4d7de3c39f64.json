{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\layout\\\\index.js\";\nimport React, { useState, useContext, useEffect } from \"react\";\nimport clsx from \"clsx\";\nimport { makeStyles, Drawer, AppBar, Toolbar, List, Typography, Divider, MenuItem, IconButton, Menu, useTheme, useMediaQuery, Box } from \"@material-ui/core\";\nimport Badge from \"@material-ui/core/Badge\";\nimport MenuIcon from \"@material-ui/icons/Menu\";\nimport ChevronLeftIcon from \"@material-ui/icons/ChevronLeft\";\nimport AccountCircle from \"@material-ui/icons/AccountCircle\";\n\n// Importar novos componentes modernos\nimport ModernHeader from \"../components/ModernHeader\";\nimport ModernSidebar from \"../components/ModernSidebar\";\nimport MainListItems from \"./MainListItems\";\nimport NotificationsPopOver from \"../components/NotificationsPopOver\";\nimport UserModal from \"../components/UserModal\";\nimport { AuthContext } from \"../context/Auth/AuthContext\";\nimport BackdropLoading from \"../components/BackdropLoading\";\nimport { i18n } from \"../translate/i18n\";\nimport toastError from \"../errors/toastError\";\nimport AnnouncementsPopover from \"../components/AnnouncementsPopover\";\nimport logo from \"../assets/logo.png\";\nimport { socketConnection } from \"../services/socket\";\nimport ChatPopover from \"../pages/Chat/ChatPopover\";\nconst drawerWidth = 300;\nconst useStyles = makeStyles(theme => ({\n  root: {\n    display: \"flex\",\n    height: \"100vh\",\n    [theme.breakpoints.down(\"sm\")]: {\n      height: \"calc(100vh - 56px)\"\n    }\n  },\n  toolbar: {\n    paddingRight: 24 // keep right padding when drawer closed\n  },\n  toolbarIcon: {\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"space-between\",\n    padding: \"0 8px\",\n    minHeight: \"48px\",\n    boxSizing: \"border-box\"\n  },\n  appBar: {\n    zIndex: theme.zIndex.drawer + 1,\n    transition: theme.transitions.create([\"width\", \"margin\"], {\n      easing: theme.transitions.easing.sharp,\n      duration: theme.transitions.duration.leavingScreen\n    })\n  },\n  appBarShift: {\n    marginLeft: drawerWidth,\n    width: `calc(100% - ${drawerWidth}px)`,\n    transition: theme.transitions.create([\"width\", \"margin\"], {\n      easing: theme.transitions.easing.sharp,\n      duration: theme.transitions.duration.enteringScreen\n    })\n  },\n  menuButton: {\n    marginRight: 36\n  },\n  menuButtonHidden: {\n    display: \"none\"\n  },\n  title: {\n    flexGrow: 1,\n    fontSize: 14\n  },\n  drawerPaper: {\n    position: \"relative\",\n    whiteSpace: \"nowrap\",\n    width: drawerWidth,\n    transition: theme.transitions.create(\"width\", {\n      easing: theme.transitions.easing.sharp,\n      duration: theme.transitions.duration.enteringScreen\n    })\n  },\n  drawerPaperClose: {\n    overflowX: \"hidden\",\n    transition: theme.transitions.create(\"width\", {\n      easing: theme.transitions.easing.sharp,\n      duration: theme.transitions.duration.leavingScreen\n    }),\n    width: theme.spacing(7),\n    [theme.breakpoints.up(\"sm\")]: {\n      width: theme.spacing(9)\n    }\n  },\n  appBarSpacer: {\n    minHeight: \"48px\"\n  },\n  content: {\n    flex: 1,\n    overflow: \"auto\",\n    ...theme.scrollbarStyles\n  },\n  container: {\n    paddingTop: theme.spacing(4),\n    paddingBottom: theme.spacing(4)\n  },\n  paper: {\n    padding: theme.spacing(2),\n    display: \"flex\",\n    overflow: \"auto\",\n    flexDirection: \"column\"\n  },\n  containerWithScroll: {\n    flex: 1,\n    padding: theme.spacing(1),\n    overflowY: \"scroll\",\n    ...theme.scrollbarStyles\n  }\n}));\nconst LoggedInLayout = ({\n  children\n}) => {\n  const classes = useStyles();\n  const [userModalOpen, setUserModalOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [menuOpen, setMenuOpen] = useState(false);\n  const {\n    handleLogout,\n    loading\n  } = useContext(AuthContext);\n  const [drawerOpen, setDrawerOpen] = useState(false);\n  const [drawerVariant, setDrawerVariant] = useState(\"permanent\");\n  const {\n    user\n  } = useContext(AuthContext);\n  const theme = useTheme();\n  const greaterThenSm = useMediaQuery(theme.breakpoints.up(\"sm\"));\n  useEffect(() => {\n    if (document.body.offsetWidth > 600) {\n      setDrawerOpen(true);\n    }\n  }, []);\n  useEffect(() => {\n    if (document.body.offsetWidth < 600) {\n      setDrawerVariant(\"temporary\");\n    } else {\n      setDrawerVariant(\"permanent\");\n    }\n  }, [drawerOpen]);\n  useEffect(() => {\n    const companyId = localStorage.getItem(\"companyId\");\n    const userId = localStorage.getItem(\"userId\");\n    const socket = socketConnection({\n      companyId\n    });\n    socket.on(`company-${companyId}-auth`, data => {\n      if (data.user.id === +userId) {\n        toastError(\"Sua conta foi acessada em outro computador.\");\n        setTimeout(() => {\n          localStorage.clear();\n          window.location.reload();\n        }, 1000);\n      }\n    });\n    socket.emit(\"userStatus\");\n    const interval = setInterval(() => {\n      socket.emit(\"userStatus\");\n    }, 1000 * 60 * 5);\n    return () => {\n      socket.disconnect();\n      clearInterval(interval);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const handleMenu = event => {\n    setAnchorEl(event.currentTarget);\n    setMenuOpen(true);\n  };\n  const handleCloseMenu = () => {\n    setAnchorEl(null);\n    setMenuOpen(false);\n  };\n  const handleOpenUserModal = () => {\n    setUserModalOpen(true);\n    handleCloseMenu();\n  };\n  const handleClickLogout = () => {\n    handleCloseMenu();\n    handleLogout();\n  };\n  const drawerClose = () => {\n    if (document.body.offsetWidth < 600) {\n      setDrawerOpen(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/React.createElement(BackdropLoading, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 12\n      }\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.root,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(Drawer, {\n    variant: drawerVariant,\n    className: drawerOpen ? classes.drawerPaper : classes.drawerPaperClose,\n    classes: {\n      paper: clsx(classes.drawerPaper, !drawerOpen && classes.drawerPaperClose)\n    },\n    open: drawerOpen,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.toolbarIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: logo,\n    style: {\n      margin: \"0 auto\",\n      height: '100%',\n      width: '90%',\n      alignSelf: 'center'\n    },\n    alt: \"logo\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(IconButton, {\n    onClick: () => setDrawerOpen(!drawerOpen),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(ChevronLeftIcon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 13\n    }\n  }))), /*#__PURE__*/React.createElement(Divider, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 9\n    }\n  }), /*#__PURE__*/React.createElement(List, {\n    className: classes.containerWithScroll,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(MainListItems, {\n    drawerClose: drawerClose,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 11\n    }\n  })), /*#__PURE__*/React.createElement(Divider, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 9\n    }\n  })), /*#__PURE__*/React.createElement(UserModal, {\n    open: userModalOpen,\n    onClose: () => setUserModalOpen(false),\n    userId: user === null || user === void 0 ? void 0 : user.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(AppBar, {\n    position: \"absolute\",\n    className: clsx(classes.appBar, drawerOpen && classes.appBarShift),\n    color: \"primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Toolbar, {\n    variant: \"dense\",\n    className: classes.toolbar,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(IconButton, {\n    edge: \"start\",\n    variant: \"contained\",\n    \"aria-label\": \"open drawer\",\n    onClick: () => setDrawerOpen(!drawerOpen),\n    className: clsx(classes.menuButton, drawerOpen && classes.menuButtonHidden),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(MenuIcon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 13\n    }\n  })), /*#__PURE__*/React.createElement(Typography, {\n    component: \"h1\",\n    variant: \"h6\",\n    color: \"inherit\",\n    noWrap: true,\n    className: classes.title,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 11\n    }\n  }, greaterThenSm ? /*#__PURE__*/React.createElement(React.Fragment, null, \"Ol\\xE1 \", /*#__PURE__*/React.createElement(\"b\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 21\n    }\n  }, user.name), \", Seja bem-vindo.\") : user.name), /*#__PURE__*/React.createElement(Badge, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 11\n    }\n  }), user.id && /*#__PURE__*/React.createElement(NotificationsPopOver, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 23\n    }\n  }), /*#__PURE__*/React.createElement(AnnouncementsPopover, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(ChatPopover, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(IconButton, {\n    \"aria-label\": \"account of current user\",\n    \"aria-controls\": \"menu-appbar\",\n    \"aria-haspopup\": \"true\",\n    onClick: handleMenu,\n    variant: \"contained\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(AccountCircle, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(Menu, {\n    id: \"menu-appbar\",\n    anchorEl: anchorEl,\n    getContentAnchorEl: null,\n    anchorOrigin: {\n      vertical: \"bottom\",\n      horizontal: \"right\"\n    },\n    transformOrigin: {\n      vertical: \"top\",\n      horizontal: \"right\"\n    },\n    open: menuOpen,\n    onClose: handleCloseMenu,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(MenuItem, {\n    onClick: handleOpenUserModal,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 15\n    }\n  }, i18n.t(\"mainDrawer.appBar.user.profile\")))))), /*#__PURE__*/React.createElement(\"main\", {\n    className: classes.content,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.appBarSpacer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 9\n    }\n  }), children ? children : null));\n};\nexport default LoggedInLayout;", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "clsx", "makeStyles", "Drawer", "AppBar", "<PERSON><PERSON><PERSON>", "List", "Typography", "Divider", "MenuItem", "IconButton", "<PERSON><PERSON>", "useTheme", "useMediaQuery", "Box", "Badge", "MenuIcon", "ChevronLeftIcon", "AccountCircle", "ModernHeader", "ModernSidebar", "MainListItems", "NotificationsPopOver", "UserModal", "AuthContext", "BackdropLoading", "i18n", "toastError", "AnnouncementsPopover", "logo", "socketConnection", "ChatPopover", "drawerWidth", "useStyles", "theme", "root", "display", "height", "breakpoints", "down", "toolbar", "paddingRight", "toolbarIcon", "alignItems", "justifyContent", "padding", "minHeight", "boxSizing", "appBar", "zIndex", "drawer", "transition", "transitions", "create", "easing", "sharp", "duration", "leavingScreen", "appBarShift", "marginLeft", "width", "enteringScreen", "menuButton", "marginRight", "menuButtonHidden", "title", "flexGrow", "fontSize", "drawerPaper", "position", "whiteSpace", "drawerPaperClose", "overflowX", "spacing", "up", "appBarSpacer", "content", "flex", "overflow", "scrollbarStyles", "container", "paddingTop", "paddingBottom", "paper", "flexDirection", "containerWithScroll", "overflowY", "LoggedInLayout", "children", "classes", "userModalOpen", "setUserModalOpen", "anchorEl", "setAnchorEl", "menuOpen", "setMenuOpen", "handleLogout", "loading", "drawerOpen", "setDrawerOpen", "drawerVariant", "setDrawerVariant", "user", "greaterThenSm", "document", "body", "offsetWidth", "companyId", "localStorage", "getItem", "userId", "socket", "on", "data", "id", "setTimeout", "clear", "window", "location", "reload", "emit", "interval", "setInterval", "disconnect", "clearInterval", "handleMenu", "event", "currentTarget", "handleCloseMenu", "handleOpenUserModal", "handleClickLogout", "drawerClose", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "variant", "open", "src", "style", "margin", "alignSelf", "alt", "onClick", "onClose", "color", "edge", "component", "noWrap", "Fragment", "name", "getContentAnchorEl", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "t"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/layout/index.js"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport clsx from \"clsx\";\r\n\r\nimport {\r\n  makeStyles,\r\n  Drawer,\r\n  AppBar,\r\n  Toolbar,\r\n  List,\r\n  Typography,\r\n  Divider,\r\n  MenuItem,\r\n  IconButton,\r\n  Menu,\r\n  useTheme,\r\n  useMediaQuery,\r\n  Box,\r\n} from \"@material-ui/core\";\r\n\r\nimport Badge from \"@material-ui/core/Badge\";\r\n\r\nimport MenuIcon from \"@material-ui/icons/Menu\";\r\nimport ChevronLeftIcon from \"@material-ui/icons/ChevronLeft\";\r\nimport AccountCircle from \"@material-ui/icons/AccountCircle\";\r\n\r\n// Importar novos componentes modernos\r\nimport ModernHeader from \"../components/ModernHeader\";\r\nimport ModernSidebar from \"../components/ModernSidebar\";\r\n\r\nimport MainListItems from \"./MainListItems\";\r\nimport NotificationsPopOver from \"../components/NotificationsPopOver\";\r\nimport UserModal from \"../components/UserModal\";\r\nimport { AuthContext } from \"../context/Auth/AuthContext\";\r\nimport BackdropLoading from \"../components/BackdropLoading\";\r\nimport { i18n } from \"../translate/i18n\";\r\nimport toastError from \"../errors/toastError\";\r\nimport AnnouncementsPopover from \"../components/AnnouncementsPopover\";\r\n\r\nimport logo from \"../assets/logo.png\"; \r\nimport { socketConnection } from \"../services/socket\";\r\nimport ChatPopover from \"../pages/Chat/ChatPopover\";\r\n\r\nconst drawerWidth = 300;\r\n\r\nconst useStyles = makeStyles((theme) => ({\r\n  root: {\r\n    display: \"flex\",\r\n    height: \"100vh\",\r\n\r\n    [theme.breakpoints.down(\"sm\")]: {\r\n      height: \"calc(100vh - 56px)\",\r\n    },\r\n  },\r\n\r\n  toolbar: {\r\n    paddingRight: 24, // keep right padding when drawer closed\r\n  },\r\n  toolbarIcon: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"space-between\",\r\n    padding: \"0 8px\",\r\n    minHeight: \"48px\",\r\n    boxSizing:\"border-box\",\r\n  },\r\n  appBar: {\r\n    zIndex: theme.zIndex.drawer + 1,\r\n    transition: theme.transitions.create([\"width\", \"margin\"], {\r\n      easing: theme.transitions.easing.sharp,\r\n      duration: theme.transitions.duration.leavingScreen,\r\n    }),\r\n  },\r\n  appBarShift: {\r\n    marginLeft: drawerWidth,\r\n    width: `calc(100% - ${drawerWidth}px)`,\r\n    transition: theme.transitions.create([\"width\", \"margin\"], {\r\n      easing: theme.transitions.easing.sharp,\r\n      duration: theme.transitions.duration.enteringScreen,\r\n    }),\r\n  },\r\n  menuButton: {\r\n    marginRight: 36,\r\n  },\r\n  menuButtonHidden: {\r\n    display: \"none\",\r\n  },\r\n  title: {\r\n    flexGrow: 1,\r\n    fontSize: 14,\r\n  },\r\n  drawerPaper: {\r\n    position: \"relative\",\r\n    whiteSpace: \"nowrap\",\r\n    width: drawerWidth,\r\n    transition: theme.transitions.create(\"width\", {\r\n      easing: theme.transitions.easing.sharp,\r\n      duration: theme.transitions.duration.enteringScreen,\r\n    }),\r\n  },\r\n  drawerPaperClose: {\r\n    overflowX: \"hidden\",\r\n    transition: theme.transitions.create(\"width\", {\r\n      easing: theme.transitions.easing.sharp,\r\n      duration: theme.transitions.duration.leavingScreen,\r\n    }),\r\n    width: theme.spacing(7),\r\n    [theme.breakpoints.up(\"sm\")]: {\r\n      width: theme.spacing(9),\r\n    },\r\n  },\r\n  appBarSpacer: {\r\n    minHeight: \"48px\",\r\n  },\r\n  content: {\r\n    flex: 1,\r\n    overflow: \"auto\",\r\n    ...theme.scrollbarStyles,\r\n  },\r\n  container: {\r\n    paddingTop: theme.spacing(4),\r\n    paddingBottom: theme.spacing(4),\r\n  },\r\n  paper: {\r\n    padding: theme.spacing(2),\r\n    display: \"flex\",\r\n    overflow: \"auto\",\r\n    flexDirection: \"column\",\r\n  },\r\n  containerWithScroll: {\r\n    flex: 1,\r\n    padding: theme.spacing(1),\r\n    overflowY: \"scroll\",\r\n    ...theme.scrollbarStyles,\r\n  },\r\n}));\r\n\r\nconst LoggedInLayout = ({ children }) => {\r\n  const classes = useStyles();\r\n  const [userModalOpen, setUserModalOpen] = useState(false);\r\n  const [anchorEl, setAnchorEl] = useState(null);\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const { handleLogout, loading } = useContext(AuthContext);\r\n  const [drawerOpen, setDrawerOpen] = useState(false);\r\n  const [drawerVariant, setDrawerVariant] = useState(\"permanent\");\r\n  const { user } = useContext(AuthContext);\r\n\r\n  const theme = useTheme();\r\n  const greaterThenSm = useMediaQuery(theme.breakpoints.up(\"sm\"));\r\n\r\n  useEffect(() => {\r\n    if (document.body.offsetWidth > 600) {\r\n      setDrawerOpen(true);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (document.body.offsetWidth < 600) {\r\n      setDrawerVariant(\"temporary\");\r\n    } else {\r\n      setDrawerVariant(\"permanent\");\r\n    }\r\n  }, [drawerOpen]);\r\n\r\n  useEffect(() => {\r\n    const companyId = localStorage.getItem(\"companyId\");\r\n    const userId = localStorage.getItem(\"userId\");\r\n\r\n    const socket = socketConnection({ companyId });\r\n\r\n    socket.on(`company-${companyId}-auth`, (data) => {\r\n      if (data.user.id === +userId) {\r\n        toastError(\"Sua conta foi acessada em outro computador.\");\r\n        setTimeout(() => {\r\n          localStorage.clear();\r\n          window.location.reload();\r\n        }, 1000);\r\n      }\r\n    });\r\n\r\n    socket.emit(\"userStatus\");\r\n    const interval = setInterval(() => {\r\n      socket.emit(\"userStatus\");\r\n    }, 1000 * 60 * 5);\r\n\r\n    return () => {\r\n      socket.disconnect();\r\n      clearInterval(interval);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const handleMenu = (event) => {\r\n    setAnchorEl(event.currentTarget);\r\n    setMenuOpen(true);\r\n  };\r\n\r\n  const handleCloseMenu = () => {\r\n    setAnchorEl(null);\r\n    setMenuOpen(false);\r\n  };\r\n\r\n  const handleOpenUserModal = () => {\r\n    setUserModalOpen(true);\r\n    handleCloseMenu();\r\n  };\r\n\r\n  const handleClickLogout = () => {\r\n    handleCloseMenu();\r\n    handleLogout();\r\n  };\r\n\r\n  const drawerClose = () => {\r\n    if (document.body.offsetWidth < 600) {\r\n      setDrawerOpen(false);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return <BackdropLoading />;\r\n  }\r\n\r\n\r\n  return (\r\n    <div className={classes.root}>\r\n      <Drawer\r\n        variant={drawerVariant}\r\n        className={drawerOpen ? classes.drawerPaper : classes.drawerPaperClose}\r\n        classes={{\r\n          paper: clsx(\r\n            classes.drawerPaper,\r\n            !drawerOpen && classes.drawerPaperClose\r\n          ),\r\n        }}\r\n        open={drawerOpen}\r\n      >\r\n        <div className={classes.toolbarIcon}>\r\n          <img src={logo} style={{ margin: \"0 auto\", height: '100%', width: '90%',alignSelf: 'center' }} alt=\"logo\" />\r\n          <IconButton onClick={() => setDrawerOpen(!drawerOpen)}>\r\n            <ChevronLeftIcon />\r\n          </IconButton>\r\n        </div>\r\n        <Divider />\r\n        <List className={classes.containerWithScroll}>\r\n          <MainListItems drawerClose={drawerClose} />\r\n        </List>\r\n        <Divider />\r\n      </Drawer>\r\n      <UserModal\r\n        open={userModalOpen}\r\n        onClose={() => setUserModalOpen(false)}\r\n        userId={user?.id}\r\n      />\r\n      <AppBar\r\n        position=\"absolute\"\r\n        className={clsx(classes.appBar, drawerOpen && classes.appBarShift)}\r\n        color=\"primary\"\r\n      >\r\n        <Toolbar variant=\"dense\" className={classes.toolbar}>\r\n          <IconButton\r\n            edge=\"start\"\r\n            variant=\"contained\"\r\n            aria-label=\"open drawer\"\r\n            onClick={() => setDrawerOpen(!drawerOpen)}\r\n            className={clsx(\r\n              classes.menuButton,\r\n              drawerOpen && classes.menuButtonHidden\r\n            )}\r\n          >\r\n            <MenuIcon />\r\n          </IconButton>\r\n          <Typography\r\n            component=\"h1\"\r\n            variant=\"h6\"\r\n            color=\"inherit\"\r\n            noWrap\r\n            className={classes.title}\r\n          >\r\n            {greaterThenSm ? (\r\n              <>\r\n                Olá <b>{user.name}</b>, Seja bem-vindo.\r\n              </>\r\n            ) : (\r\n              user.name\r\n            )}\r\n          </Typography>\r\n          <Badge\r\n          \r\n          />\r\n          {user.id && <NotificationsPopOver />}\r\n\r\n          <AnnouncementsPopover />\r\n\r\n          <ChatPopover />\r\n\r\n          <div>\r\n            <IconButton\r\n              aria-label=\"account of current user\"\r\n              aria-controls=\"menu-appbar\"\r\n              aria-haspopup=\"true\"\r\n              onClick={handleMenu}\r\n              variant=\"contained\"\r\n\r\n            >\r\n              <AccountCircle />\r\n            </IconButton>\r\n            <Menu\r\n              id=\"menu-appbar\"\r\n              anchorEl={anchorEl}\r\n              getContentAnchorEl={null}\r\n              anchorOrigin={{\r\n                vertical: \"bottom\",\r\n                horizontal: \"right\",\r\n              }}\r\n              transformOrigin={{\r\n                vertical: \"top\",\r\n                horizontal: \"right\",\r\n              }}\r\n              open={menuOpen}\r\n              onClose={handleCloseMenu}\r\n            >\r\n              <MenuItem onClick={handleOpenUserModal}>\r\n                {i18n.t(\"mainDrawer.appBar.user.profile\")}\r\n              </MenuItem>\r\n\r\n            </Menu>\r\n          </div>\r\n        </Toolbar>\r\n      </AppBar>\r\n      <main className={classes.content}>\r\n        <div className={classes.appBarSpacer} />\r\n\r\n        {children ? children : null}\r\n      </main>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoggedInLayout;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,OAAOC,IAAI,MAAM,MAAM;AAEvB,SACEC,UAAU,EACVC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,aAAa,EACbC,GAAG,QACE,mBAAmB;AAE1B,OAAOC,KAAK,MAAM,yBAAyB;AAE3C,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,OAAOC,aAAa,MAAM,kCAAkC;;AAE5D;AACA,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,aAAa,MAAM,6BAA6B;AAEvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,oBAAoB,MAAM,oCAAoC;AACrE,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,SAASC,WAAW,QAAQ,6BAA6B;AACzD,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,IAAI,QAAQ,mBAAmB;AACxC,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,oBAAoB,MAAM,oCAAoC;AAErE,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,WAAW,MAAM,2BAA2B;AAEnD,MAAMC,WAAW,GAAG,GAAG;AAEvB,MAAMC,SAAS,GAAG/B,UAAU,CAAEgC,KAAK,KAAM;EACvCC,IAAI,EAAE;IACJC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,OAAO;IAEf,CAACH,KAAK,CAACI,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG;MAC9BF,MAAM,EAAE;IACV;EACF,CAAC;EAEDG,OAAO,EAAE;IACPC,YAAY,EAAE,EAAE,CAAE;EACpB,CAAC;EACDC,WAAW,EAAE;IACXN,OAAO,EAAE,MAAM;IACfO,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAC;EACZ,CAAC;EACDC,MAAM,EAAE;IACNC,MAAM,EAAEf,KAAK,CAACe,MAAM,CAACC,MAAM,GAAG,CAAC;IAC/BC,UAAU,EAAEjB,KAAK,CAACkB,WAAW,CAACC,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;MACxDC,MAAM,EAAEpB,KAAK,CAACkB,WAAW,CAACE,MAAM,CAACC,KAAK;MACtCC,QAAQ,EAAEtB,KAAK,CAACkB,WAAW,CAACI,QAAQ,CAACC;IACvC,CAAC;EACH,CAAC;EACDC,WAAW,EAAE;IACXC,UAAU,EAAE3B,WAAW;IACvB4B,KAAK,EAAE,eAAe5B,WAAW,KAAK;IACtCmB,UAAU,EAAEjB,KAAK,CAACkB,WAAW,CAACC,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;MACxDC,MAAM,EAAEpB,KAAK,CAACkB,WAAW,CAACE,MAAM,CAACC,KAAK;MACtCC,QAAQ,EAAEtB,KAAK,CAACkB,WAAW,CAACI,QAAQ,CAACK;IACvC,CAAC;EACH,CAAC;EACDC,UAAU,EAAE;IACVC,WAAW,EAAE;EACf,CAAC;EACDC,gBAAgB,EAAE;IAChB5B,OAAO,EAAE;EACX,CAAC;EACD6B,KAAK,EAAE;IACLC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE;EACZ,CAAC;EACDC,WAAW,EAAE;IACXC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,QAAQ;IACpBV,KAAK,EAAE5B,WAAW;IAClBmB,UAAU,EAAEjB,KAAK,CAACkB,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;MAC5CC,MAAM,EAAEpB,KAAK,CAACkB,WAAW,CAACE,MAAM,CAACC,KAAK;MACtCC,QAAQ,EAAEtB,KAAK,CAACkB,WAAW,CAACI,QAAQ,CAACK;IACvC,CAAC;EACH,CAAC;EACDU,gBAAgB,EAAE;IAChBC,SAAS,EAAE,QAAQ;IACnBrB,UAAU,EAAEjB,KAAK,CAACkB,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;MAC5CC,MAAM,EAAEpB,KAAK,CAACkB,WAAW,CAACE,MAAM,CAACC,KAAK;MACtCC,QAAQ,EAAEtB,KAAK,CAACkB,WAAW,CAACI,QAAQ,CAACC;IACvC,CAAC,CAAC;IACFG,KAAK,EAAE1B,KAAK,CAACuC,OAAO,CAAC,CAAC,CAAC;IACvB,CAACvC,KAAK,CAACI,WAAW,CAACoC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC5Bd,KAAK,EAAE1B,KAAK,CAACuC,OAAO,CAAC,CAAC;IACxB;EACF,CAAC;EACDE,YAAY,EAAE;IACZ7B,SAAS,EAAE;EACb,CAAC;EACD8B,OAAO,EAAE;IACPC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,MAAM;IAChB,GAAG5C,KAAK,CAAC6C;EACX,CAAC;EACDC,SAAS,EAAE;IACTC,UAAU,EAAE/C,KAAK,CAACuC,OAAO,CAAC,CAAC,CAAC;IAC5BS,aAAa,EAAEhD,KAAK,CAACuC,OAAO,CAAC,CAAC;EAChC,CAAC;EACDU,KAAK,EAAE;IACLtC,OAAO,EAAEX,KAAK,CAACuC,OAAO,CAAC,CAAC,CAAC;IACzBrC,OAAO,EAAE,MAAM;IACf0C,QAAQ,EAAE,MAAM;IAChBM,aAAa,EAAE;EACjB,CAAC;EACDC,mBAAmB,EAAE;IACnBR,IAAI,EAAE,CAAC;IACPhC,OAAO,EAAEX,KAAK,CAACuC,OAAO,CAAC,CAAC,CAAC;IACzBa,SAAS,EAAE,QAAQ;IACnB,GAAGpD,KAAK,CAAC6C;EACX;AACF,CAAC,CAAC,CAAC;AAEH,MAAMQ,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACvC,MAAMC,OAAO,GAAGxD,SAAS,CAAC,CAAC;EAC3B,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8F,QAAQ,EAAEC,WAAW,CAAC,GAAG/F,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACgG,QAAQ,EAAEC,WAAW,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM;IAAEkG,YAAY;IAAEC;EAAQ,CAAC,GAAGlG,UAAU,CAACyB,WAAW,CAAC;EACzD,MAAM,CAAC0E,UAAU,EAAEC,aAAa,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsG,aAAa,EAAEC,gBAAgB,CAAC,GAAGvG,QAAQ,CAAC,WAAW,CAAC;EAC/D,MAAM;IAAEwG;EAAK,CAAC,GAAGvG,UAAU,CAACyB,WAAW,CAAC;EAExC,MAAMU,KAAK,GAAGtB,QAAQ,CAAC,CAAC;EACxB,MAAM2F,aAAa,GAAG1F,aAAa,CAACqB,KAAK,CAACI,WAAW,CAACoC,EAAE,CAAC,IAAI,CAAC,CAAC;EAE/D1E,SAAS,CAAC,MAAM;IACd,IAAIwG,QAAQ,CAACC,IAAI,CAACC,WAAW,GAAG,GAAG,EAAE;MACnCP,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAENnG,SAAS,CAAC,MAAM;IACd,IAAIwG,QAAQ,CAACC,IAAI,CAACC,WAAW,GAAG,GAAG,EAAE;MACnCL,gBAAgB,CAAC,WAAW,CAAC;IAC/B,CAAC,MAAM;MACLA,gBAAgB,CAAC,WAAW,CAAC;IAC/B;EACF,CAAC,EAAE,CAACH,UAAU,CAAC,CAAC;EAEhBlG,SAAS,CAAC,MAAM;IACd,MAAM2G,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,MAAM,GAAGF,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAE7C,MAAME,MAAM,GAAGjF,gBAAgB,CAAC;MAAE6E;IAAU,CAAC,CAAC;IAE9CI,MAAM,CAACC,EAAE,CAAC,WAAWL,SAAS,OAAO,EAAGM,IAAI,IAAK;MAC/C,IAAIA,IAAI,CAACX,IAAI,CAACY,EAAE,KAAK,CAACJ,MAAM,EAAE;QAC5BnF,UAAU,CAAC,6CAA6C,CAAC;QACzDwF,UAAU,CAAC,MAAM;UACfP,YAAY,CAACQ,KAAK,CAAC,CAAC;UACpBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC;IAEFR,MAAM,CAACS,IAAI,CAAC,YAAY,CAAC;IACzB,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCX,MAAM,CAACS,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC,EAAE,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;IAEjB,OAAO,MAAM;MACXT,MAAM,CAACY,UAAU,CAAC,CAAC;MACnBC,aAAa,CAACH,QAAQ,CAAC;IACzB,CAAC;IACD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,UAAU,GAAIC,KAAK,IAAK;IAC5BjC,WAAW,CAACiC,KAAK,CAACC,aAAa,CAAC;IAChChC,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMiC,eAAe,GAAGA,CAAA,KAAM;IAC5BnC,WAAW,CAAC,IAAI,CAAC;IACjBE,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMkC,mBAAmB,GAAGA,CAAA,KAAM;IAChCtC,gBAAgB,CAAC,IAAI,CAAC;IACtBqC,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9BF,eAAe,CAAC,CAAC;IACjBhC,YAAY,CAAC,CAAC;EAChB,CAAC;EAED,MAAMmC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI3B,QAAQ,CAACC,IAAI,CAACC,WAAW,GAAG,GAAG,EAAE;MACnCP,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,IAAIF,OAAO,EAAE;IACX,oBAAOpG,KAAA,CAAAuI,aAAA,CAAC3G,eAAe;MAAA4G,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;EAC5B;EAGA,oBACE7I,KAAA,CAAAuI,aAAA;IAAKO,SAAS,EAAElD,OAAO,CAACtD,IAAK;IAAAkG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3B7I,KAAA,CAAAuI,aAAA,CAACjI,MAAM;IACLyI,OAAO,EAAExC,aAAc;IACvBuC,SAAS,EAAEzC,UAAU,GAAGT,OAAO,CAACrB,WAAW,GAAGqB,OAAO,CAAClB,gBAAiB;IACvEkB,OAAO,EAAE;MACPN,KAAK,EAAElF,IAAI,CACTwF,OAAO,CAACrB,WAAW,EACnB,CAAC8B,UAAU,IAAIT,OAAO,CAAClB,gBACzB;IACF,CAAE;IACFsE,IAAI,EAAE3C,UAAW;IAAAmC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEjB7I,KAAA,CAAAuI,aAAA;IAAKO,SAAS,EAAElD,OAAO,CAAC/C,WAAY;IAAA2F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClC7I,KAAA,CAAAuI,aAAA;IAAKU,GAAG,EAAEjH,IAAK;IAACkH,KAAK,EAAE;MAAEC,MAAM,EAAE,QAAQ;MAAE3G,MAAM,EAAE,MAAM;MAAEuB,KAAK,EAAE,KAAK;MAACqF,SAAS,EAAE;IAAS,CAAE;IAACC,GAAG,EAAC,MAAM;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC5G7I,KAAA,CAAAuI,aAAA,CAAC1H,UAAU;IAACyI,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAACD,UAAU,CAAE;IAAAmC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpD7I,KAAA,CAAAuI,aAAA,CAACnH,eAAe;IAAAoH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACR,CACT,CAAC,eACN7I,KAAA,CAAAuI,aAAA,CAAC5H,OAAO;IAAA6H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACX7I,KAAA,CAAAuI,aAAA,CAAC9H,IAAI;IAACqI,SAAS,EAAElD,OAAO,CAACJ,mBAAoB;IAAAgD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3C7I,KAAA,CAAAuI,aAAA,CAAC/G,aAAa;IAAC8G,WAAW,EAAEA,WAAY;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACtC,CAAC,eACP7I,KAAA,CAAAuI,aAAA,CAAC5H,OAAO;IAAA6H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACJ,CAAC,eACT7I,KAAA,CAAAuI,aAAA,CAAC7G,SAAS;IACRsH,IAAI,EAAEnD,aAAc;IACpB0D,OAAO,EAAEA,CAAA,KAAMzD,gBAAgB,CAAC,KAAK,CAAE;IACvCmB,MAAM,EAAER,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,EAAG;IAAAmB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAClB,CAAC,eACF7I,KAAA,CAAAuI,aAAA,CAAChI,MAAM;IACLiE,QAAQ,EAAC,UAAU;IACnBsE,SAAS,EAAE1I,IAAI,CAACwF,OAAO,CAACzC,MAAM,EAAEkD,UAAU,IAAIT,OAAO,CAAC/B,WAAW,CAAE;IACnE2F,KAAK,EAAC,SAAS;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEf7I,KAAA,CAAAuI,aAAA,CAAC/H,OAAO;IAACuI,OAAO,EAAC,OAAO;IAACD,SAAS,EAAElD,OAAO,CAACjD,OAAQ;IAAA6F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClD7I,KAAA,CAAAuI,aAAA,CAAC1H,UAAU;IACT4I,IAAI,EAAC,OAAO;IACZV,OAAO,EAAC,WAAW;IACnB,cAAW,aAAa;IACxBO,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAACD,UAAU,CAAE;IAC1CyC,SAAS,EAAE1I,IAAI,CACbwF,OAAO,CAAC3B,UAAU,EAClBoC,UAAU,IAAIT,OAAO,CAACzB,gBACxB,CAAE;IAAAqE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF7I,KAAA,CAAAuI,aAAA,CAACpH,QAAQ;IAAAqH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACD,CAAC,eACb7I,KAAA,CAAAuI,aAAA,CAAC7H,UAAU;IACTgJ,SAAS,EAAC,IAAI;IACdX,OAAO,EAAC,IAAI;IACZS,KAAK,EAAC,SAAS;IACfG,MAAM;IACNb,SAAS,EAAElD,OAAO,CAACxB,KAAM;IAAAoE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAExBnC,aAAa,gBACZ1G,KAAA,CAAAuI,aAAA,CAAAvI,KAAA,CAAA4J,QAAA,QAAE,SACI,eAAA5J,KAAA,CAAAuI,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAIpC,IAAI,CAACoD,IAAQ,CAAC,qBACtB,CAAC,GAEHpD,IAAI,CAACoD,IAEG,CAAC,eACb7J,KAAA,CAAAuI,aAAA,CAACrH,KAAK;IAAAsH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAEL,CAAC,EACDpC,IAAI,CAACY,EAAE,iBAAIrH,KAAA,CAAAuI,aAAA,CAAC9G,oBAAoB;IAAA+G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAEpC7I,KAAA,CAAAuI,aAAA,CAACxG,oBAAoB;IAAAyG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAExB7I,KAAA,CAAAuI,aAAA,CAACrG,WAAW;IAAAsG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAEf7I,KAAA,CAAAuI,aAAA;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE7I,KAAA,CAAAuI,aAAA,CAAC1H,UAAU;IACT,cAAW,yBAAyB;IACpC,iBAAc,aAAa;IAC3B,iBAAc,MAAM;IACpByI,OAAO,EAAEtB,UAAW;IACpBe,OAAO,EAAC,WAAW;IAAAP,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAGnB7I,KAAA,CAAAuI,aAAA,CAAClH,aAAa;IAAAmH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACN,CAAC,eACb7I,KAAA,CAAAuI,aAAA,CAACzH,IAAI;IACHuG,EAAE,EAAC,aAAa;IAChBtB,QAAQ,EAAEA,QAAS;IACnB+D,kBAAkB,EAAE,IAAK;IACzBC,YAAY,EAAE;MACZC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE;IACd,CAAE;IACFC,eAAe,EAAE;MACfF,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE;IACd,CAAE;IACFjB,IAAI,EAAE/C,QAAS;IACfsD,OAAO,EAAEpB,eAAgB;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzB7I,KAAA,CAAAuI,aAAA,CAAC3H,QAAQ;IAAC0I,OAAO,EAAElB,mBAAoB;IAAAI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpChH,IAAI,CAACsI,CAAC,CAAC,gCAAgC,CAChC,CAEN,CACH,CACE,CACH,CAAC,eACTnK,KAAA,CAAAuI,aAAA;IAAMO,SAAS,EAAElD,OAAO,CAACb,OAAQ;IAAAyD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B7I,KAAA,CAAAuI,aAAA;IAAKO,SAAS,EAAElD,OAAO,CAACd,YAAa;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,EAEvClD,QAAQ,GAAGA,QAAQ,GAAG,IACnB,CACH,CAAC;AAEV,CAAC;AAED,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}