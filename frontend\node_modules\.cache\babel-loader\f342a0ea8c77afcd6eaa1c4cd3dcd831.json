{"ast": null, "code": "import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\nimport * as color from '../../helpers/color';\nimport { ColorWrap, EditableInput, Checkboard } from '../common';\nimport BlockSwatches from './BlockSwatches';\nexport var Block = function Block(_ref) {\n  var onChange = _ref.onChange,\n    onSwatchHover = _ref.onSwatchHover,\n    hex = _ref.hex,\n    colors = _ref.colors,\n    width = _ref.width,\n    triangle = _ref.triangle,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var transparent = hex === 'transparent';\n  var handleChange = function handleChange(hexCode, e) {\n    color.isValidHex(hexCode) && onChange({\n      hex: hexCode,\n      source: 'hex'\n    }, e);\n  };\n  var styles = reactCSS(merge({\n    'default': {\n      card: {\n        width: width,\n        background: '#fff',\n        boxShadow: '0 1px rgba(0,0,0,.1)',\n        borderRadius: '6px',\n        position: 'relative'\n      },\n      head: {\n        height: '110px',\n        background: hex,\n        borderRadius: '6px 6px 0 0',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        position: 'relative'\n      },\n      body: {\n        padding: '10px'\n      },\n      label: {\n        fontSize: '18px',\n        color: color.getContrastingColor(hex),\n        position: 'relative'\n      },\n      triangle: {\n        width: '0px',\n        height: '0px',\n        borderStyle: 'solid',\n        borderWidth: '0 10px 10px 10px',\n        borderColor: 'transparent transparent ' + hex + ' transparent',\n        position: 'absolute',\n        top: '-10px',\n        left: '50%',\n        marginLeft: '-10px'\n      },\n      input: {\n        width: '100%',\n        fontSize: '12px',\n        color: '#666',\n        border: '0px',\n        outline: 'none',\n        height: '22px',\n        boxShadow: 'inset 0 0 0 1px #ddd',\n        borderRadius: '4px',\n        padding: '0 7px',\n        boxSizing: 'border-box'\n      }\n    },\n    'hide-triangle': {\n      triangle: {\n        display: 'none'\n      }\n    }\n  }, passedStyles), {\n    'hide-triangle': triangle === 'hide'\n  });\n  return React.createElement('div', {\n    style: styles.card,\n    className: 'block-picker ' + className\n  }, React.createElement('div', {\n    style: styles.triangle\n  }), React.createElement('div', {\n    style: styles.head\n  }, transparent && React.createElement(Checkboard, {\n    borderRadius: '6px 6px 0 0'\n  }), React.createElement('div', {\n    style: styles.label\n  }, hex)), React.createElement('div', {\n    style: styles.body\n  }, React.createElement(BlockSwatches, {\n    colors: colors,\n    onClick: handleChange,\n    onSwatchHover: onSwatchHover\n  }), React.createElement(EditableInput, {\n    style: {\n      input: styles.input\n    },\n    value: hex,\n    onChange: handleChange\n  })));\n};\nBlock.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  colors: PropTypes.arrayOf(PropTypes.string),\n  triangle: PropTypes.oneOf(['top', 'hide']),\n  styles: PropTypes.object\n};\nBlock.defaultProps = {\n  width: 170,\n  colors: ['#D9E3F0', '#F47373', '#697689', '#37D67A', '#2CCCE4', '#555555', '#dce775', '#ff8a65', '#ba68c8'],\n  triangle: 'top',\n  styles: {}\n};\nexport default ColorWrap(Block);", "map": {"version": 3, "names": ["React", "PropTypes", "reactCSS", "merge", "color", "ColorWrap", "EditableInput", "Checkboard", "BlockSwatches", "Block", "_ref", "onChange", "onSwatchHover", "hex", "colors", "width", "triangle", "_ref$styles", "styles", "passedStyles", "undefined", "_ref$className", "className", "transparent", "handleChange", "hexCode", "e", "isValidHex", "source", "card", "background", "boxShadow", "borderRadius", "position", "head", "height", "display", "alignItems", "justifyContent", "body", "padding", "label", "fontSize", "getContrastingColor", "borderStyle", "borderWidth", "borderColor", "top", "left", "marginLeft", "input", "border", "outline", "boxSizing", "createElement", "style", "onClick", "value", "propTypes", "oneOfType", "string", "number", "arrayOf", "oneOf", "object", "defaultProps"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/block/Block.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\nimport * as color from '../../helpers/color';\n\nimport { ColorWrap, EditableInput, Checkboard } from '../common';\nimport BlockSwatches from './BlockSwatches';\n\nexport var Block = function Block(_ref) {\n  var onChange = _ref.onChange,\n      onSwatchHover = _ref.onSwatchHover,\n      hex = _ref.hex,\n      colors = _ref.colors,\n      width = _ref.width,\n      triangle = _ref.triangle,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var transparent = hex === 'transparent';\n  var handleChange = function handleChange(hexCode, e) {\n    color.isValidHex(hexCode) && onChange({\n      hex: hexCode,\n      source: 'hex'\n    }, e);\n  };\n\n  var styles = reactCSS(merge({\n    'default': {\n      card: {\n        width: width,\n        background: '#fff',\n        boxShadow: '0 1px rgba(0,0,0,.1)',\n        borderRadius: '6px',\n        position: 'relative'\n      },\n      head: {\n        height: '110px',\n        background: hex,\n        borderRadius: '6px 6px 0 0',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        position: 'relative'\n      },\n      body: {\n        padding: '10px'\n      },\n      label: {\n        fontSize: '18px',\n        color: color.getContrastingColor(hex),\n        position: 'relative'\n      },\n      triangle: {\n        width: '0px',\n        height: '0px',\n        borderStyle: 'solid',\n        borderWidth: '0 10px 10px 10px',\n        borderColor: 'transparent transparent ' + hex + ' transparent',\n        position: 'absolute',\n        top: '-10px',\n        left: '50%',\n        marginLeft: '-10px'\n      },\n      input: {\n        width: '100%',\n        fontSize: '12px',\n        color: '#666',\n        border: '0px',\n        outline: 'none',\n        height: '22px',\n        boxShadow: 'inset 0 0 0 1px #ddd',\n        borderRadius: '4px',\n        padding: '0 7px',\n        boxSizing: 'border-box'\n      }\n    },\n    'hide-triangle': {\n      triangle: {\n        display: 'none'\n      }\n    }\n  }, passedStyles), { 'hide-triangle': triangle === 'hide' });\n\n  return React.createElement(\n    'div',\n    { style: styles.card, className: 'block-picker ' + className },\n    React.createElement('div', { style: styles.triangle }),\n    React.createElement(\n      'div',\n      { style: styles.head },\n      transparent && React.createElement(Checkboard, { borderRadius: '6px 6px 0 0' }),\n      React.createElement(\n        'div',\n        { style: styles.label },\n        hex\n      )\n    ),\n    React.createElement(\n      'div',\n      { style: styles.body },\n      React.createElement(BlockSwatches, { colors: colors, onClick: handleChange, onSwatchHover: onSwatchHover }),\n      React.createElement(EditableInput, {\n        style: { input: styles.input },\n        value: hex,\n        onChange: handleChange\n      })\n    )\n  );\n};\n\nBlock.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  colors: PropTypes.arrayOf(PropTypes.string),\n  triangle: PropTypes.oneOf(['top', 'hide']),\n  styles: PropTypes.object\n};\n\nBlock.defaultProps = {\n  width: 170,\n  colors: ['#D9E3F0', '#F47373', '#697689', '#37D67A', '#2CCCE4', '#555555', '#dce775', '#ff8a65', '#ba68c8'],\n  triangle: 'top',\n  styles: {}\n};\n\nexport default ColorWrap(Block);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAO,KAAKC,KAAK,MAAM,qBAAqB;AAE5C,SAASC,SAAS,EAAEC,aAAa,EAAEC,UAAU,QAAQ,WAAW;AAChE,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,OAAO,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAE;EACtC,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,aAAa,GAAGF,IAAI,CAACE,aAAa;IAClCC,GAAG,GAAGH,IAAI,CAACG,GAAG;IACdC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,KAAK,GAAGL,IAAI,CAACK,KAAK;IAClBC,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IACxBC,WAAW,GAAGP,IAAI,CAACQ,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,cAAc,GAAGX,IAAI,CAACY,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKD,SAAS,GAAG,EAAE,GAAGC,cAAc;EAElE,IAAIE,WAAW,GAAGV,GAAG,KAAK,aAAa;EACvC,IAAIW,YAAY,GAAG,SAASA,YAAYA,CAACC,OAAO,EAAEC,CAAC,EAAE;IACnDtB,KAAK,CAACuB,UAAU,CAACF,OAAO,CAAC,IAAId,QAAQ,CAAC;MACpCE,GAAG,EAAEY,OAAO;MACZG,MAAM,EAAE;IACV,CAAC,EAAEF,CAAC,CAAC;EACP,CAAC;EAED,IAAIR,MAAM,GAAGhB,QAAQ,CAACC,KAAK,CAAC;IAC1B,SAAS,EAAE;MACT0B,IAAI,EAAE;QACJd,KAAK,EAAEA,KAAK;QACZe,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,sBAAsB;QACjCC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDC,IAAI,EAAE;QACJC,MAAM,EAAE,OAAO;QACfL,UAAU,EAAEjB,GAAG;QACfmB,YAAY,EAAE,aAAa;QAC3BI,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBL,QAAQ,EAAE;MACZ,CAAC;MACDM,IAAI,EAAE;QACJC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,QAAQ,EAAE,MAAM;QAChBtC,KAAK,EAAEA,KAAK,CAACuC,mBAAmB,CAAC9B,GAAG,CAAC;QACrCoB,QAAQ,EAAE;MACZ,CAAC;MACDjB,QAAQ,EAAE;QACRD,KAAK,EAAE,KAAK;QACZoB,MAAM,EAAE,KAAK;QACbS,WAAW,EAAE,OAAO;QACpBC,WAAW,EAAE,kBAAkB;QAC/BC,WAAW,EAAE,0BAA0B,GAAGjC,GAAG,GAAG,cAAc;QAC9DoB,QAAQ,EAAE,UAAU;QACpBc,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,KAAK;QACXC,UAAU,EAAE;MACd,CAAC;MACDC,KAAK,EAAE;QACLnC,KAAK,EAAE,MAAM;QACb2B,QAAQ,EAAE,MAAM;QAChBtC,KAAK,EAAE,MAAM;QACb+C,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,MAAM;QACfjB,MAAM,EAAE,MAAM;QACdJ,SAAS,EAAE,sBAAsB;QACjCC,YAAY,EAAE,KAAK;QACnBQ,OAAO,EAAE,OAAO;QAChBa,SAAS,EAAE;MACb;IACF,CAAC;IACD,eAAe,EAAE;MACfrC,QAAQ,EAAE;QACRoB,OAAO,EAAE;MACX;IACF;EACF,CAAC,EAAEjB,YAAY,CAAC,EAAE;IAAE,eAAe,EAAEH,QAAQ,KAAK;EAAO,CAAC,CAAC;EAE3D,OAAOhB,KAAK,CAACsD,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAErC,MAAM,CAACW,IAAI;IAAEP,SAAS,EAAE,eAAe,GAAGA;EAAU,CAAC,EAC9DtB,KAAK,CAACsD,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAErC,MAAM,CAACF;EAAS,CAAC,CAAC,EACtDhB,KAAK,CAACsD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAErC,MAAM,CAACgB;EAAK,CAAC,EACtBX,WAAW,IAAIvB,KAAK,CAACsD,aAAa,CAAC/C,UAAU,EAAE;IAAEyB,YAAY,EAAE;EAAc,CAAC,CAAC,EAC/EhC,KAAK,CAACsD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAErC,MAAM,CAACuB;EAAM,CAAC,EACvB5B,GACF,CACF,CAAC,EACDb,KAAK,CAACsD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAErC,MAAM,CAACqB;EAAK,CAAC,EACtBvC,KAAK,CAACsD,aAAa,CAAC9C,aAAa,EAAE;IAAEM,MAAM,EAAEA,MAAM;IAAE0C,OAAO,EAAEhC,YAAY;IAAEZ,aAAa,EAAEA;EAAc,CAAC,CAAC,EAC3GZ,KAAK,CAACsD,aAAa,CAAChD,aAAa,EAAE;IACjCiD,KAAK,EAAE;MAAEL,KAAK,EAAEhC,MAAM,CAACgC;IAAM,CAAC;IAC9BO,KAAK,EAAE5C,GAAG;IACVF,QAAQ,EAAEa;EACZ,CAAC,CACH,CACF,CAAC;AACH,CAAC;AAEDf,KAAK,CAACiD,SAAS,GAAG;EAChB3C,KAAK,EAAEd,SAAS,CAAC0D,SAAS,CAAC,CAAC1D,SAAS,CAAC2D,MAAM,EAAE3D,SAAS,CAAC4D,MAAM,CAAC,CAAC;EAChE/C,MAAM,EAAEb,SAAS,CAAC6D,OAAO,CAAC7D,SAAS,CAAC2D,MAAM,CAAC;EAC3C5C,QAAQ,EAAEf,SAAS,CAAC8D,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;EAC1C7C,MAAM,EAAEjB,SAAS,CAAC+D;AACpB,CAAC;AAEDvD,KAAK,CAACwD,YAAY,GAAG;EACnBlD,KAAK,EAAE,GAAG;EACVD,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAC3GE,QAAQ,EAAE,KAAK;EACfE,MAAM,EAAE,CAAC;AACX,CAAC;AAED,eAAeb,SAAS,CAACI,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}