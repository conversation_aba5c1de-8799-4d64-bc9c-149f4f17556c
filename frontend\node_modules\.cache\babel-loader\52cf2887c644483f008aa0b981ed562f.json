{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\components\\\\ModernCard\\\\index.js\";\nimport React from 'react';\nimport { makeStyles } from '@material-ui/core/styles';\nimport { Card, CardContent, CardHeader, Avatar, IconButton } from '@material-ui/core';\nimport { MoreVert } from '@material-ui/icons';\nconst useStyles = makeStyles(theme => ({\n  modernCard: {\n    borderRadius: 16,\n    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n    border: '1px solid rgba(0,0,0,0.04)',\n    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\n    '&:hover': {\n      transform: 'translateY(-4px)',\n      boxShadow: '0 12px 40px rgba(0,0,0,0.15)',\n      borderColor: theme.palette.primary.light\n    }\n  },\n  cardHeader: {\n    paddingBottom: 8,\n    '& .MuiCardHeader-title': {\n      fontSize: '1.1rem',\n      fontWeight: 600,\n      color: theme.palette.text.primary\n    },\n    '& .MuiCardHeader-subheader': {\n      fontSize: '0.875rem',\n      color: theme.palette.text.secondary\n    }\n  },\n  cardContent: {\n    paddingTop: 0,\n    '&:last-child': {\n      paddingBottom: 16\n    }\n  },\n  avatar: {\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    width: 40,\n    height: 40\n  },\n  actionButton: {\n    color: theme.palette.text.secondary,\n    '&:hover': {\n      backgroundColor: 'rgba(102, 126, 234, 0.08)',\n      color: theme.palette.primary.main\n    }\n  }\n}));\nconst ModernCard = ({\n  title,\n  subtitle,\n  avatar,\n  children,\n  action,\n  onClick,\n  ...props\n}) => {\n  const classes = useStyles();\n  return /*#__PURE__*/React.createElement(Card, Object.assign({\n    className: classes.modernCard,\n    onClick: onClick\n  }, props, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 5\n    }\n  }), (title || subtitle || avatar || action) && /*#__PURE__*/React.createElement(CardHeader, {\n    className: classes.cardHeader,\n    avatar: avatar && /*#__PURE__*/React.createElement(Avatar, {\n      className: classes.avatar,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 13\n      }\n    }, avatar),\n    action: action && /*#__PURE__*/React.createElement(IconButton, {\n      className: classes.actionButton,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 13\n      }\n    }, action || /*#__PURE__*/React.createElement(MoreVert, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 26\n      }\n    })),\n    title: title,\n    subheader: subtitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 9\n    }\n  }), children && /*#__PURE__*/React.createElement(CardContent, {\n    className: classes.cardContent,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 9\n    }\n  }, children));\n};\nexport default ModernCard;", "map": {"version": 3, "names": ["React", "makeStyles", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "IconButton", "<PERSON><PERSON><PERSON>", "useStyles", "theme", "modernCard", "borderRadius", "boxShadow", "border", "transition", "background", "transform", "borderColor", "palette", "primary", "light", "<PERSON><PERSON><PERSON><PERSON>", "paddingBottom", "fontSize", "fontWeight", "color", "text", "secondary", "cardContent", "paddingTop", "avatar", "width", "height", "actionButton", "backgroundColor", "main", "ModernCard", "title", "subtitle", "children", "action", "onClick", "props", "classes", "createElement", "Object", "assign", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "subheader"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/components/ModernCard/index.js"], "sourcesContent": ["import React from 'react';\nimport { makeStyles } from '@material-ui/core/styles';\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, Avatar, IconButton } from '@material-ui/core';\nimport { MoreVert } from '@material-ui/icons';\n\nconst useStyles = makeStyles((theme) => ({\n  modernCard: {\n    borderRadius: 16,\n    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n    border: '1px solid rgba(0,0,0,0.04)',\n    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\n    '&:hover': {\n      transform: 'translateY(-4px)',\n      boxShadow: '0 12px 40px rgba(0,0,0,0.15)',\n      borderColor: theme.palette.primary.light,\n    }\n  },\n  cardHeader: {\n    paddingBottom: 8,\n    '& .MuiCardHeader-title': {\n      fontSize: '1.1rem',\n      fontWeight: 600,\n      color: theme.palette.text.primary,\n    },\n    '& .MuiCardHeader-subheader': {\n      fontSize: '0.875rem',\n      color: theme.palette.text.secondary,\n    }\n  },\n  cardContent: {\n    paddingTop: 0,\n    '&:last-child': {\n      paddingBottom: 16,\n    }\n  },\n  avatar: {\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    width: 40,\n    height: 40,\n  },\n  actionButton: {\n    color: theme.palette.text.secondary,\n    '&:hover': {\n      backgroundColor: 'rgba(102, 126, 234, 0.08)',\n      color: theme.palette.primary.main,\n    }\n  }\n}));\n\nconst ModernCard = ({ \n  title, \n  subtitle, \n  avatar, \n  children, \n  action,\n  onClick,\n  ...props \n}) => {\n  const classes = useStyles();\n\n  return (\n    <Card \n      className={classes.modernCard} \n      onClick={onClick}\n      {...props}\n    >\n      {(title || subtitle || avatar || action) && (\n        <CardHeader\n          className={classes.cardHeader}\n          avatar={avatar && (\n            <Avatar className={classes.avatar}>\n              {avatar}\n            </Avatar>\n          )}\n          action={action && (\n            <IconButton className={classes.actionButton}>\n              {action || <MoreVert />}\n            </IconButton>\n          )}\n          title={title}\n          subheader={subtitle}\n        />\n      )}\n      {children && (\n        <CardContent className={classes.cardContent}>\n          {children}\n        </CardContent>\n      )}\n    </Card>\n  );\n};\n\nexport default ModernCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,UAAU,QAAQ,mBAAmB;AACrF,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,MAAMC,SAAS,GAAGP,UAAU,CAAEQ,KAAK,KAAM;EACvCC,UAAU,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,6BAA6B;IACxCC,MAAM,EAAE,4BAA4B;IACpCC,UAAU,EAAE,uCAAuC;IACnDC,UAAU,EAAE,mDAAmD;IAC/D,SAAS,EAAE;MACTC,SAAS,EAAE,kBAAkB;MAC7BJ,SAAS,EAAE,8BAA8B;MACzCK,WAAW,EAAER,KAAK,CAACS,OAAO,CAACC,OAAO,CAACC;IACrC;EACF,CAAC;EACDC,UAAU,EAAE;IACVC,aAAa,EAAE,CAAC;IAChB,wBAAwB,EAAE;MACxBC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,GAAG;MACfC,KAAK,EAAEhB,KAAK,CAACS,OAAO,CAACQ,IAAI,CAACP;IAC5B,CAAC;IACD,4BAA4B,EAAE;MAC5BI,QAAQ,EAAE,UAAU;MACpBE,KAAK,EAAEhB,KAAK,CAACS,OAAO,CAACQ,IAAI,CAACC;IAC5B;EACF,CAAC;EACDC,WAAW,EAAE;IACXC,UAAU,EAAE,CAAC;IACb,cAAc,EAAE;MACdP,aAAa,EAAE;IACjB;EACF,CAAC;EACDQ,MAAM,EAAE;IACNf,UAAU,EAAE,mDAAmD;IAC/DgB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC;EACDC,YAAY,EAAE;IACZR,KAAK,EAAEhB,KAAK,CAACS,OAAO,CAACQ,IAAI,CAACC,SAAS;IACnC,SAAS,EAAE;MACTO,eAAe,EAAE,2BAA2B;MAC5CT,KAAK,EAAEhB,KAAK,CAACS,OAAO,CAACC,OAAO,CAACgB;IAC/B;EACF;AACF,CAAC,CAAC,CAAC;AAEH,MAAMC,UAAU,GAAGA,CAAC;EAClBC,KAAK;EACLC,QAAQ;EACRR,MAAM;EACNS,QAAQ;EACRC,MAAM;EACNC,OAAO;EACP,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAGnC,SAAS,CAAC,CAAC;EAE3B,oBACER,KAAA,CAAA4C,aAAA,CAAC1C,IAAI,EAAA2C,MAAA,CAAAC,MAAA;IACHC,SAAS,EAAEJ,OAAO,CAACjC,UAAW;IAC9B+B,OAAO,EAAEA;EAAQ,GACbC,KAAK;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,IAER,CAAChB,KAAK,IAAIC,QAAQ,IAAIR,MAAM,IAAIU,MAAM,kBACrCxC,KAAA,CAAA4C,aAAA,CAACxC,UAAU;IACT2C,SAAS,EAAEJ,OAAO,CAACtB,UAAW;IAC9BS,MAAM,EAAEA,MAAM,iBACZ9B,KAAA,CAAA4C,aAAA,CAACvC,MAAM;MAAC0C,SAAS,EAAEJ,OAAO,CAACb,MAAO;MAAAkB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC/BvB,MACK,CACR;IACFU,MAAM,EAAEA,MAAM,iBACZxC,KAAA,CAAA4C,aAAA,CAACtC,UAAU;MAACyC,SAAS,EAAEJ,OAAO,CAACV,YAAa;MAAAe,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACzCb,MAAM,iBAAIxC,KAAA,CAAA4C,aAAA,CAACrC,QAAQ;MAAAyC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CACZ,CACZ;IACFhB,KAAK,EAAEA,KAAM;IACbiB,SAAS,EAAEhB,QAAS;IAAAU,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACrB,CACF,EACAd,QAAQ,iBACPvC,KAAA,CAAA4C,aAAA,CAACzC,WAAW;IAAC4C,SAAS,EAAEJ,OAAO,CAACf,WAAY;IAAAoB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzCd,QACU,CAEX,CAAC;AAEX,CAAC;AAED,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}