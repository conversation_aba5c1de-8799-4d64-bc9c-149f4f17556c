{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M5 18.31l3-1.16V5.45L5 6.46zm11 .24l3-1.01V5.69l-3 1.17z\",\n  opacity: \".3\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM8 17.15l-3 1.16V6.46l3-1.01v11.7zm6 1.38l-4-1.4V5.47l4 1.4v11.66zm5-.99l-3 1.01V6.86l3-1.16v11.84z\"\n})), 'MapTwoTone');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "Fragment", "d", "opacity"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/MapTwoTone.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M5 18.31l3-1.16V5.45L5 6.46zm11 .24l3-1.01V5.69l-3 1.17z\",\n  opacity: \".3\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM8 17.15l-3 1.16V6.46l3-1.01v11.7zm6 1.38l-4-1.4V5.47l4 1.4v11.66zm5-.99l-3 1.01V6.86l3-1.16v11.84z\"\n})), 'MapTwoTone');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAACF,KAAK,CAACG,QAAQ,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC5HE,CAAC,EAAE,0DAA0D;EAC7DC,OAAO,EAAE;AACX,CAAC,CAAC,EAAE,aAAaL,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC3CE,CAAC,EAAE;AACL,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}