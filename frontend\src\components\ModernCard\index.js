import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { <PERSON>, Card<PERSON>ontent, CardHeader, Avatar, IconButton } from '@material-ui/core';
import { MoreVert } from '@material-ui/icons';

const useStyles = makeStyles((theme) => ({
  modernCard: {
    borderRadius: 16,
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    border: '1px solid rgba(0,0,0,0.04)',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
      borderColor: theme.palette.primary.light,
    }
  },
  cardHeader: {
    paddingBottom: 8,
    '& .MuiCardHeader-title': {
      fontSize: '1.1rem',
      fontWeight: 600,
      color: theme.palette.text.primary,
    },
    '& .MuiCardHeader-subheader': {
      fontSize: '0.875rem',
      color: theme.palette.text.secondary,
    }
  },
  cardContent: {
    paddingTop: 0,
    '&:last-child': {
      paddingBottom: 16,
    }
  },
  avatar: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    width: 40,
    height: 40,
  },
  actionButton: {
    color: theme.palette.text.secondary,
    '&:hover': {
      backgroundColor: 'rgba(102, 126, 234, 0.08)',
      color: theme.palette.primary.main,
    }
  }
}));

const ModernCard = ({ 
  title, 
  subtitle, 
  avatar, 
  children, 
  action,
  onClick,
  ...props 
}) => {
  const classes = useStyles();

  return (
    <Card 
      className={classes.modernCard} 
      onClick={onClick}
      {...props}
    >
      {(title || subtitle || avatar || action) && (
        <CardHeader
          className={classes.cardHeader}
          avatar={avatar && (
            <Avatar className={classes.avatar}>
              {avatar}
            </Avatar>
          )}
          action={action && (
            <IconButton className={classes.actionButton}>
              {action || <MoreVert />}
            </IconButton>
          )}
          title={title}
          subheader={subtitle}
        />
      )}
      {children && (
        <CardContent className={classes.cardContent}>
          {children}
        </CardContent>
      )}
    </Card>
  );
};

export default ModernCard;
