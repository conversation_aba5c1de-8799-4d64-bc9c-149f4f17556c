{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport NimbleEmoji from './emoji/nimble-emoji';\nvar NotFound = /*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(NotFound, _React$PureComponent);\n  function NotFound() {\n    _classCallCheck(this, NotFound);\n    return _possibleConstructorReturn(this, _getPrototypeOf(NotFound).apply(this, arguments));\n  }\n  _createClass(NotFound, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        data = _this$props.data,\n        emojiProps = _this$props.emojiProps,\n        i18n = _this$props.i18n,\n        notFound = _this$props.notFound,\n        notFoundEmoji = _this$props.notFoundEmoji;\n      var component = notFound && notFound() || React.createElement(\"div\", {\n        className: \"emoji-mart-no-results\"\n      }, NimbleEmoji(_objectSpread({\n        data: data\n      }, emojiProps, {\n        size: 38,\n        emoji: notFoundEmoji,\n        onOver: null,\n        onLeave: null,\n        onClick: null\n      })), React.createElement(\"div\", {\n        className: \"emoji-mart-no-results-label\"\n      }, i18n.notfound));\n      return component;\n    }\n  }]);\n  return NotFound;\n}(React.PureComponent);\nexport { NotFound as default };\nNotFound.propTypes\n/* remove-proptypes */ = {\n  notFound: PropTypes.func.isRequired,\n  emojiProps: PropTypes.object.isRequired\n};", "map": {"version": 3, "names": ["_defineProperty", "_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_inherits", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "React", "PropTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NotFound", "_React$PureComponent", "value", "render", "_this$props", "props", "data", "emojiProps", "i18n", "notFound", "notFoundEmoji", "component", "createElement", "className", "size", "emoji", "onOver", "onLeave", "onClick", "notfound", "PureComponent", "default", "propTypes", "func", "isRequired"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/emoji-mart/dist-es/components/not-found.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport NimbleEmoji from './emoji/nimble-emoji';\n\nvar NotFound =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(NotFound, _React$PureComponent);\n\n  function NotFound() {\n    _classCallCheck(this, NotFound);\n\n    return _possibleConstructorReturn(this, _getPrototypeOf(NotFound).apply(this, arguments));\n  }\n\n  _createClass(NotFound, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n          data = _this$props.data,\n          emojiProps = _this$props.emojiProps,\n          i18n = _this$props.i18n,\n          notFound = _this$props.notFound,\n          notFoundEmoji = _this$props.notFoundEmoji;\n      var component = notFound && notFound() || React.createElement(\"div\", {\n        className: \"emoji-mart-no-results\"\n      }, NimbleEmoji(_objectSpread({\n        data: data\n      }, emojiProps, {\n        size: 38,\n        emoji: notFoundEmoji,\n        onOver: null,\n        onLeave: null,\n        onClick: null\n      })), React.createElement(\"div\", {\n        className: \"emoji-mart-no-results-label\"\n      }, i18n.notfound));\n      return component;\n    }\n  }]);\n\n  return NotFound;\n}(React.PureComponent);\n\nexport { NotFound as default };\nNotFound.propTypes\n/* remove-proptypes */\n= {\n  notFound: PropTypes.func.isRequired,\n  emojiProps: PropTypes.object.isRequired\n};"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uCAAuC;AACnE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,OAAOC,0BAA0B,MAAM,kDAAkD;AACzF,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,SAAS,MAAM,iCAAiC;AAEvD,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAEI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC;IAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAEpV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAE1B,eAAe,CAACoB,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIhB,MAAM,CAACiB,yBAAyB,EAAE;MAAEjB,MAAM,CAACkB,gBAAgB,CAACR,MAAM,EAAEV,MAAM,CAACiB,yBAAyB,CAACH,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAElB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEhB,MAAM,CAACmB,cAAc,CAACT,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAErhB,OAAOU,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,WAAW,MAAM,sBAAsB;AAE9C,IAAIC,QAAQ,GACZ;AACA,UAAUC,oBAAoB,EAAE;EAC9B7B,SAAS,CAAC4B,QAAQ,EAAEC,oBAAoB,CAAC;EAEzC,SAASD,QAAQA,CAAA,EAAG;IAClBhC,eAAe,CAAC,IAAI,EAAEgC,QAAQ,CAAC;IAE/B,OAAO9B,0BAA0B,CAAC,IAAI,EAAEC,eAAe,CAAC6B,QAAQ,CAAC,CAACf,KAAK,CAAC,IAAI,EAAEI,SAAS,CAAC,CAAC;EAC3F;EAEApB,YAAY,CAAC+B,QAAQ,EAAE,CAAC;IACtBP,GAAG,EAAE,QAAQ;IACbS,KAAK,EAAE,SAASC,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW,GAAG,IAAI,CAACC,KAAK;QACxBC,IAAI,GAAGF,WAAW,CAACE,IAAI;QACvBC,UAAU,GAAGH,WAAW,CAACG,UAAU;QACnCC,IAAI,GAAGJ,WAAW,CAACI,IAAI;QACvBC,QAAQ,GAAGL,WAAW,CAACK,QAAQ;QAC/BC,aAAa,GAAGN,WAAW,CAACM,aAAa;MAC7C,IAAIC,SAAS,GAAGF,QAAQ,IAAIA,QAAQ,CAAC,CAAC,IAAIZ,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;QACnEC,SAAS,EAAE;MACb,CAAC,EAAEd,WAAW,CAACb,aAAa,CAAC;QAC3BoB,IAAI,EAAEA;MACR,CAAC,EAAEC,UAAU,EAAE;QACbO,IAAI,EAAE,EAAE;QACRC,KAAK,EAAEL,aAAa;QACpBM,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC,EAAErB,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;QAC9BC,SAAS,EAAE;MACb,CAAC,EAAEL,IAAI,CAACW,QAAQ,CAAC,CAAC;MAClB,OAAOR,SAAS;IAClB;EACF,CAAC,CAAC,CAAC;EAEH,OAAOX,QAAQ;AACjB,CAAC,CAACH,KAAK,CAACuB,aAAa,CAAC;AAEtB,SAASpB,QAAQ,IAAIqB,OAAO;AAC5BrB,QAAQ,CAACsB;AACT,yBACE;EACAb,QAAQ,EAAEX,SAAS,CAACyB,IAAI,CAACC,UAAU;EACnCjB,UAAU,EAAET,SAAS,CAACxB,MAAM,CAACkD;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}