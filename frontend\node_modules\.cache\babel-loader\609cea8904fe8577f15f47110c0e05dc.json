{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(\"path\", {\n  d: \"M22 4H2v16h20V4zM8.5 15H7.3l-2.55-3.5V15H3.5V9h1.25l2.5 3.5V9H8.5v6zm5-4.74H11v1.12h2.5v1.26H11v1.11h2.5V15h-4V9h4v1.26zm7 4.74h-6V9h1.25v4.51h1.13V9.99h1.25v3.51h1.12V9h1.25v6z\"\n}), 'FiberNewSharp');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "d"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/FiberNewSharp.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M22 4H2v16h20V4zM8.5 15H7.3l-2.55-3.5V15H3.5V9h1.25l2.5 3.5V9H8.5v6zm5-4.74H11v1.12h2.5v1.26H11v1.11h2.5V15h-4V9h4v1.26zm7 4.74h-6V9h1.25v4.51h1.13V9.99h1.25v3.51h1.12V9h1.25v6z\"\n}), 'FiberNewSharp');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EACrEC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}