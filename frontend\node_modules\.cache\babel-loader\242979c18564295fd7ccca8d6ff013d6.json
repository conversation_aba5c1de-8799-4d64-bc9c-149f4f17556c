{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M16.62 16h.76L19 12.76V8h-4v4h3.62zm-10 0h.76L9 12.76V8H5v4h3.62z\",\n  opacity: \".3\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M18.62 18L21 13.24V6h-8v8h2.38l-2 4h5.24zM15 12V8h4v4.76L17.38 16h-.76l2-4H15zM3.38 18h5.24L11 13.24V6H3v8h2.38l-2 4zM5 12V8h4v4.76L7.38 16h-.76l2-4H5z\"\n})), 'FormatQuoteTwoTone');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "Fragment", "d", "opacity"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/FormatQuoteTwoTone.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M16.62 16h.76L19 12.76V8h-4v4h3.62zm-10 0h.76L9 12.76V8H5v4h3.62z\",\n  opacity: \".3\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M18.62 18L21 13.24V6h-8v8h2.38l-2 4h5.24zM15 12V8h4v4.76L17.38 16h-.76l2-4H15zM3.38 18h5.24L11 13.24V6H3v8h2.38l-2 4zM5 12V8h4v4.76L7.38 16h-.76l2-4H5z\"\n})), 'FormatQuoteTwoTone');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAACF,KAAK,CAACG,QAAQ,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC5HE,CAAC,EAAE,mEAAmE;EACtEC,OAAO,EAAE;AACX,CAAC,CAAC,EAAE,aAAaL,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC3CE,CAAC,EAAE;AACL,CAAC,CAAC,CAAC,EAAE,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}