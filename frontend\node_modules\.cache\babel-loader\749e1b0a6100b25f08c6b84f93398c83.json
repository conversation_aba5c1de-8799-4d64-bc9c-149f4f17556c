{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\components\\\\ModernButton\\\\index.js\";\nimport React from 'react';\nimport { makeStyles } from '@material-ui/core/styles';\nimport { Button, CircularProgress } from '@material-ui/core';\nconst useStyles = makeStyles(theme => ({\n  primaryButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '10px 24px',\n    fontSize: '0.875rem',\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    color: 'white',\n    border: 'none',\n    boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)',\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n      transform: 'translateY(-1px)',\n      boxShadow: '0 4px 16px rgba(102, 126, 234, 0.4)'\n    },\n    '&:active': {\n      transform: 'translateY(0)'\n    },\n    '&:disabled': {\n      background: theme.palette.action.disabled,\n      color: theme.palette.action.disabled,\n      transform: 'none',\n      boxShadow: 'none'\n    }\n  },\n  secondaryButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '10px 24px',\n    fontSize: '0.875rem',\n    backgroundColor: 'white',\n    color: theme.palette.primary.main,\n    border: `1px solid ${theme.palette.primary.main}`,\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      backgroundColor: 'rgba(102, 126, 234, 0.08)',\n      borderColor: theme.palette.primary.dark,\n      transform: 'translateY(-1px)',\n      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.2)'\n    },\n    '&:active': {\n      transform: 'translateY(0)'\n    }\n  },\n  outlinedButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '10px 24px',\n    fontSize: '0.875rem',\n    backgroundColor: 'transparent',\n    color: theme.palette.text.primary,\n    border: '1px solid rgba(0,0,0,0.12)',\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      backgroundColor: 'rgba(0,0,0,0.04)',\n      borderColor: 'rgba(0,0,0,0.24)',\n      transform: 'translateY(-1px)',\n      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n    },\n    '&:active': {\n      transform: 'translateY(0)'\n    }\n  },\n  textButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '8px 16px',\n    fontSize: '0.875rem',\n    color: theme.palette.primary.main,\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      backgroundColor: 'rgba(102, 126, 234, 0.08)',\n      transform: 'translateY(-1px)'\n    },\n    '&:active': {\n      transform: 'translateY(0)'\n    }\n  },\n  dangerButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '10px 24px',\n    fontSize: '0.875rem',\n    background: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',\n    color: 'white',\n    border: 'none',\n    boxShadow: '0 2px 8px rgba(245, 101, 101, 0.3)',\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      background: 'linear-gradient(135deg, #e53e3e 0%, #c53030 100%)',\n      transform: 'translateY(-1px)',\n      boxShadow: '0 4px 16px rgba(245, 101, 101, 0.4)'\n    },\n    '&:active': {\n      transform: 'translateY(0)'\n    }\n  },\n  successButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '10px 24px',\n    fontSize: '0.875rem',\n    background: 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)',\n    color: 'white',\n    border: 'none',\n    boxShadow: '0 2px 8px rgba(72, 187, 120, 0.3)',\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      background: 'linear-gradient(135deg, #38a169 0%, #2f855a 100%)',\n      transform: 'translateY(-1px)',\n      boxShadow: '0 4px 16px rgba(72, 187, 120, 0.4)'\n    },\n    '&:active': {\n      transform: 'translateY(0)'\n    }\n  },\n  smallButton: {\n    padding: '6px 16px',\n    fontSize: '0.75rem',\n    minHeight: 32\n  },\n  largeButton: {\n    padding: '12px 32px',\n    fontSize: '1rem',\n    minHeight: 48\n  },\n  loadingSpinner: {\n    marginRight: theme.spacing(1),\n    color: 'inherit'\n  }\n}));\nconst ModernButton = ({\n  variant = 'primary',\n  size = 'medium',\n  loading = false,\n  children,\n  startIcon,\n  endIcon,\n  className,\n  ...props\n}) => {\n  const classes = useStyles();\n  const getButtonClass = () => {\n    let baseClass = '';\n    switch (variant) {\n      case 'primary':\n        baseClass = classes.primaryButton;\n        break;\n      case 'secondary':\n        baseClass = classes.secondaryButton;\n        break;\n      case 'outlined':\n        baseClass = classes.outlinedButton;\n        break;\n      case 'text':\n        baseClass = classes.textButton;\n        break;\n      case 'danger':\n        baseClass = classes.dangerButton;\n        break;\n      case 'success':\n        baseClass = classes.successButton;\n        break;\n      default:\n        baseClass = classes.primaryButton;\n    }\n    if (size === 'small') {\n      baseClass += ` ${classes.smallButton}`;\n    } else if (size === 'large') {\n      baseClass += ` ${classes.largeButton}`;\n    }\n    return baseClass;\n  };\n  return /*#__PURE__*/React.createElement(Button, Object.assign({\n    className: `${getButtonClass()} ${className || ''}`,\n    disabled: loading || props.disabled,\n    startIcon: loading ? /*#__PURE__*/React.createElement(CircularProgress, {\n      size: 16,\n      className: classes.loadingSpinner,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 28\n      }\n    }) : startIcon,\n    endIcon: !loading ? endIcon : null\n  }, props, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 5\n    }\n  }), children);\n};\nexport default ModernButton;", "map": {"version": 3, "names": ["React", "makeStyles", "<PERSON><PERSON>", "CircularProgress", "useStyles", "theme", "primaryButton", "borderRadius", "textTransform", "fontWeight", "padding", "fontSize", "background", "color", "border", "boxShadow", "transition", "transform", "palette", "action", "disabled", "secondaryButton", "backgroundColor", "primary", "main", "borderColor", "dark", "outlinedButton", "text", "textButton", "dangerButton", "successButton", "smallButton", "minHeight", "largeButton", "loadingSpinner", "marginRight", "spacing", "ModernButton", "variant", "size", "loading", "children", "startIcon", "endIcon", "className", "props", "classes", "getButtonClass", "baseClass", "createElement", "Object", "assign", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/components/ModernButton/index.js"], "sourcesContent": ["import React from 'react';\nimport { makeStyles } from '@material-ui/core/styles';\nimport { Button, CircularProgress } from '@material-ui/core';\n\nconst useStyles = makeStyles((theme) => ({\n  primaryButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '10px 24px',\n    fontSize: '0.875rem',\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    color: 'white',\n    border: 'none',\n    boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)',\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n      transform: 'translateY(-1px)',\n      boxShadow: '0 4px 16px rgba(102, 126, 234, 0.4)',\n    },\n    '&:active': {\n      transform: 'translateY(0)',\n    },\n    '&:disabled': {\n      background: theme.palette.action.disabled,\n      color: theme.palette.action.disabled,\n      transform: 'none',\n      boxShadow: 'none',\n    }\n  },\n  secondaryButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '10px 24px',\n    fontSize: '0.875rem',\n    backgroundColor: 'white',\n    color: theme.palette.primary.main,\n    border: `1px solid ${theme.palette.primary.main}`,\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      backgroundColor: 'rgba(102, 126, 234, 0.08)',\n      borderColor: theme.palette.primary.dark,\n      transform: 'translateY(-1px)',\n      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.2)',\n    },\n    '&:active': {\n      transform: 'translateY(0)',\n    }\n  },\n  outlinedButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '10px 24px',\n    fontSize: '0.875rem',\n    backgroundColor: 'transparent',\n    color: theme.palette.text.primary,\n    border: '1px solid rgba(0,0,0,0.12)',\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      backgroundColor: 'rgba(0,0,0,0.04)',\n      borderColor: 'rgba(0,0,0,0.24)',\n      transform: 'translateY(-1px)',\n      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n    },\n    '&:active': {\n      transform: 'translateY(0)',\n    }\n  },\n  textButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '8px 16px',\n    fontSize: '0.875rem',\n    color: theme.palette.primary.main,\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      backgroundColor: 'rgba(102, 126, 234, 0.08)',\n      transform: 'translateY(-1px)',\n    },\n    '&:active': {\n      transform: 'translateY(0)',\n    }\n  },\n  dangerButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '10px 24px',\n    fontSize: '0.875rem',\n    background: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',\n    color: 'white',\n    border: 'none',\n    boxShadow: '0 2px 8px rgba(245, 101, 101, 0.3)',\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      background: 'linear-gradient(135deg, #e53e3e 0%, #c53030 100%)',\n      transform: 'translateY(-1px)',\n      boxShadow: '0 4px 16px rgba(245, 101, 101, 0.4)',\n    },\n    '&:active': {\n      transform: 'translateY(0)',\n    }\n  },\n  successButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '10px 24px',\n    fontSize: '0.875rem',\n    background: 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)',\n    color: 'white',\n    border: 'none',\n    boxShadow: '0 2px 8px rgba(72, 187, 120, 0.3)',\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      background: 'linear-gradient(135deg, #38a169 0%, #2f855a 100%)',\n      transform: 'translateY(-1px)',\n      boxShadow: '0 4px 16px rgba(72, 187, 120, 0.4)',\n    },\n    '&:active': {\n      transform: 'translateY(0)',\n    }\n  },\n  smallButton: {\n    padding: '6px 16px',\n    fontSize: '0.75rem',\n    minHeight: 32,\n  },\n  largeButton: {\n    padding: '12px 32px',\n    fontSize: '1rem',\n    minHeight: 48,\n  },\n  loadingSpinner: {\n    marginRight: theme.spacing(1),\n    color: 'inherit',\n  }\n}));\n\nconst ModernButton = ({\n  variant = 'primary',\n  size = 'medium',\n  loading = false,\n  children,\n  startIcon,\n  endIcon,\n  className,\n  ...props\n}) => {\n  const classes = useStyles();\n\n  const getButtonClass = () => {\n    let baseClass = '';\n    \n    switch (variant) {\n      case 'primary':\n        baseClass = classes.primaryButton;\n        break;\n      case 'secondary':\n        baseClass = classes.secondaryButton;\n        break;\n      case 'outlined':\n        baseClass = classes.outlinedButton;\n        break;\n      case 'text':\n        baseClass = classes.textButton;\n        break;\n      case 'danger':\n        baseClass = classes.dangerButton;\n        break;\n      case 'success':\n        baseClass = classes.successButton;\n        break;\n      default:\n        baseClass = classes.primaryButton;\n    }\n\n    if (size === 'small') {\n      baseClass += ` ${classes.smallButton}`;\n    } else if (size === 'large') {\n      baseClass += ` ${classes.largeButton}`;\n    }\n\n    return baseClass;\n  };\n\n  return (\n    <Button\n      className={`${getButtonClass()} ${className || ''}`}\n      disabled={loading || props.disabled}\n      startIcon={loading ? <CircularProgress size={16} className={classes.loadingSpinner} /> : startIcon}\n      endIcon={!loading ? endIcon : null}\n      {...props}\n    >\n      {children}\n    </Button>\n  );\n};\n\nexport default ModernButton;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,MAAM,EAAEC,gBAAgB,QAAQ,mBAAmB;AAE5D,MAAMC,SAAS,GAAGH,UAAU,CAAEI,KAAK,KAAM;EACvCC,aAAa,EAAE;IACbC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,GAAG;IACfC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,mDAAmD;IAC/DC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,oCAAoC;IAC/CC,UAAU,EAAE,sBAAsB;IAClC,SAAS,EAAE;MACTJ,UAAU,EAAE,mDAAmD;MAC/DK,SAAS,EAAE,kBAAkB;MAC7BF,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVE,SAAS,EAAE;IACb,CAAC;IACD,YAAY,EAAE;MACZL,UAAU,EAAEP,KAAK,CAACa,OAAO,CAACC,MAAM,CAACC,QAAQ;MACzCP,KAAK,EAAER,KAAK,CAACa,OAAO,CAACC,MAAM,CAACC,QAAQ;MACpCH,SAAS,EAAE,MAAM;MACjBF,SAAS,EAAE;IACb;EACF,CAAC;EACDM,eAAe,EAAE;IACfd,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,GAAG;IACfC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,UAAU;IACpBW,eAAe,EAAE,OAAO;IACxBT,KAAK,EAAER,KAAK,CAACa,OAAO,CAACK,OAAO,CAACC,IAAI;IACjCV,MAAM,EAAE,aAAaT,KAAK,CAACa,OAAO,CAACK,OAAO,CAACC,IAAI,EAAE;IACjDR,UAAU,EAAE,sBAAsB;IAClC,SAAS,EAAE;MACTM,eAAe,EAAE,2BAA2B;MAC5CG,WAAW,EAAEpB,KAAK,CAACa,OAAO,CAACK,OAAO,CAACG,IAAI;MACvCT,SAAS,EAAE,kBAAkB;MAC7BF,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVE,SAAS,EAAE;IACb;EACF,CAAC;EACDU,cAAc,EAAE;IACdpB,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,GAAG;IACfC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,UAAU;IACpBW,eAAe,EAAE,aAAa;IAC9BT,KAAK,EAAER,KAAK,CAACa,OAAO,CAACU,IAAI,CAACL,OAAO;IACjCT,MAAM,EAAE,4BAA4B;IACpCE,UAAU,EAAE,sBAAsB;IAClC,SAAS,EAAE;MACTM,eAAe,EAAE,kBAAkB;MACnCG,WAAW,EAAE,kBAAkB;MAC/BR,SAAS,EAAE,kBAAkB;MAC7BF,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVE,SAAS,EAAE;IACb;EACF,CAAC;EACDY,UAAU,EAAE;IACVtB,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,GAAG;IACfC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE,UAAU;IACpBE,KAAK,EAAER,KAAK,CAACa,OAAO,CAACK,OAAO,CAACC,IAAI;IACjCR,UAAU,EAAE,sBAAsB;IAClC,SAAS,EAAE;MACTM,eAAe,EAAE,2BAA2B;MAC5CL,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVA,SAAS,EAAE;IACb;EACF,CAAC;EACDa,YAAY,EAAE;IACZvB,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,GAAG;IACfC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,mDAAmD;IAC/DC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,oCAAoC;IAC/CC,UAAU,EAAE,sBAAsB;IAClC,SAAS,EAAE;MACTJ,UAAU,EAAE,mDAAmD;MAC/DK,SAAS,EAAE,kBAAkB;MAC7BF,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVE,SAAS,EAAE;IACb;EACF,CAAC;EACDc,aAAa,EAAE;IACbxB,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,GAAG;IACfC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,mDAAmD;IAC/DC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,mCAAmC;IAC9CC,UAAU,EAAE,sBAAsB;IAClC,SAAS,EAAE;MACTJ,UAAU,EAAE,mDAAmD;MAC/DK,SAAS,EAAE,kBAAkB;MAC7BF,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVE,SAAS,EAAE;IACb;EACF,CAAC;EACDe,WAAW,EAAE;IACXtB,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE,SAAS;IACnBsB,SAAS,EAAE;EACb,CAAC;EACDC,WAAW,EAAE;IACXxB,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,MAAM;IAChBsB,SAAS,EAAE;EACb,CAAC;EACDE,cAAc,EAAE;IACdC,WAAW,EAAE/B,KAAK,CAACgC,OAAO,CAAC,CAAC,CAAC;IAC7BxB,KAAK,EAAE;EACT;AACF,CAAC,CAAC,CAAC;AAEH,MAAMyB,YAAY,GAAGA,CAAC;EACpBC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,QAAQ;EACfC,OAAO,GAAG,KAAK;EACfC,QAAQ;EACRC,SAAS;EACTC,OAAO;EACPC,SAAS;EACT,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAG3C,SAAS,CAAC,CAAC;EAE3B,MAAM4C,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIC,SAAS,GAAG,EAAE;IAElB,QAAQV,OAAO;MACb,KAAK,SAAS;QACZU,SAAS,GAAGF,OAAO,CAACzC,aAAa;QACjC;MACF,KAAK,WAAW;QACd2C,SAAS,GAAGF,OAAO,CAAC1B,eAAe;QACnC;MACF,KAAK,UAAU;QACb4B,SAAS,GAAGF,OAAO,CAACpB,cAAc;QAClC;MACF,KAAK,MAAM;QACTsB,SAAS,GAAGF,OAAO,CAAClB,UAAU;QAC9B;MACF,KAAK,QAAQ;QACXoB,SAAS,GAAGF,OAAO,CAACjB,YAAY;QAChC;MACF,KAAK,SAAS;QACZmB,SAAS,GAAGF,OAAO,CAAChB,aAAa;QACjC;MACF;QACEkB,SAAS,GAAGF,OAAO,CAACzC,aAAa;IACrC;IAEA,IAAIkC,IAAI,KAAK,OAAO,EAAE;MACpBS,SAAS,IAAI,IAAIF,OAAO,CAACf,WAAW,EAAE;IACxC,CAAC,MAAM,IAAIQ,IAAI,KAAK,OAAO,EAAE;MAC3BS,SAAS,IAAI,IAAIF,OAAO,CAACb,WAAW,EAAE;IACxC;IAEA,OAAOe,SAAS;EAClB,CAAC;EAED,oBACEjD,KAAA,CAAAkD,aAAA,CAAChD,MAAM,EAAAiD,MAAA,CAAAC,MAAA;IACLP,SAAS,EAAE,GAAGG,cAAc,CAAC,CAAC,IAAIH,SAAS,IAAI,EAAE,EAAG;IACpDzB,QAAQ,EAAEqB,OAAO,IAAIK,KAAK,CAAC1B,QAAS;IACpCuB,SAAS,EAAEF,OAAO,gBAAGzC,KAAA,CAAAkD,aAAA,CAAC/C,gBAAgB;MAACqC,IAAI,EAAE,EAAG;MAACK,SAAS,EAAEE,OAAO,CAACZ,cAAe;MAAAkB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC,GAAGf,SAAU;IACnGC,OAAO,EAAE,CAACH,OAAO,GAAGG,OAAO,GAAG;EAAK,GAC/BE,KAAK;IAAAO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,IAERhB,QACK,CAAC;AAEb,CAAC;AAED,eAAeJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}