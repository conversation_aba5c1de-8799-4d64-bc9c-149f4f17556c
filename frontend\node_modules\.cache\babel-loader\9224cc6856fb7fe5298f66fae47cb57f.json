{"ast": null, "code": "import React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  fillOpacity: \".3\",\n  d: \"M2 22h20V2L2 22z\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M17 7L2 22h15V7z\"\n})), 'NetworkCellOutlined');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "Fragment", "fillOpacity", "d"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/NetworkCellOutlined.js"], "sourcesContent": ["import React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  fillOpacity: \".3\",\n  d: \"M2 22h20V2L2 22z\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M17 7L2 22h15V7z\"\n})), 'NetworkCellOutlined');"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAACF,KAAK,CAACG,QAAQ,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC5HE,WAAW,EAAE,IAAI;EACjBC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,aAAaL,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC3CG,CAAC,EAAE;AACL,CAAC,CAAC,CAAC,EAAE,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}