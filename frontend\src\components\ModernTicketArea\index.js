import React, { useState } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Button,
  Chip,
  IconButton,
  InputBase,
  Paper,
  Divider
} from '@material-ui/core';
import {
  Inbox,
  CheckCircle,
  Search,
  Add,
  FilterList,
  Refresh
} from '@material-ui/icons';

const useStyles = makeStyles((theme) => ({
  container: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: theme.palette.background.default,
  },
  header: {
    padding: '20px 24px',
    backgroundColor: 'white',
    borderBottom: '1px solid rgba(0,0,0,0.06)',
    boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
  },
  headerTop: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: '1.5rem',
    fontWeight: 700,
    color: theme.palette.text.primary,
  },
  actionButtons: {
    display: 'flex',
    gap: 12,
  },
  primaryButton: {
    borderRadius: 8,
    textTransform: 'none',
    fontWeight: 600,
    padding: '8px 20px',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    '&:hover': {
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
    }
  },
  secondaryButton: {
    borderRadius: 8,
    textTransform: 'none',
    fontWeight: 600,
    padding: '8px 16px',
    color: theme.palette.text.secondary,
    border: '1px solid rgba(0,0,0,0.12)',
    '&:hover': {
      backgroundColor: 'rgba(102, 126, 234, 0.04)',
      borderColor: theme.palette.primary.main,
    }
  },
  searchContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: 16,
  },
  searchBox: {
    display: 'flex',
    alignItems: 'center',
    backgroundColor: theme.palette.background.default,
    borderRadius: 12,
    padding: '8px 16px',
    border: '1px solid rgba(0,0,0,0.08)',
    minWidth: 300,
    '&:hover': {
      borderColor: 'rgba(0,0,0,0.12)',
    },
    '&:focus-within': {
      borderColor: theme.palette.primary.main,
      boxShadow: `0 0 0 3px ${theme.palette.primary.main}20`,
    }
  },
  searchInput: {
    marginLeft: 8,
    flex: 1,
    fontSize: '0.875rem',
  },
  filterButton: {
    color: theme.palette.text.secondary,
    '&:hover': {
      backgroundColor: 'rgba(102, 126, 234, 0.08)',
      color: theme.palette.primary.main,
    }
  },
  tabsContainer: {
    backgroundColor: 'white',
    borderBottom: '1px solid rgba(0,0,0,0.06)',
  },
  tabs: {
    paddingLeft: 24,
    '& .MuiTabs-indicator': {
      height: 3,
      borderRadius: '3px 3px 0 0',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    }
  },
  tab: {
    textTransform: 'none',
    fontWeight: 600,
    fontSize: '0.875rem',
    minHeight: 56,
    padding: '12px 20px',
    '&.Mui-selected': {
      color: theme.palette.primary.main,
    }
  },
  tabIcon: {
    marginRight: 8,
    fontSize: '1.125rem',
  },
  content: {
    flex: 1,
    padding: 24,
    overflow: 'auto',
    ...theme.scrollbarStyles,
  },
  emptyState: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    textAlign: 'center',
    color: theme.palette.text.secondary,
  },
  emptyIcon: {
    fontSize: '4rem',
    marginBottom: 16,
    opacity: 0.3,
  },
  emptyTitle: {
    fontSize: '1.25rem',
    fontWeight: 600,
    marginBottom: 8,
    color: theme.palette.text.primary,
  },
  emptyDescription: {
    fontSize: '0.875rem',
    maxWidth: 400,
    lineHeight: 1.6,
  },
  statusChip: {
    marginLeft: 8,
    fontSize: '0.75rem',
    height: 24,
  }
}));

const ModernTicketArea = ({
  onNewTicket,
  onRefresh,
  onSearch,
  children
}) => {
  const classes = useStyles();
  const [activeTab, setActiveTab] = useState(0);
  const [searchValue, setSearchValue] = useState('');

  const tabs = [
    { label: 'Abertas', icon: <Inbox className={classes.tabIcon} />, count: 5 },
    { label: 'Resolvidas', icon: <CheckCircle className={classes.tabIcon} />, count: 12 },
    { label: 'Busca', icon: <Search className={classes.tabIcon} /> }
  ];

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleSearch = (event) => {
    setSearchValue(event.target.value);
    if (onSearch) {
      onSearch(event.target.value);
    }
  };

  return (
    <Box className={classes.container}>
      {/* Header */}
      <Box className={classes.header}>
        <Box className={classes.headerTop}>
          <Typography className={classes.title}>
            Atendimentos
          </Typography>
          <Box className={classes.actionButtons}>
            <Button
              variant="outlined"
              className={classes.secondaryButton}
              startIcon={<Refresh />}
              onClick={onRefresh}
            >
              Atualizar
            </Button>
            <Button
              variant="contained"
              className={classes.primaryButton}
              startIcon={<Add />}
              onClick={onNewTicket}
            >
              Novo Ticket
            </Button>
          </Box>
        </Box>

        <Box className={classes.searchContainer}>
          <Paper className={classes.searchBox} elevation={0}>
            <Search color="action" />
            <InputBase
              className={classes.searchInput}
              placeholder="Buscar tickets, contatos..."
              value={searchValue}
              onChange={handleSearch}
            />
          </Paper>
          <IconButton className={classes.filterButton}>
            <FilterList />
          </IconButton>
        </Box>
      </Box>

      {/* Tabs */}
      <Box className={classes.tabsContainer}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          className={classes.tabs}
          indicatorColor="primary"
          textColor="primary"
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              className={classes.tab}
              label={
                <Box display="flex" alignItems="center">
                  {tab.icon}
                  {tab.label}
                  {tab.count && (
                    <Chip
                      size="small"
                      label={tab.count}
                      className={classes.statusChip}
                      color={index === 0 ? "primary" : "default"}
                    />
                  )}
                </Box>
              }
            />
          ))}
        </Tabs>
      </Box>

      {/* Content */}
      <Box className={classes.content}>
        {children || (
          <Box className={classes.emptyState}>
            <Inbox className={classes.emptyIcon} />
            <Typography className={classes.emptyTitle}>
              Nenhum atendimento encontrado
            </Typography>
            <Typography className={classes.emptyDescription}>
              Nenhum atendimento encontrado com esse status ou termo pesquisado.
              Selecione um ticket para começar a conversar.
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ModernTicketArea;
