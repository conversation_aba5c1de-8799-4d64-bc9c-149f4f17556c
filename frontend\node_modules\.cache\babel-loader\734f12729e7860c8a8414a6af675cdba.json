{"ast": null, "code": "import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport map from 'lodash-es/map';\nimport merge from 'lodash-es/merge';\nimport { ColorWrap } from '../common';\nimport GithubSwatch from './GithubSwatch';\nexport var Github = function Github(_ref) {\n  var width = _ref.width,\n    colors = _ref.colors,\n    onChange = _ref.onChange,\n    onSwatchHover = _ref.onSwatchHover,\n    triangle = _ref.triangle,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var styles = reactCSS(merge({\n    'default': {\n      card: {\n        width: width,\n        background: '#fff',\n        border: '1px solid rgba(0,0,0,0.2)',\n        boxShadow: '0 3px 12px rgba(0,0,0,0.15)',\n        borderRadius: '4px',\n        position: 'relative',\n        padding: '5px',\n        display: 'flex',\n        flexWrap: 'wrap'\n      },\n      triangle: {\n        position: 'absolute',\n        border: '7px solid transparent',\n        borderBottomColor: '#fff'\n      },\n      triangleShadow: {\n        position: 'absolute',\n        border: '8px solid transparent',\n        borderBottomColor: 'rgba(0,0,0,0.15)'\n      }\n    },\n    'hide-triangle': {\n      triangle: {\n        display: 'none'\n      },\n      triangleShadow: {\n        display: 'none'\n      }\n    },\n    'top-left-triangle': {\n      triangle: {\n        top: '-14px',\n        left: '10px'\n      },\n      triangleShadow: {\n        top: '-16px',\n        left: '9px'\n      }\n    },\n    'top-right-triangle': {\n      triangle: {\n        top: '-14px',\n        right: '10px'\n      },\n      triangleShadow: {\n        top: '-16px',\n        right: '9px'\n      }\n    },\n    'bottom-left-triangle': {\n      triangle: {\n        top: '35px',\n        left: '10px',\n        transform: 'rotate(180deg)'\n      },\n      triangleShadow: {\n        top: '37px',\n        left: '9px',\n        transform: 'rotate(180deg)'\n      }\n    },\n    'bottom-right-triangle': {\n      triangle: {\n        top: '35px',\n        right: '10px',\n        transform: 'rotate(180deg)'\n      },\n      triangleShadow: {\n        top: '37px',\n        right: '9px',\n        transform: 'rotate(180deg)'\n      }\n    }\n  }, passedStyles), {\n    'hide-triangle': triangle === 'hide',\n    'top-left-triangle': triangle === 'top-left',\n    'top-right-triangle': triangle === 'top-right',\n    'bottom-left-triangle': triangle === 'bottom-left',\n    'bottom-right-triangle': triangle === 'bottom-right'\n  });\n  var handleChange = function handleChange(hex, e) {\n    return onChange({\n      hex: hex,\n      source: 'hex'\n    }, e);\n  };\n  return React.createElement('div', {\n    style: styles.card,\n    className: 'github-picker ' + className\n  }, React.createElement('div', {\n    style: styles.triangleShadow\n  }), React.createElement('div', {\n    style: styles.triangle\n  }), map(colors, function (c) {\n    return React.createElement(GithubSwatch, {\n      color: c,\n      key: c,\n      onClick: handleChange,\n      onSwatchHover: onSwatchHover\n    });\n  }));\n};\nGithub.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  colors: PropTypes.arrayOf(PropTypes.string),\n  triangle: PropTypes.oneOf(['hide', 'top-left', 'top-right', 'bottom-left', 'bottom-right']),\n  styles: PropTypes.object\n};\nGithub.defaultProps = {\n  width: 200,\n  colors: ['#B80000', '#DB3E00', '#FCCB00', '#008B02', '#006B76', '#1273DE', '#004DCF', '#5300EB', '#EB9694', '#FAD0C3', '#FEF3BD', '#C1E1C5', '#BEDADC', '#C4DEF6', '#BED3F3', '#D4C4FB'],\n  triangle: 'top-left',\n  styles: {}\n};\nexport default ColorWrap(Github);", "map": {"version": 3, "names": ["React", "PropTypes", "reactCSS", "map", "merge", "ColorWrap", "GithubSwatch", "<PERSON><PERSON><PERSON>", "_ref", "width", "colors", "onChange", "onSwatchHover", "triangle", "_ref$styles", "styles", "passedStyles", "undefined", "_ref$className", "className", "card", "background", "border", "boxShadow", "borderRadius", "position", "padding", "display", "flexWrap", "borderBottomColor", "triangleShadow", "top", "left", "right", "transform", "handleChange", "hex", "e", "source", "createElement", "style", "c", "color", "key", "onClick", "propTypes", "oneOfType", "string", "number", "arrayOf", "oneOf", "object", "defaultProps"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/github/Github.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport map from 'lodash-es/map';\nimport merge from 'lodash-es/merge';\n\nimport { ColorWrap } from '../common';\nimport GithubSwatch from './GithubSwatch';\n\nexport var Github = function Github(_ref) {\n  var width = _ref.width,\n      colors = _ref.colors,\n      onChange = _ref.onChange,\n      onSwatchHover = _ref.onSwatchHover,\n      triangle = _ref.triangle,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var styles = reactCSS(merge({\n    'default': {\n      card: {\n        width: width,\n        background: '#fff',\n        border: '1px solid rgba(0,0,0,0.2)',\n        boxShadow: '0 3px 12px rgba(0,0,0,0.15)',\n        borderRadius: '4px',\n        position: 'relative',\n        padding: '5px',\n        display: 'flex',\n        flexWrap: 'wrap'\n      },\n      triangle: {\n        position: 'absolute',\n        border: '7px solid transparent',\n        borderBottomColor: '#fff'\n      },\n      triangleShadow: {\n        position: 'absolute',\n        border: '8px solid transparent',\n        borderBottomColor: 'rgba(0,0,0,0.15)'\n      }\n    },\n    'hide-triangle': {\n      triangle: {\n        display: 'none'\n      },\n      triangleShadow: {\n        display: 'none'\n      }\n    },\n    'top-left-triangle': {\n      triangle: {\n        top: '-14px',\n        left: '10px'\n      },\n      triangleShadow: {\n        top: '-16px',\n        left: '9px'\n      }\n    },\n    'top-right-triangle': {\n      triangle: {\n        top: '-14px',\n        right: '10px'\n      },\n      triangleShadow: {\n        top: '-16px',\n        right: '9px'\n      }\n    },\n    'bottom-left-triangle': {\n      triangle: {\n        top: '35px',\n        left: '10px',\n        transform: 'rotate(180deg)'\n      },\n      triangleShadow: {\n        top: '37px',\n        left: '9px',\n        transform: 'rotate(180deg)'\n      }\n    },\n    'bottom-right-triangle': {\n      triangle: {\n        top: '35px',\n        right: '10px',\n        transform: 'rotate(180deg)'\n      },\n      triangleShadow: {\n        top: '37px',\n        right: '9px',\n        transform: 'rotate(180deg)'\n      }\n    }\n  }, passedStyles), {\n    'hide-triangle': triangle === 'hide',\n    'top-left-triangle': triangle === 'top-left',\n    'top-right-triangle': triangle === 'top-right',\n    'bottom-left-triangle': triangle === 'bottom-left',\n    'bottom-right-triangle': triangle === 'bottom-right'\n  });\n\n  var handleChange = function handleChange(hex, e) {\n    return onChange({ hex: hex, source: 'hex' }, e);\n  };\n\n  return React.createElement(\n    'div',\n    { style: styles.card, className: 'github-picker ' + className },\n    React.createElement('div', { style: styles.triangleShadow }),\n    React.createElement('div', { style: styles.triangle }),\n    map(colors, function (c) {\n      return React.createElement(GithubSwatch, {\n        color: c,\n        key: c,\n        onClick: handleChange,\n        onSwatchHover: onSwatchHover\n      });\n    })\n  );\n};\n\nGithub.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  colors: PropTypes.arrayOf(PropTypes.string),\n  triangle: PropTypes.oneOf(['hide', 'top-left', 'top-right', 'bottom-left', 'bottom-right']),\n  styles: PropTypes.object\n};\n\nGithub.defaultProps = {\n  width: 200,\n  colors: ['#B80000', '#DB3E00', '#FCCB00', '#008B02', '#006B76', '#1273DE', '#004DCF', '#5300EB', '#EB9694', '#FAD0C3', '#FEF3BD', '#C1E1C5', '#BEDADC', '#C4DEF6', '#BED3F3', '#D4C4FB'],\n  triangle: 'top-left',\n  styles: {}\n};\n\nexport default ColorWrap(Github);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,GAAG,MAAM,eAAe;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,YAAY,MAAM,gBAAgB;AAEzC,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACxC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,aAAa,GAAGJ,IAAI,CAACI,aAAa;IAClCC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;IACxBC,WAAW,GAAGN,IAAI,CAACO,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,cAAc,GAAGV,IAAI,CAACW,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKD,SAAS,GAAG,EAAE,GAAGC,cAAc;EAElE,IAAIH,MAAM,GAAGb,QAAQ,CAACE,KAAK,CAAC;IAC1B,SAAS,EAAE;MACTgB,IAAI,EAAE;QACJX,KAAK,EAAEA,KAAK;QACZY,UAAU,EAAE,MAAM;QAClBC,MAAM,EAAE,2BAA2B;QACnCC,SAAS,EAAE,6BAA6B;QACxCC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE;MACZ,CAAC;MACDf,QAAQ,EAAE;QACRY,QAAQ,EAAE,UAAU;QACpBH,MAAM,EAAE,uBAAuB;QAC/BO,iBAAiB,EAAE;MACrB,CAAC;MACDC,cAAc,EAAE;QACdL,QAAQ,EAAE,UAAU;QACpBH,MAAM,EAAE,uBAAuB;QAC/BO,iBAAiB,EAAE;MACrB;IACF,CAAC;IACD,eAAe,EAAE;MACfhB,QAAQ,EAAE;QACRc,OAAO,EAAE;MACX,CAAC;MACDG,cAAc,EAAE;QACdH,OAAO,EAAE;MACX;IACF,CAAC;IACD,mBAAmB,EAAE;MACnBd,QAAQ,EAAE;QACRkB,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE;MACR,CAAC;MACDF,cAAc,EAAE;QACdC,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE;MACR;IACF,CAAC;IACD,oBAAoB,EAAE;MACpBnB,QAAQ,EAAE;QACRkB,GAAG,EAAE,OAAO;QACZE,KAAK,EAAE;MACT,CAAC;MACDH,cAAc,EAAE;QACdC,GAAG,EAAE,OAAO;QACZE,KAAK,EAAE;MACT;IACF,CAAC;IACD,sBAAsB,EAAE;MACtBpB,QAAQ,EAAE;QACRkB,GAAG,EAAE,MAAM;QACXC,IAAI,EAAE,MAAM;QACZE,SAAS,EAAE;MACb,CAAC;MACDJ,cAAc,EAAE;QACdC,GAAG,EAAE,MAAM;QACXC,IAAI,EAAE,KAAK;QACXE,SAAS,EAAE;MACb;IACF,CAAC;IACD,uBAAuB,EAAE;MACvBrB,QAAQ,EAAE;QACRkB,GAAG,EAAE,MAAM;QACXE,KAAK,EAAE,MAAM;QACbC,SAAS,EAAE;MACb,CAAC;MACDJ,cAAc,EAAE;QACdC,GAAG,EAAE,MAAM;QACXE,KAAK,EAAE,KAAK;QACZC,SAAS,EAAE;MACb;IACF;EACF,CAAC,EAAElB,YAAY,CAAC,EAAE;IAChB,eAAe,EAAEH,QAAQ,KAAK,MAAM;IACpC,mBAAmB,EAAEA,QAAQ,KAAK,UAAU;IAC5C,oBAAoB,EAAEA,QAAQ,KAAK,WAAW;IAC9C,sBAAsB,EAAEA,QAAQ,KAAK,aAAa;IAClD,uBAAuB,EAAEA,QAAQ,KAAK;EACxC,CAAC,CAAC;EAEF,IAAIsB,YAAY,GAAG,SAASA,YAAYA,CAACC,GAAG,EAAEC,CAAC,EAAE;IAC/C,OAAO1B,QAAQ,CAAC;MAAEyB,GAAG,EAAEA,GAAG;MAAEE,MAAM,EAAE;IAAM,CAAC,EAAED,CAAC,CAAC;EACjD,CAAC;EAED,OAAOrC,KAAK,CAACuC,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAEzB,MAAM,CAACK,IAAI;IAAED,SAAS,EAAE,gBAAgB,GAAGA;EAAU,CAAC,EAC/DnB,KAAK,CAACuC,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEzB,MAAM,CAACe;EAAe,CAAC,CAAC,EAC5D9B,KAAK,CAACuC,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEzB,MAAM,CAACF;EAAS,CAAC,CAAC,EACtDV,GAAG,CAACO,MAAM,EAAE,UAAU+B,CAAC,EAAE;IACvB,OAAOzC,KAAK,CAACuC,aAAa,CAACjC,YAAY,EAAE;MACvCoC,KAAK,EAAED,CAAC;MACRE,GAAG,EAAEF,CAAC;MACNG,OAAO,EAAET,YAAY;MACrBvB,aAAa,EAAEA;IACjB,CAAC,CAAC;EACJ,CAAC,CACH,CAAC;AACH,CAAC;AAEDL,MAAM,CAACsC,SAAS,GAAG;EACjBpC,KAAK,EAAER,SAAS,CAAC6C,SAAS,CAAC,CAAC7C,SAAS,CAAC8C,MAAM,EAAE9C,SAAS,CAAC+C,MAAM,CAAC,CAAC;EAChEtC,MAAM,EAAET,SAAS,CAACgD,OAAO,CAAChD,SAAS,CAAC8C,MAAM,CAAC;EAC3ClC,QAAQ,EAAEZ,SAAS,CAACiD,KAAK,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;EAC3FnC,MAAM,EAAEd,SAAS,CAACkD;AACpB,CAAC;AAED5C,MAAM,CAAC6C,YAAY,GAAG;EACpB3C,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACxLG,QAAQ,EAAE,UAAU;EACpBE,MAAM,EAAE,CAAC;AACX,CAAC;AAED,eAAeV,SAAS,CAACE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}