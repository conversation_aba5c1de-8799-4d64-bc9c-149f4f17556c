{"ast": null, "code": "var _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\nimport { ColorWrap, Saturation, Hue, Alpha, Checkboard } from '../common';\nimport SketchFields from './SketchFields';\nimport SketchPresetColors from './SketchPresetColors';\nexport var Sketch = function Sketch(_ref) {\n  var width = _ref.width,\n    rgb = _ref.rgb,\n    hex = _ref.hex,\n    hsv = _ref.hsv,\n    hsl = _ref.hsl,\n    onChange = _ref.onChange,\n    onSwatchHover = _ref.onSwatchHover,\n    disableAlpha = _ref.disableAlpha,\n    presetColors = _ref.presetColors,\n    renderers = _ref.renderers,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var styles = reactCSS(merge({\n    'default': _extends({\n      picker: {\n        width: width,\n        padding: '10px 10px 0',\n        boxSizing: 'initial',\n        background: '#fff',\n        borderRadius: '4px',\n        boxShadow: '0 0 0 1px rgba(0,0,0,.15), 0 8px 16px rgba(0,0,0,.15)'\n      },\n      saturation: {\n        width: '100%',\n        paddingBottom: '75%',\n        position: 'relative',\n        overflow: 'hidden'\n      },\n      Saturation: {\n        radius: '3px',\n        shadow: 'inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)'\n      },\n      controls: {\n        display: 'flex'\n      },\n      sliders: {\n        padding: '4px 0',\n        flex: '1'\n      },\n      color: {\n        width: '24px',\n        height: '24px',\n        position: 'relative',\n        marginTop: '4px',\n        marginLeft: '4px',\n        borderRadius: '3px'\n      },\n      activeColor: {\n        absolute: '0px 0px 0px 0px',\n        borderRadius: '2px',\n        background: 'rgba(' + rgb.r + ',' + rgb.g + ',' + rgb.b + ',' + rgb.a + ')',\n        boxShadow: 'inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)'\n      },\n      hue: {\n        position: 'relative',\n        height: '10px',\n        overflow: 'hidden'\n      },\n      Hue: {\n        radius: '2px',\n        shadow: 'inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)'\n      },\n      alpha: {\n        position: 'relative',\n        height: '10px',\n        marginTop: '4px',\n        overflow: 'hidden'\n      },\n      Alpha: {\n        radius: '2px',\n        shadow: 'inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)'\n      }\n    }, passedStyles),\n    'disableAlpha': {\n      color: {\n        height: '10px'\n      },\n      hue: {\n        height: '10px'\n      },\n      alpha: {\n        display: 'none'\n      }\n    }\n  }, passedStyles), {\n    disableAlpha: disableAlpha\n  });\n  return React.createElement('div', {\n    style: styles.picker,\n    className: 'sketch-picker ' + className\n  }, React.createElement('div', {\n    style: styles.saturation\n  }, React.createElement(Saturation, {\n    style: styles.Saturation,\n    hsl: hsl,\n    hsv: hsv,\n    onChange: onChange\n  })), React.createElement('div', {\n    style: styles.controls,\n    className: 'flexbox-fix'\n  }, React.createElement('div', {\n    style: styles.sliders\n  }, React.createElement('div', {\n    style: styles.hue\n  }, React.createElement(Hue, {\n    style: styles.Hue,\n    hsl: hsl,\n    onChange: onChange\n  })), React.createElement('div', {\n    style: styles.alpha\n  }, React.createElement(Alpha, {\n    style: styles.Alpha,\n    rgb: rgb,\n    hsl: hsl,\n    renderers: renderers,\n    onChange: onChange\n  }))), React.createElement('div', {\n    style: styles.color\n  }, React.createElement(Checkboard, null), React.createElement('div', {\n    style: styles.activeColor\n  }))), React.createElement(SketchFields, {\n    rgb: rgb,\n    hsl: hsl,\n    hex: hex,\n    onChange: onChange,\n    disableAlpha: disableAlpha\n  }), React.createElement(SketchPresetColors, {\n    colors: presetColors,\n    onClick: onChange,\n    onSwatchHover: onSwatchHover\n  }));\n};\nSketch.propTypes = {\n  disableAlpha: PropTypes.bool,\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  styles: PropTypes.object\n};\nSketch.defaultProps = {\n  disableAlpha: false,\n  width: 200,\n  styles: {},\n  presetColors: ['#D0021B', '#F5A623', '#F8E71C', '#8B572A', '#7ED321', '#417505', '#BD10E0', '#9013FE', '#4A90E2', '#50E3C2', '#B8E986', '#000000', '#4A4A4A', '#9B9B9B', '#FFFFFF']\n};\nexport default ColorWrap(Sketch);", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "React", "PropTypes", "reactCSS", "merge", "ColorWrap", "Saturation", "<PERSON><PERSON>", "Alpha", "Checkboard", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SketchPresetColors", "Sketch", "_ref", "width", "rgb", "hex", "hsv", "hsl", "onChange", "onSwatchHover", "disable<PERSON><PERSON>pha", "presetColors", "renderers", "_ref$styles", "styles", "passedStyles", "undefined", "_ref$className", "className", "picker", "padding", "boxSizing", "background", "borderRadius", "boxShadow", "saturation", "paddingBottom", "position", "overflow", "radius", "shadow", "controls", "display", "sliders", "flex", "color", "height", "marginTop", "marginLeft", "activeColor", "absolute", "r", "g", "b", "a", "hue", "alpha", "createElement", "style", "colors", "onClick", "propTypes", "bool", "oneOfType", "string", "number", "object", "defaultProps"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/sketch/Sketch.js"], "sourcesContent": ["var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\n\nimport { ColorWrap, Saturation, Hue, Alpha, Checkboard } from '../common';\nimport SketchFields from './SketchFields';\nimport SketchPresetColors from './SketchPresetColors';\n\nexport var Sketch = function Sketch(_ref) {\n  var width = _ref.width,\n      rgb = _ref.rgb,\n      hex = _ref.hex,\n      hsv = _ref.hsv,\n      hsl = _ref.hsl,\n      onChange = _ref.onChange,\n      onSwatchHover = _ref.onSwatchHover,\n      disableAlpha = _ref.disableAlpha,\n      presetColors = _ref.presetColors,\n      renderers = _ref.renderers,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var styles = reactCSS(merge({\n    'default': _extends({\n      picker: {\n        width: width,\n        padding: '10px 10px 0',\n        boxSizing: 'initial',\n        background: '#fff',\n        borderRadius: '4px',\n        boxShadow: '0 0 0 1px rgba(0,0,0,.15), 0 8px 16px rgba(0,0,0,.15)'\n      },\n      saturation: {\n        width: '100%',\n        paddingBottom: '75%',\n        position: 'relative',\n        overflow: 'hidden'\n      },\n      Saturation: {\n        radius: '3px',\n        shadow: 'inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)'\n      },\n      controls: {\n        display: 'flex'\n      },\n      sliders: {\n        padding: '4px 0',\n        flex: '1'\n      },\n      color: {\n        width: '24px',\n        height: '24px',\n        position: 'relative',\n        marginTop: '4px',\n        marginLeft: '4px',\n        borderRadius: '3px'\n      },\n      activeColor: {\n        absolute: '0px 0px 0px 0px',\n        borderRadius: '2px',\n        background: 'rgba(' + rgb.r + ',' + rgb.g + ',' + rgb.b + ',' + rgb.a + ')',\n        boxShadow: 'inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)'\n      },\n      hue: {\n        position: 'relative',\n        height: '10px',\n        overflow: 'hidden'\n      },\n      Hue: {\n        radius: '2px',\n        shadow: 'inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)'\n      },\n\n      alpha: {\n        position: 'relative',\n        height: '10px',\n        marginTop: '4px',\n        overflow: 'hidden'\n      },\n      Alpha: {\n        radius: '2px',\n        shadow: 'inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)'\n      }\n    }, passedStyles),\n    'disableAlpha': {\n      color: {\n        height: '10px'\n      },\n      hue: {\n        height: '10px'\n      },\n      alpha: {\n        display: 'none'\n      }\n    }\n  }, passedStyles), { disableAlpha: disableAlpha });\n\n  return React.createElement(\n    'div',\n    { style: styles.picker, className: 'sketch-picker ' + className },\n    React.createElement(\n      'div',\n      { style: styles.saturation },\n      React.createElement(Saturation, {\n        style: styles.Saturation,\n        hsl: hsl,\n        hsv: hsv,\n        onChange: onChange\n      })\n    ),\n    React.createElement(\n      'div',\n      { style: styles.controls, className: 'flexbox-fix' },\n      React.createElement(\n        'div',\n        { style: styles.sliders },\n        React.createElement(\n          'div',\n          { style: styles.hue },\n          React.createElement(Hue, {\n            style: styles.Hue,\n            hsl: hsl,\n            onChange: onChange\n          })\n        ),\n        React.createElement(\n          'div',\n          { style: styles.alpha },\n          React.createElement(Alpha, {\n            style: styles.Alpha,\n            rgb: rgb,\n            hsl: hsl,\n            renderers: renderers,\n            onChange: onChange\n          })\n        )\n      ),\n      React.createElement(\n        'div',\n        { style: styles.color },\n        React.createElement(Checkboard, null),\n        React.createElement('div', { style: styles.activeColor })\n      )\n    ),\n    React.createElement(SketchFields, {\n      rgb: rgb,\n      hsl: hsl,\n      hex: hex,\n      onChange: onChange,\n      disableAlpha: disableAlpha\n    }),\n    React.createElement(SketchPresetColors, {\n      colors: presetColors,\n      onClick: onChange,\n      onSwatchHover: onSwatchHover\n    })\n  );\n};\n\nSketch.propTypes = {\n  disableAlpha: PropTypes.bool,\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  styles: PropTypes.object\n};\n\nSketch.defaultProps = {\n  disableAlpha: false,\n  width: 200,\n  styles: {},\n  presetColors: ['#D0021B', '#F5A623', '#F8E71C', '#8B572A', '#7ED321', '#417505', '#BD10E0', '#9013FE', '#4A90E2', '#50E3C2', '#B8E986', '#000000', '#4A4A4A', '#9B9B9B', '#FFFFFF']\n};\n\nexport default ColorWrap(Sketch);"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,OAAOS,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,SAAS,EAAEC,UAAU,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,QAAQ,WAAW;AACzE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,kBAAkB,MAAM,sBAAsB;AAErD,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACxC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,GAAG,GAAGF,IAAI,CAACE,GAAG;IACdC,GAAG,GAAGH,IAAI,CAACG,GAAG;IACdC,GAAG,GAAGJ,IAAI,CAACI,GAAG;IACdC,GAAG,GAAGL,IAAI,CAACK,GAAG;IACdC,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IACxBC,aAAa,GAAGP,IAAI,CAACO,aAAa;IAClCC,YAAY,GAAGR,IAAI,CAACQ,YAAY;IAChCC,YAAY,GAAGT,IAAI,CAACS,YAAY;IAChCC,SAAS,GAAGV,IAAI,CAACU,SAAS;IAC1BC,WAAW,GAAGX,IAAI,CAACY,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,cAAc,GAAGf,IAAI,CAACgB,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKD,SAAS,GAAG,EAAE,GAAGC,cAAc;EAElE,IAAIH,MAAM,GAAGtB,QAAQ,CAACC,KAAK,CAAC;IAC1B,SAAS,EAAEf,QAAQ,CAAC;MAClByC,MAAM,EAAE;QACNhB,KAAK,EAAEA,KAAK;QACZiB,OAAO,EAAE,aAAa;QACtBC,SAAS,EAAE,SAAS;QACpBC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE;MACb,CAAC;MACDC,UAAU,EAAE;QACVtB,KAAK,EAAE,MAAM;QACbuB,aAAa,EAAE,KAAK;QACpBC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDjC,UAAU,EAAE;QACVkC,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE;MACV,CAAC;MACDC,QAAQ,EAAE;QACRC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPb,OAAO,EAAE,OAAO;QAChBc,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLhC,KAAK,EAAE,MAAM;QACbiC,MAAM,EAAE,MAAM;QACdT,QAAQ,EAAE,UAAU;QACpBU,SAAS,EAAE,KAAK;QAChBC,UAAU,EAAE,KAAK;QACjBf,YAAY,EAAE;MAChB,CAAC;MACDgB,WAAW,EAAE;QACXC,QAAQ,EAAE,iBAAiB;QAC3BjB,YAAY,EAAE,KAAK;QACnBD,UAAU,EAAE,OAAO,GAAGlB,GAAG,CAACqC,CAAC,GAAG,GAAG,GAAGrC,GAAG,CAACsC,CAAC,GAAG,GAAG,GAAGtC,GAAG,CAACuC,CAAC,GAAG,GAAG,GAAGvC,GAAG,CAACwC,CAAC,GAAG,GAAG;QAC3EpB,SAAS,EAAE;MACb,CAAC;MACDqB,GAAG,EAAE;QACHlB,QAAQ,EAAE,UAAU;QACpBS,MAAM,EAAE,MAAM;QACdR,QAAQ,EAAE;MACZ,CAAC;MACDhC,GAAG,EAAE;QACHiC,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE;MACV,CAAC;MAEDgB,KAAK,EAAE;QACLnB,QAAQ,EAAE,UAAU;QACpBS,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,KAAK;QAChBT,QAAQ,EAAE;MACZ,CAAC;MACD/B,KAAK,EAAE;QACLgC,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE;MACV;IACF,CAAC,EAAEf,YAAY,CAAC;IAChB,cAAc,EAAE;MACdoB,KAAK,EAAE;QACLC,MAAM,EAAE;MACV,CAAC;MACDS,GAAG,EAAE;QACHT,MAAM,EAAE;MACV,CAAC;MACDU,KAAK,EAAE;QACLd,OAAO,EAAE;MACX;IACF;EACF,CAAC,EAAEjB,YAAY,CAAC,EAAE;IAAEL,YAAY,EAAEA;EAAa,CAAC,CAAC;EAEjD,OAAOpB,KAAK,CAACyD,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAACK,MAAM;IAAED,SAAS,EAAE,gBAAgB,GAAGA;EAAU,CAAC,EACjE5B,KAAK,CAACyD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAACW;EAAW,CAAC,EAC5BnC,KAAK,CAACyD,aAAa,CAACpD,UAAU,EAAE;IAC9BqD,KAAK,EAAElC,MAAM,CAACnB,UAAU;IACxBY,GAAG,EAAEA,GAAG;IACRD,GAAG,EAAEA,GAAG;IACRE,QAAQ,EAAEA;EACZ,CAAC,CACH,CAAC,EACDlB,KAAK,CAACyD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAACiB,QAAQ;IAAEb,SAAS,EAAE;EAAc,CAAC,EACpD5B,KAAK,CAACyD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAACmB;EAAQ,CAAC,EACzB3C,KAAK,CAACyD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAAC+B;EAAI,CAAC,EACrBvD,KAAK,CAACyD,aAAa,CAACnD,GAAG,EAAE;IACvBoD,KAAK,EAAElC,MAAM,CAAClB,GAAG;IACjBW,GAAG,EAAEA,GAAG;IACRC,QAAQ,EAAEA;EACZ,CAAC,CACH,CAAC,EACDlB,KAAK,CAACyD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAACgC;EAAM,CAAC,EACvBxD,KAAK,CAACyD,aAAa,CAAClD,KAAK,EAAE;IACzBmD,KAAK,EAAElC,MAAM,CAACjB,KAAK;IACnBO,GAAG,EAAEA,GAAG;IACRG,GAAG,EAAEA,GAAG;IACRK,SAAS,EAAEA,SAAS;IACpBJ,QAAQ,EAAEA;EACZ,CAAC,CACH,CACF,CAAC,EACDlB,KAAK,CAACyD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAACqB;EAAM,CAAC,EACvB7C,KAAK,CAACyD,aAAa,CAACjD,UAAU,EAAE,IAAI,CAAC,EACrCR,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAElC,MAAM,CAACyB;EAAY,CAAC,CAC1D,CACF,CAAC,EACDjD,KAAK,CAACyD,aAAa,CAAChD,YAAY,EAAE;IAChCK,GAAG,EAAEA,GAAG;IACRG,GAAG,EAAEA,GAAG;IACRF,GAAG,EAAEA,GAAG;IACRG,QAAQ,EAAEA,QAAQ;IAClBE,YAAY,EAAEA;EAChB,CAAC,CAAC,EACFpB,KAAK,CAACyD,aAAa,CAAC/C,kBAAkB,EAAE;IACtCiD,MAAM,EAAEtC,YAAY;IACpBuC,OAAO,EAAE1C,QAAQ;IACjBC,aAAa,EAAEA;EACjB,CAAC,CACH,CAAC;AACH,CAAC;AAEDR,MAAM,CAACkD,SAAS,GAAG;EACjBzC,YAAY,EAAEnB,SAAS,CAAC6D,IAAI;EAC5BjD,KAAK,EAAEZ,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAAC+D,MAAM,EAAE/D,SAAS,CAACgE,MAAM,CAAC,CAAC;EAChEzC,MAAM,EAAEvB,SAAS,CAACiE;AACpB,CAAC;AAEDvD,MAAM,CAACwD,YAAY,GAAG;EACpB/C,YAAY,EAAE,KAAK;EACnBP,KAAK,EAAE,GAAG;EACVW,MAAM,EAAE,CAAC,CAAC;EACVH,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;AACpL,CAAC;AAED,eAAejB,SAAS,CAACO,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}