import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  Container,
  Typography,
  Box,
  Breadcrumbs,
  Link,
  Paper
} from '@material-ui/core';
import { NavigateNext, Home } from '@material-ui/icons';

const useStyles = makeStyles((theme) => ({
  pageContainer: {
    minHeight: '100vh',
    backgroundColor: theme.palette.background.default,
    paddingTop: theme.spacing(3),
    paddingBottom: theme.spacing(3),
  },
  pageHeader: {
    marginBottom: theme.spacing(4),
    padding: theme.spacing(3),
    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',
    borderRadius: 16,
    border: '1px solid rgba(102, 126, 234, 0.1)',
  },
  pageTitle: {
    fontSize: '2rem',
    fontWeight: 700,
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    backgroundClip: 'text',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    marginBottom: theme.spacing(1),
  },
  pageSubtitle: {
    fontSize: '1rem',
    color: theme.palette.text.secondary,
    marginBottom: theme.spacing(2),
  },
  breadcrumbs: {
    '& .MuiBreadcrumbs-separator': {
      color: theme.palette.text.secondary,
    },
    '& .MuiLink-root': {
      color: theme.palette.text.secondary,
      textDecoration: 'none',
      fontSize: '0.875rem',
      '&:hover': {
        color: theme.palette.primary.main,
        textDecoration: 'underline',
      }
    },
    '& .MuiTypography-root': {
      color: theme.palette.primary.main,
      fontSize: '0.875rem',
      fontWeight: 500,
    }
  },
  contentArea: {
    '& > *': {
      marginBottom: theme.spacing(3),
    }
  },
  modernPaper: {
    padding: theme.spacing(3),
    borderRadius: 16,
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    border: '1px solid rgba(0,0,0,0.04)',
    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
  },
  actionBar: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(3),
    padding: theme.spacing(2),
    backgroundColor: 'white',
    borderRadius: 12,
    boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
  },
  homeIcon: {
    fontSize: '1rem',
    marginRight: theme.spacing(0.5),
  }
}));

const ModernPageContainer = ({
  title,
  subtitle,
  breadcrumbs = [],
  children,
  maxWidth = 'lg',
  showHeader = true,
  actionBar,
  ...props
}) => {
  const classes = useStyles();

  const defaultBreadcrumbs = [
    {
      label: 'Início',
      href: '/dashboard',
      icon: <Home className={classes.homeIcon} />
    },
    ...breadcrumbs
  ];

  return (
    <Container maxWidth={maxWidth} className={classes.pageContainer} {...props}>
      {showHeader && (
        <Paper className={classes.pageHeader} elevation={0}>
          {/* Breadcrumbs */}
          {defaultBreadcrumbs.length > 1 && (
            <Breadcrumbs
              separator={<NavigateNext fontSize="small" />}
              className={classes.breadcrumbs}
              aria-label="breadcrumb"
            >
              {defaultBreadcrumbs.slice(0, -1).map((crumb, index) => (
                <Link
                  key={index}
                  href={crumb.href}
                  onClick={(e) => {
                    if (crumb.onClick) {
                      e.preventDefault();
                      crumb.onClick();
                    }
                  }}
                >
                  <Box display="flex" alignItems="center">
                    {crumb.icon}
                    {crumb.label}
                  </Box>
                </Link>
              ))}
              <Typography>
                {defaultBreadcrumbs[defaultBreadcrumbs.length - 1].label}
              </Typography>
            </Breadcrumbs>
          )}

          {/* Page Title */}
          {title && (
            <Typography className={classes.pageTitle} variant="h1">
              {title}
            </Typography>
          )}

          {/* Page Subtitle */}
          {subtitle && (
            <Typography className={classes.pageSubtitle}>
              {subtitle}
            </Typography>
          )}
        </Paper>
      )}

      {/* Action Bar */}
      {actionBar && (
        <div className={classes.actionBar}>
          {actionBar}
        </div>
      )}

      {/* Content Area */}
      <div className={classes.contentArea}>
        {children}
      </div>
    </Container>
  );
};

export default ModernPageContainer;
