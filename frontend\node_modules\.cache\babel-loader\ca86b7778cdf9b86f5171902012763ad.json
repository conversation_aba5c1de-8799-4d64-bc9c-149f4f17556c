{"ast": null, "code": "import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport map from 'lodash-es/map';\nimport merge from 'lodash-es/merge';\nimport * as material from 'material-colors';\nimport { ColorWrap, Raised } from '../common';\nimport SwatchesGroup from './SwatchesGroup';\nexport var Swatches = function Swatches(_ref) {\n  var width = _ref.width,\n    height = _ref.height,\n    onChange = _ref.onChange,\n    onSwatchHover = _ref.onSwatchHover,\n    colors = _ref.colors,\n    hex = _ref.hex,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var styles = reactCSS(merge({\n    'default': {\n      picker: {\n        width: width,\n        height: height\n      },\n      overflow: {\n        height: height,\n        overflowY: 'scroll'\n      },\n      body: {\n        padding: '16px 0 6px 16px'\n      },\n      clear: {\n        clear: 'both'\n      }\n    }\n  }, passedStyles));\n  var handleChange = function handleChange(data, e) {\n    return onChange({\n      hex: data,\n      source: 'hex'\n    }, e);\n  };\n  return React.createElement('div', {\n    style: styles.picker,\n    className: 'swatches-picker ' + className\n  }, React.createElement(Raised, null, React.createElement('div', {\n    style: styles.overflow\n  }, React.createElement('div', {\n    style: styles.body\n  }, map(colors, function (group) {\n    return React.createElement(SwatchesGroup, {\n      key: group.toString(),\n      group: group,\n      active: hex,\n      onClick: handleChange,\n      onSwatchHover: onSwatchHover\n    });\n  }), React.createElement('div', {\n    style: styles.clear\n  })))));\n};\nSwatches.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  colors: PropTypes.arrayOf(PropTypes.arrayOf(PropTypes.string)),\n  styles: PropTypes.object\n\n  /* eslint-disable max-len */\n};\nSwatches.defaultProps = {\n  width: 320,\n  height: 240,\n  colors: [[material.red['900'], material.red['700'], material.red['500'], material.red['300'], material.red['100']], [material.pink['900'], material.pink['700'], material.pink['500'], material.pink['300'], material.pink['100']], [material.purple['900'], material.purple['700'], material.purple['500'], material.purple['300'], material.purple['100']], [material.deepPurple['900'], material.deepPurple['700'], material.deepPurple['500'], material.deepPurple['300'], material.deepPurple['100']], [material.indigo['900'], material.indigo['700'], material.indigo['500'], material.indigo['300'], material.indigo['100']], [material.blue['900'], material.blue['700'], material.blue['500'], material.blue['300'], material.blue['100']], [material.lightBlue['900'], material.lightBlue['700'], material.lightBlue['500'], material.lightBlue['300'], material.lightBlue['100']], [material.cyan['900'], material.cyan['700'], material.cyan['500'], material.cyan['300'], material.cyan['100']], [material.teal['900'], material.teal['700'], material.teal['500'], material.teal['300'], material.teal['100']], ['#194D33', material.green['700'], material.green['500'], material.green['300'], material.green['100']], [material.lightGreen['900'], material.lightGreen['700'], material.lightGreen['500'], material.lightGreen['300'], material.lightGreen['100']], [material.lime['900'], material.lime['700'], material.lime['500'], material.lime['300'], material.lime['100']], [material.yellow['900'], material.yellow['700'], material.yellow['500'], material.yellow['300'], material.yellow['100']], [material.amber['900'], material.amber['700'], material.amber['500'], material.amber['300'], material.amber['100']], [material.orange['900'], material.orange['700'], material.orange['500'], material.orange['300'], material.orange['100']], [material.deepOrange['900'], material.deepOrange['700'], material.deepOrange['500'], material.deepOrange['300'], material.deepOrange['100']], [material.brown['900'], material.brown['700'], material.brown['500'], material.brown['300'], material.brown['100']], [material.blueGrey['900'], material.blueGrey['700'], material.blueGrey['500'], material.blueGrey['300'], material.blueGrey['100']], ['#000000', '#525252', '#969696', '#D9D9D9', '#FFFFFF']],\n  styles: {}\n};\nexport default ColorWrap(Swatches);", "map": {"version": 3, "names": ["React", "PropTypes", "reactCSS", "map", "merge", "material", "ColorWrap", "Raised", "SwatchesGroup", "Swatches", "_ref", "width", "height", "onChange", "onSwatchHover", "colors", "hex", "_ref$styles", "styles", "passedStyles", "undefined", "_ref$className", "className", "picker", "overflow", "overflowY", "body", "padding", "clear", "handleChange", "data", "e", "source", "createElement", "style", "group", "key", "toString", "active", "onClick", "propTypes", "oneOfType", "string", "number", "arrayOf", "object", "defaultProps", "red", "pink", "purple", "deepPurple", "indigo", "blue", "lightBlue", "cyan", "teal", "green", "lightGreen", "lime", "yellow", "amber", "orange", "deepOrange", "brown", "blue<PERSON>rey"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/swatches/Swatches.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport map from 'lodash-es/map';\nimport merge from 'lodash-es/merge';\nimport * as material from 'material-colors';\n\nimport { ColorWrap, Raised } from '../common';\nimport SwatchesGroup from './SwatchesGroup';\n\nexport var Swatches = function Swatches(_ref) {\n  var width = _ref.width,\n      height = _ref.height,\n      onChange = _ref.onChange,\n      onSwatchHover = _ref.onSwatchHover,\n      colors = _ref.colors,\n      hex = _ref.hex,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var styles = reactCSS(merge({\n    'default': {\n      picker: {\n        width: width,\n        height: height\n      },\n      overflow: {\n        height: height,\n        overflowY: 'scroll'\n      },\n      body: {\n        padding: '16px 0 6px 16px'\n      },\n      clear: {\n        clear: 'both'\n      }\n    }\n  }, passedStyles));\n\n  var handleChange = function handleChange(data, e) {\n    return onChange({ hex: data, source: 'hex' }, e);\n  };\n\n  return React.createElement(\n    'div',\n    { style: styles.picker, className: 'swatches-picker ' + className },\n    React.createElement(\n      Raised,\n      null,\n      React.createElement(\n        'div',\n        { style: styles.overflow },\n        React.createElement(\n          'div',\n          { style: styles.body },\n          map(colors, function (group) {\n            return React.createElement(SwatchesGroup, {\n              key: group.toString(),\n              group: group,\n              active: hex,\n              onClick: handleChange,\n              onSwatchHover: onSwatchHover\n            });\n          }),\n          React.createElement('div', { style: styles.clear })\n        )\n      )\n    )\n  );\n};\n\nSwatches.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  colors: PropTypes.arrayOf(PropTypes.arrayOf(PropTypes.string)),\n  styles: PropTypes.object\n\n  /* eslint-disable max-len */\n};Swatches.defaultProps = {\n  width: 320,\n  height: 240,\n  colors: [[material.red['900'], material.red['700'], material.red['500'], material.red['300'], material.red['100']], [material.pink['900'], material.pink['700'], material.pink['500'], material.pink['300'], material.pink['100']], [material.purple['900'], material.purple['700'], material.purple['500'], material.purple['300'], material.purple['100']], [material.deepPurple['900'], material.deepPurple['700'], material.deepPurple['500'], material.deepPurple['300'], material.deepPurple['100']], [material.indigo['900'], material.indigo['700'], material.indigo['500'], material.indigo['300'], material.indigo['100']], [material.blue['900'], material.blue['700'], material.blue['500'], material.blue['300'], material.blue['100']], [material.lightBlue['900'], material.lightBlue['700'], material.lightBlue['500'], material.lightBlue['300'], material.lightBlue['100']], [material.cyan['900'], material.cyan['700'], material.cyan['500'], material.cyan['300'], material.cyan['100']], [material.teal['900'], material.teal['700'], material.teal['500'], material.teal['300'], material.teal['100']], ['#194D33', material.green['700'], material.green['500'], material.green['300'], material.green['100']], [material.lightGreen['900'], material.lightGreen['700'], material.lightGreen['500'], material.lightGreen['300'], material.lightGreen['100']], [material.lime['900'], material.lime['700'], material.lime['500'], material.lime['300'], material.lime['100']], [material.yellow['900'], material.yellow['700'], material.yellow['500'], material.yellow['300'], material.yellow['100']], [material.amber['900'], material.amber['700'], material.amber['500'], material.amber['300'], material.amber['100']], [material.orange['900'], material.orange['700'], material.orange['500'], material.orange['300'], material.orange['100']], [material.deepOrange['900'], material.deepOrange['700'], material.deepOrange['500'], material.deepOrange['300'], material.deepOrange['100']], [material.brown['900'], material.brown['700'], material.brown['500'], material.brown['300'], material.brown['100']], [material.blueGrey['900'], material.blueGrey['700'], material.blueGrey['500'], material.blueGrey['300'], material.blueGrey['100']], ['#000000', '#525252', '#969696', '#D9D9D9', '#FFFFFF']],\n  styles: {}\n};\n\nexport default ColorWrap(Swatches);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,GAAG,MAAM,eAAe;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAO,KAAKC,QAAQ,MAAM,iBAAiB;AAE3C,SAASC,SAAS,EAAEC,MAAM,QAAQ,WAAW;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,OAAO,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;EAC5C,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,aAAa,GAAGJ,IAAI,CAACI,aAAa;IAClCC,MAAM,GAAGL,IAAI,CAACK,MAAM;IACpBC,GAAG,GAAGN,IAAI,CAACM,GAAG;IACdC,WAAW,GAAGP,IAAI,CAACQ,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,cAAc,GAAGX,IAAI,CAACY,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKD,SAAS,GAAG,EAAE,GAAGC,cAAc;EAElE,IAAIH,MAAM,GAAGhB,QAAQ,CAACE,KAAK,CAAC;IAC1B,SAAS,EAAE;MACTmB,MAAM,EAAE;QACNZ,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC;MACDY,QAAQ,EAAE;QACRZ,MAAM,EAAEA,MAAM;QACda,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE;MACT;IACF;EACF,CAAC,EAAET,YAAY,CAAC,CAAC;EAEjB,IAAIU,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,CAAC,EAAE;IAChD,OAAOlB,QAAQ,CAAC;MAAEG,GAAG,EAAEc,IAAI;MAAEE,MAAM,EAAE;IAAM,CAAC,EAAED,CAAC,CAAC;EAClD,CAAC;EAED,OAAO/B,KAAK,CAACiC,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAEhB,MAAM,CAACK,MAAM;IAAED,SAAS,EAAE,kBAAkB,GAAGA;EAAU,CAAC,EACnEtB,KAAK,CAACiC,aAAa,CACjB1B,MAAM,EACN,IAAI,EACJP,KAAK,CAACiC,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEhB,MAAM,CAACM;EAAS,CAAC,EAC1BxB,KAAK,CAACiC,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEhB,MAAM,CAACQ;EAAK,CAAC,EACtBvB,GAAG,CAACY,MAAM,EAAE,UAAUoB,KAAK,EAAE;IAC3B,OAAOnC,KAAK,CAACiC,aAAa,CAACzB,aAAa,EAAE;MACxC4B,GAAG,EAAED,KAAK,CAACE,QAAQ,CAAC,CAAC;MACrBF,KAAK,EAAEA,KAAK;MACZG,MAAM,EAAEtB,GAAG;MACXuB,OAAO,EAAEV,YAAY;MACrBf,aAAa,EAAEA;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC,EACFd,KAAK,CAACiC,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEhB,MAAM,CAACU;EAAM,CAAC,CACpD,CACF,CACF,CACF,CAAC;AACH,CAAC;AAEDnB,QAAQ,CAAC+B,SAAS,GAAG;EACnB7B,KAAK,EAAEV,SAAS,CAACwC,SAAS,CAAC,CAACxC,SAAS,CAACyC,MAAM,EAAEzC,SAAS,CAAC0C,MAAM,CAAC,CAAC;EAChE/B,MAAM,EAAEX,SAAS,CAACwC,SAAS,CAAC,CAACxC,SAAS,CAACyC,MAAM,EAAEzC,SAAS,CAAC0C,MAAM,CAAC,CAAC;EACjE5B,MAAM,EAAEd,SAAS,CAAC2C,OAAO,CAAC3C,SAAS,CAAC2C,OAAO,CAAC3C,SAAS,CAACyC,MAAM,CAAC,CAAC;EAC9DxB,MAAM,EAAEjB,SAAS,CAAC4C;;EAElB;AACF,CAAC;AAACpC,QAAQ,CAACqC,YAAY,GAAG;EACxBnC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,GAAG;EACXG,MAAM,EAAE,CAAC,CAACV,QAAQ,CAAC0C,GAAG,CAAC,KAAK,CAAC,EAAE1C,QAAQ,CAAC0C,GAAG,CAAC,KAAK,CAAC,EAAE1C,QAAQ,CAAC0C,GAAG,CAAC,KAAK,CAAC,EAAE1C,QAAQ,CAAC0C,GAAG,CAAC,KAAK,CAAC,EAAE1C,QAAQ,CAAC0C,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC1C,QAAQ,CAAC2C,IAAI,CAAC,KAAK,CAAC,EAAE3C,QAAQ,CAAC2C,IAAI,CAAC,KAAK,CAAC,EAAE3C,QAAQ,CAAC2C,IAAI,CAAC,KAAK,CAAC,EAAE3C,QAAQ,CAAC2C,IAAI,CAAC,KAAK,CAAC,EAAE3C,QAAQ,CAAC2C,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC3C,QAAQ,CAAC4C,MAAM,CAAC,KAAK,CAAC,EAAE5C,QAAQ,CAAC4C,MAAM,CAAC,KAAK,CAAC,EAAE5C,QAAQ,CAAC4C,MAAM,CAAC,KAAK,CAAC,EAAE5C,QAAQ,CAAC4C,MAAM,CAAC,KAAK,CAAC,EAAE5C,QAAQ,CAAC4C,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC5C,QAAQ,CAAC6C,UAAU,CAAC,KAAK,CAAC,EAAE7C,QAAQ,CAAC6C,UAAU,CAAC,KAAK,CAAC,EAAE7C,QAAQ,CAAC6C,UAAU,CAAC,KAAK,CAAC,EAAE7C,QAAQ,CAAC6C,UAAU,CAAC,KAAK,CAAC,EAAE7C,QAAQ,CAAC6C,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC7C,QAAQ,CAAC8C,MAAM,CAAC,KAAK,CAAC,EAAE9C,QAAQ,CAAC8C,MAAM,CAAC,KAAK,CAAC,EAAE9C,QAAQ,CAAC8C,MAAM,CAAC,KAAK,CAAC,EAAE9C,QAAQ,CAAC8C,MAAM,CAAC,KAAK,CAAC,EAAE9C,QAAQ,CAAC8C,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC9C,QAAQ,CAAC+C,IAAI,CAAC,KAAK,CAAC,EAAE/C,QAAQ,CAAC+C,IAAI,CAAC,KAAK,CAAC,EAAE/C,QAAQ,CAAC+C,IAAI,CAAC,KAAK,CAAC,EAAE/C,QAAQ,CAAC+C,IAAI,CAAC,KAAK,CAAC,EAAE/C,QAAQ,CAAC+C,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC/C,QAAQ,CAACgD,SAAS,CAAC,KAAK,CAAC,EAAEhD,QAAQ,CAACgD,SAAS,CAAC,KAAK,CAAC,EAAEhD,QAAQ,CAACgD,SAAS,CAAC,KAAK,CAAC,EAAEhD,QAAQ,CAACgD,SAAS,CAAC,KAAK,CAAC,EAAEhD,QAAQ,CAACgD,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAChD,QAAQ,CAACiD,IAAI,CAAC,KAAK,CAAC,EAAEjD,QAAQ,CAACiD,IAAI,CAAC,KAAK,CAAC,EAAEjD,QAAQ,CAACiD,IAAI,CAAC,KAAK,CAAC,EAAEjD,QAAQ,CAACiD,IAAI,CAAC,KAAK,CAAC,EAAEjD,QAAQ,CAACiD,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAACjD,QAAQ,CAACkD,IAAI,CAAC,KAAK,CAAC,EAAElD,QAAQ,CAACkD,IAAI,CAAC,KAAK,CAAC,EAAElD,QAAQ,CAACkD,IAAI,CAAC,KAAK,CAAC,EAAElD,QAAQ,CAACkD,IAAI,CAAC,KAAK,CAAC,EAAElD,QAAQ,CAACkD,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,SAAS,EAAElD,QAAQ,CAACmD,KAAK,CAAC,KAAK,CAAC,EAAEnD,QAAQ,CAACmD,KAAK,CAAC,KAAK,CAAC,EAAEnD,QAAQ,CAACmD,KAAK,CAAC,KAAK,CAAC,EAAEnD,QAAQ,CAACmD,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAACnD,QAAQ,CAACoD,UAAU,CAAC,KAAK,CAAC,EAAEpD,QAAQ,CAACoD,UAAU,CAAC,KAAK,CAAC,EAAEpD,QAAQ,CAACoD,UAAU,CAAC,KAAK,CAAC,EAAEpD,QAAQ,CAACoD,UAAU,CAAC,KAAK,CAAC,EAAEpD,QAAQ,CAACoD,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAACpD,QAAQ,CAACqD,IAAI,CAAC,KAAK,CAAC,EAAErD,QAAQ,CAACqD,IAAI,CAAC,KAAK,CAAC,EAAErD,QAAQ,CAACqD,IAAI,CAAC,KAAK,CAAC,EAAErD,QAAQ,CAACqD,IAAI,CAAC,KAAK,CAAC,EAAErD,QAAQ,CAACqD,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAACrD,QAAQ,CAACsD,MAAM,CAAC,KAAK,CAAC,EAAEtD,QAAQ,CAACsD,MAAM,CAAC,KAAK,CAAC,EAAEtD,QAAQ,CAACsD,MAAM,CAAC,KAAK,CAAC,EAAEtD,QAAQ,CAACsD,MAAM,CAAC,KAAK,CAAC,EAAEtD,QAAQ,CAACsD,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAACtD,QAAQ,CAACuD,KAAK,CAAC,KAAK,CAAC,EAAEvD,QAAQ,CAACuD,KAAK,CAAC,KAAK,CAAC,EAAEvD,QAAQ,CAACuD,KAAK,CAAC,KAAK,CAAC,EAAEvD,QAAQ,CAACuD,KAAK,CAAC,KAAK,CAAC,EAAEvD,QAAQ,CAACuD,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAACvD,QAAQ,CAACwD,MAAM,CAAC,KAAK,CAAC,EAAExD,QAAQ,CAACwD,MAAM,CAAC,KAAK,CAAC,EAAExD,QAAQ,CAACwD,MAAM,CAAC,KAAK,CAAC,EAAExD,QAAQ,CAACwD,MAAM,CAAC,KAAK,CAAC,EAAExD,QAAQ,CAACwD,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAACxD,QAAQ,CAACyD,UAAU,CAAC,KAAK,CAAC,EAAEzD,QAAQ,CAACyD,UAAU,CAAC,KAAK,CAAC,EAAEzD,QAAQ,CAACyD,UAAU,CAAC,KAAK,CAAC,EAAEzD,QAAQ,CAACyD,UAAU,CAAC,KAAK,CAAC,EAAEzD,QAAQ,CAACyD,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAACzD,QAAQ,CAAC0D,KAAK,CAAC,KAAK,CAAC,EAAE1D,QAAQ,CAAC0D,KAAK,CAAC,KAAK,CAAC,EAAE1D,QAAQ,CAAC0D,KAAK,CAAC,KAAK,CAAC,EAAE1D,QAAQ,CAAC0D,KAAK,CAAC,KAAK,CAAC,EAAE1D,QAAQ,CAAC0D,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC1D,QAAQ,CAAC2D,QAAQ,CAAC,KAAK,CAAC,EAAE3D,QAAQ,CAAC2D,QAAQ,CAAC,KAAK,CAAC,EAAE3D,QAAQ,CAAC2D,QAAQ,CAAC,KAAK,CAAC,EAAE3D,QAAQ,CAAC2D,QAAQ,CAAC,KAAK,CAAC,EAAE3D,QAAQ,CAAC2D,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EAC9sE9C,MAAM,EAAE,CAAC;AACX,CAAC;AAED,eAAeZ,SAAS,CAACG,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}