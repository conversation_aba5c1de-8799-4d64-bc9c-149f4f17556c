{"ast": null, "code": "import React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M17.62 10l-10-10-1.41 1.41 2.38 2.38L2.38 10 10 17.62 17.62 10zM5.21 10L10 5.21 14.79 10H5.21zM19 11.5s-2 2.17-2 3.5c0 1.1.9 2 2 2s2-.9 2-2c0-1.33-2-3.5-2-3.5z\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  fillOpacity: \".36\",\n  d: \"M0 20h24v4H0v-4z\"\n})), 'FormatColorFillSharp');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "Fragment", "d", "fillOpacity"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/FormatColorFillSharp.js"], "sourcesContent": ["import React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M17.62 10l-10-10-1.41 1.41 2.38 2.38L2.38 10 10 17.62 17.62 10zM5.21 10L10 5.21 14.79 10H5.21zM19 11.5s-2 2.17-2 3.5c0 1.1.9 2 2 2s2-.9 2-2c0-1.33-2-3.5-2-3.5z\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  fillOpacity: \".36\",\n  d: \"M0 20h24v4H0v-4z\"\n})), 'FormatColorFillSharp');"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAACF,KAAK,CAACG,QAAQ,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC5HE,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,aAAaJ,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC3CG,WAAW,EAAE,KAAK;EAClBD,CAAC,EAAE;AACL,CAAC,CAAC,CAAC,EAAE,sBAAsB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}