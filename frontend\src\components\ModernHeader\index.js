import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Avatar,
  Box,
  Chip,
  Badge
} from '@material-ui/core';
import {
  Menu as MenuIcon,
  Notifications,
  Search,
  Settings
} from '@material-ui/icons';

const useStyles = makeStyles((theme) => ({
  appBar: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
    borderBottom: 'none',
    backdropFilter: 'blur(10px)',
  },
  toolbar: {
    paddingLeft: 24,
    paddingRight: 24,
    minHeight: 64,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftSection: {
    display: 'flex',
    alignItems: 'center',
    gap: 16,
  },
  centerSection: {
    flex: 1,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  rightSection: {
    display: 'flex',
    alignItems: 'center',
    gap: 12,
  },
  menuButton: {
    color: 'white',
    backgroundColor: 'rgba(255,255,255,0.1)',
    '&:hover': {
      backgroundColor: 'rgba(255,255,255,0.2)',
      transform: 'scale(1.05)',
    },
    transition: 'all 0.2s ease-in-out',
  },
  title: {
    color: 'white',
    fontWeight: 600,
    fontSize: '1.1rem',
    display: 'flex',
    alignItems: 'center',
    gap: 8,
  },
  welcomeText: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: '0.875rem',
    fontWeight: 400,
  },
  userName: {
    color: 'white',
    fontWeight: 600,
  },
  actionButton: {
    color: 'white',
    backgroundColor: 'rgba(255,255,255,0.1)',
    '&:hover': {
      backgroundColor: 'rgba(255,255,255,0.2)',
      transform: 'scale(1.05)',
    },
    transition: 'all 0.2s ease-in-out',
    width: 40,
    height: 40,
  },
  avatar: {
    width: 36,
    height: 36,
    backgroundColor: 'rgba(255,255,255,0.2)',
    color: 'white',
    fontWeight: 600,
    border: '2px solid rgba(255,255,255,0.3)',
    cursor: 'pointer',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      transform: 'scale(1.1)',
      borderColor: 'rgba(255,255,255,0.5)',
    }
  },
  statusChip: {
    backgroundColor: 'rgba(76, 175, 80, 0.9)',
    color: 'white',
    fontSize: '0.75rem',
    height: 24,
    '& .MuiChip-label': {
      paddingLeft: 8,
      paddingRight: 8,
    }
  },
  notificationBadge: {
    '& .MuiBadge-badge': {
      backgroundColor: '#ff4444',
      color: 'white',
      fontSize: '0.75rem',
      minWidth: 18,
      height: 18,
    }
  }
}));

const ModernHeader = ({
  onMenuClick,
  user,
  notifications = 0,
  onNotificationClick,
  onSettingsClick,
  onUserClick,
  showSearch = false,
  onSearchClick
}) => {
  const classes = useStyles();

  const getUserInitials = (name) => {
    if (!name) return 'U';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  return (
    <AppBar position="fixed" className={classes.appBar}>
      <Toolbar className={classes.toolbar}>
        {/* Left Section */}
        <Box className={classes.leftSection}>
          <IconButton
            edge="start"
            className={classes.menuButton}
            onClick={onMenuClick}
            aria-label="menu"
          >
            <MenuIcon />
          </IconButton>
        </Box>

        {/* Center Section */}
        <Box className={classes.centerSection}>
          <Box textAlign="center">
            <Typography className={classes.welcomeText}>
              Olá, <span className={classes.userName}>{user?.name || 'Usuário'}</span>
            </Typography>
            <Typography variant="caption" style={{ color: 'rgba(255,255,255,0.7)' }}>
              Seja bem-vindo ao sistema
            </Typography>
          </Box>
        </Box>

        {/* Right Section */}
        <Box className={classes.rightSection}>
          {/* Status Online */}
          <Chip 
            label="Online" 
            size="small" 
            className={classes.statusChip}
          />

          {/* Search Button */}
          {showSearch && (
            <IconButton
              className={classes.actionButton}
              onClick={onSearchClick}
              aria-label="search"
            >
              <Search />
            </IconButton>
          )}

          {/* Notifications */}
          <IconButton
            className={classes.actionButton}
            onClick={onNotificationClick}
            aria-label="notifications"
          >
            <Badge 
              badgeContent={notifications} 
              className={classes.notificationBadge}
            >
              <Notifications />
            </Badge>
          </IconButton>

          {/* Settings */}
          <IconButton
            className={classes.actionButton}
            onClick={onSettingsClick}
            aria-label="settings"
          >
            <Settings />
          </IconButton>

          {/* User Avatar */}
          <Avatar 
            className={classes.avatar}
            onClick={onUserClick}
          >
            {getUserInitials(user?.name)}
          </Avatar>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default ModernHeader;
