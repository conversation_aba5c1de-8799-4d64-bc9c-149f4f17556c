{"env": {"es2021": true, "node": true, "jest": true}, "extends": ["airbnb-base", "plugin:@typescript-eslint/recommended", "prettier/@typescript-eslint", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}, "plugins": ["@typescript-eslint", "prettier"], "rules": {"@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "_"}], "import/prefer-default-export": "off", "no-console": "off", "no-param-reassign": "off", "prettier/prettier": "error", "import/extensions": ["error", "ignorePackages", {"ts": "never"}], "quotes": [1, "double", {"avoidEscape": true}]}, "settings": {"import/resolver": {"typescript": {}}}}