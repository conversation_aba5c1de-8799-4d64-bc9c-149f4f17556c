{"ast": null, "code": "var _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n}\nimport React, { Component, PureComponent } from 'react';\nimport debounce from 'lodash-es/debounce';\nimport * as color from '../../helpers/color';\nexport var ColorWrap = function ColorWrap(Picker) {\n  var ColorPicker = function (_ref) {\n    _inherits(ColorPicker, _ref);\n    function ColorPicker(props) {\n      _classCallCheck(this, ColorPicker);\n      var _this = _possibleConstructorReturn(this, (ColorPicker.__proto__ || Object.getPrototypeOf(ColorPicker)).call(this));\n      _this.handleChange = function (data, event) {\n        var isValidColor = color.simpleCheckForValidColor(data);\n        if (isValidColor) {\n          var colors = color.toState(data, data.h || _this.state.oldHue);\n          _this.setState(colors);\n          _this.props.onChangeComplete && _this.debounce(_this.props.onChangeComplete, colors, event);\n          _this.props.onChange && _this.props.onChange(colors, event);\n        }\n      };\n      _this.handleSwatchHover = function (data, event) {\n        var isValidColor = color.simpleCheckForValidColor(data);\n        if (isValidColor) {\n          var colors = color.toState(data, data.h || _this.state.oldHue);\n          _this.props.onSwatchHover && _this.props.onSwatchHover(colors, event);\n        }\n      };\n      _this.state = _extends({}, color.toState(props.color, 0));\n      _this.debounce = debounce(function (fn, data, event) {\n        fn(data, event);\n      }, 100);\n      return _this;\n    }\n    _createClass(ColorPicker, [{\n      key: 'render',\n      value: function render() {\n        var optionalEvents = {};\n        if (this.props.onSwatchHover) {\n          optionalEvents.onSwatchHover = this.handleSwatchHover;\n        }\n        return React.createElement(Picker, _extends({}, this.props, this.state, {\n          onChange: this.handleChange\n        }, optionalEvents));\n      }\n    }], [{\n      key: 'getDerivedStateFromProps',\n      value: function getDerivedStateFromProps(nextProps, state) {\n        return _extends({}, color.toState(nextProps.color, state.oldHue));\n      }\n    }]);\n    return ColorPicker;\n  }(PureComponent || Component);\n  ColorPicker.propTypes = _extends({}, Picker.propTypes);\n  ColorPicker.defaultProps = _extends({}, Picker.defaultProps, {\n    color: {\n      h: 250,\n      s: 0.50,\n      l: 0.20,\n      a: 1\n    }\n  });\n  return ColorPicker;\n};\nexport default ColorWrap;", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "_createClass", "defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_classCallCheck", "instance", "TypeError", "_possibleConstructorReturn", "self", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "value", "setPrototypeOf", "__proto__", "React", "Component", "PureComponent", "debounce", "color", "ColorWrap", "Picker", "ColorPicker", "_ref", "_this", "getPrototypeOf", "handleChange", "data", "event", "isValidColor", "simpleCheckForValidColor", "colors", "toState", "h", "state", "oldHue", "setState", "onChangeComplete", "onChange", "handleSwatchHover", "onSwatchHover", "fn", "render", "optionalEvents", "createElement", "getDerivedStateFromProps", "nextProps", "propTypes", "defaultProps", "s", "l", "a"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/common/ColorWrap.js"], "sourcesContent": ["var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nimport React, { Component, PureComponent } from 'react';\nimport debounce from 'lodash-es/debounce';\nimport * as color from '../../helpers/color';\n\nexport var ColorWrap = function ColorWrap(Picker) {\n  var ColorPicker = function (_ref) {\n    _inherits(ColorPicker, _ref);\n\n    function ColorPicker(props) {\n      _classCallCheck(this, ColorPicker);\n\n      var _this = _possibleConstructorReturn(this, (ColorPicker.__proto__ || Object.getPrototypeOf(ColorPicker)).call(this));\n\n      _this.handleChange = function (data, event) {\n        var isValidColor = color.simpleCheckForValidColor(data);\n        if (isValidColor) {\n          var colors = color.toState(data, data.h || _this.state.oldHue);\n          _this.setState(colors);\n          _this.props.onChangeComplete && _this.debounce(_this.props.onChangeComplete, colors, event);\n          _this.props.onChange && _this.props.onChange(colors, event);\n        }\n      };\n\n      _this.handleSwatchHover = function (data, event) {\n        var isValidColor = color.simpleCheckForValidColor(data);\n        if (isValidColor) {\n          var colors = color.toState(data, data.h || _this.state.oldHue);\n          _this.props.onSwatchHover && _this.props.onSwatchHover(colors, event);\n        }\n      };\n\n      _this.state = _extends({}, color.toState(props.color, 0));\n\n      _this.debounce = debounce(function (fn, data, event) {\n        fn(data, event);\n      }, 100);\n      return _this;\n    }\n\n    _createClass(ColorPicker, [{\n      key: 'render',\n      value: function render() {\n        var optionalEvents = {};\n        if (this.props.onSwatchHover) {\n          optionalEvents.onSwatchHover = this.handleSwatchHover;\n        }\n\n        return React.createElement(Picker, _extends({}, this.props, this.state, {\n          onChange: this.handleChange\n        }, optionalEvents));\n      }\n    }], [{\n      key: 'getDerivedStateFromProps',\n      value: function getDerivedStateFromProps(nextProps, state) {\n        return _extends({}, color.toState(nextProps.color, state.oldHue));\n      }\n    }]);\n\n    return ColorPicker;\n  }(PureComponent || Component);\n\n  ColorPicker.propTypes = _extends({}, Picker.propTypes);\n\n  ColorPicker.defaultProps = _extends({}, Picker.defaultProps, {\n    color: {\n      h: 250,\n      s: 0.50,\n      l: 0.20,\n      a: 1\n    }\n  });\n\n  return ColorPicker;\n};\n\nexport default ColorWrap;"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,IAAIS,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACV,MAAM,EAAEW,KAAK,EAAE;IAAE,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,KAAK,CAACR,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIW,UAAU,GAAGD,KAAK,CAACV,CAAC,CAAC;MAAEW,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEjB,MAAM,CAACkB,cAAc,CAAChB,MAAM,EAAEY,UAAU,CAACP,GAAG,EAAEO,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAER,gBAAgB,CAACO,WAAW,CAACX,SAAS,EAAEY,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAET,gBAAgB,CAACO,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,SAASG,eAAeA,CAACC,QAAQ,EAAEJ,WAAW,EAAE;EAAE,IAAI,EAAEI,QAAQ,YAAYJ,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIK,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEhB,IAAI,EAAE;EAAE,IAAI,CAACgB,IAAI,EAAE;IAAE,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOjB,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGgB,IAAI;AAAE;AAE/O,SAASE,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIN,SAAS,CAAC,0DAA0D,GAAG,OAAOM,UAAU,CAAC;EAAE;EAAED,QAAQ,CAACrB,SAAS,GAAGR,MAAM,CAAC+B,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACtB,SAAS,EAAE;IAAEwB,WAAW,EAAE;MAAEC,KAAK,EAAEJ,QAAQ;MAAEd,UAAU,EAAE,KAAK;MAAEE,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAIc,UAAU,EAAE9B,MAAM,CAACkC,cAAc,GAAGlC,MAAM,CAACkC,cAAc,CAACL,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACM,SAAS,GAAGL,UAAU;AAAE;AAE7e,OAAOM,KAAK,IAAIC,SAAS,EAAEC,aAAa,QAAQ,OAAO;AACvD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAO,KAAKC,KAAK,MAAM,qBAAqB;AAE5C,OAAO,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,MAAM,EAAE;EAChD,IAAIC,WAAW,GAAG,UAAUC,IAAI,EAAE;IAChChB,SAAS,CAACe,WAAW,EAAEC,IAAI,CAAC;IAE5B,SAASD,WAAWA,CAAC9B,KAAK,EAAE;MAC1BS,eAAe,CAAC,IAAI,EAAEqB,WAAW,CAAC;MAElC,IAAIE,KAAK,GAAGpB,0BAA0B,CAAC,IAAI,EAAE,CAACkB,WAAW,CAACR,SAAS,IAAInC,MAAM,CAAC8C,cAAc,CAACH,WAAW,CAAC,EAAEjC,IAAI,CAAC,IAAI,CAAC,CAAC;MAEtHmC,KAAK,CAACE,YAAY,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAE;QAC1C,IAAIC,YAAY,GAAGV,KAAK,CAACW,wBAAwB,CAACH,IAAI,CAAC;QACvD,IAAIE,YAAY,EAAE;UAChB,IAAIE,MAAM,GAAGZ,KAAK,CAACa,OAAO,CAACL,IAAI,EAAEA,IAAI,CAACM,CAAC,IAAIT,KAAK,CAACU,KAAK,CAACC,MAAM,CAAC;UAC9DX,KAAK,CAACY,QAAQ,CAACL,MAAM,CAAC;UACtBP,KAAK,CAAChC,KAAK,CAAC6C,gBAAgB,IAAIb,KAAK,CAACN,QAAQ,CAACM,KAAK,CAAChC,KAAK,CAAC6C,gBAAgB,EAAEN,MAAM,EAAEH,KAAK,CAAC;UAC3FJ,KAAK,CAAChC,KAAK,CAAC8C,QAAQ,IAAId,KAAK,CAAChC,KAAK,CAAC8C,QAAQ,CAACP,MAAM,EAAEH,KAAK,CAAC;QAC7D;MACF,CAAC;MAEDJ,KAAK,CAACe,iBAAiB,GAAG,UAAUZ,IAAI,EAAEC,KAAK,EAAE;QAC/C,IAAIC,YAAY,GAAGV,KAAK,CAACW,wBAAwB,CAACH,IAAI,CAAC;QACvD,IAAIE,YAAY,EAAE;UAChB,IAAIE,MAAM,GAAGZ,KAAK,CAACa,OAAO,CAACL,IAAI,EAAEA,IAAI,CAACM,CAAC,IAAIT,KAAK,CAACU,KAAK,CAACC,MAAM,CAAC;UAC9DX,KAAK,CAAChC,KAAK,CAACgD,aAAa,IAAIhB,KAAK,CAAChC,KAAK,CAACgD,aAAa,CAACT,MAAM,EAAEH,KAAK,CAAC;QACvE;MACF,CAAC;MAEDJ,KAAK,CAACU,KAAK,GAAGxD,QAAQ,CAAC,CAAC,CAAC,EAAEyC,KAAK,CAACa,OAAO,CAACxC,KAAK,CAAC2B,KAAK,EAAE,CAAC,CAAC,CAAC;MAEzDK,KAAK,CAACN,QAAQ,GAAGA,QAAQ,CAAC,UAAUuB,EAAE,EAAEd,IAAI,EAAEC,KAAK,EAAE;QACnDa,EAAE,CAACd,IAAI,EAAEC,KAAK,CAAC;MACjB,CAAC,EAAE,GAAG,CAAC;MACP,OAAOJ,KAAK;IACd;IAEAlC,YAAY,CAACgC,WAAW,EAAE,CAAC;MACzBpC,GAAG,EAAE,QAAQ;MACb0B,KAAK,EAAE,SAAS8B,MAAMA,CAAA,EAAG;QACvB,IAAIC,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,IAAI,CAACnD,KAAK,CAACgD,aAAa,EAAE;UAC5BG,cAAc,CAACH,aAAa,GAAG,IAAI,CAACD,iBAAiB;QACvD;QAEA,OAAOxB,KAAK,CAAC6B,aAAa,CAACvB,MAAM,EAAE3C,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACc,KAAK,EAAE,IAAI,CAAC0C,KAAK,EAAE;UACtEI,QAAQ,EAAE,IAAI,CAACZ;QACjB,CAAC,EAAEiB,cAAc,CAAC,CAAC;MACrB;IACF,CAAC,CAAC,EAAE,CAAC;MACHzD,GAAG,EAAE,0BAA0B;MAC/B0B,KAAK,EAAE,SAASiC,wBAAwBA,CAACC,SAAS,EAAEZ,KAAK,EAAE;QACzD,OAAOxD,QAAQ,CAAC,CAAC,CAAC,EAAEyC,KAAK,CAACa,OAAO,CAACc,SAAS,CAAC3B,KAAK,EAAEe,KAAK,CAACC,MAAM,CAAC,CAAC;MACnE;IACF,CAAC,CAAC,CAAC;IAEH,OAAOb,WAAW;EACpB,CAAC,CAACL,aAAa,IAAID,SAAS,CAAC;EAE7BM,WAAW,CAACyB,SAAS,GAAGrE,QAAQ,CAAC,CAAC,CAAC,EAAE2C,MAAM,CAAC0B,SAAS,CAAC;EAEtDzB,WAAW,CAAC0B,YAAY,GAAGtE,QAAQ,CAAC,CAAC,CAAC,EAAE2C,MAAM,CAAC2B,YAAY,EAAE;IAC3D7B,KAAK,EAAE;MACLc,CAAC,EAAE,GAAG;MACNgB,CAAC,EAAE,IAAI;MACPC,CAAC,EAAE,IAAI;MACPC,CAAC,EAAE;IACL;EACF,CAAC,CAAC;EAEF,OAAO7B,WAAW;AACpB,CAAC;AAED,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}