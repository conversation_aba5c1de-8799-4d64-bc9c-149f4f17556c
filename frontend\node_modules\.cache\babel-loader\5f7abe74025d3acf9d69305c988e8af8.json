{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\components\\\\TicketsManagerTabs\\\\index.js\";\nimport React, { useContext, useEffect, useRef, useState } from \"react\";\nimport { useHistory } from \"react-router-dom\";\nimport { makeStyles } from \"@material-ui/core/styles\";\nimport Paper from \"@material-ui/core/Paper\";\nimport SearchIcon from \"@material-ui/icons/Search\";\nimport InputBase from \"@material-ui/core/InputBase\";\nimport Tabs from \"@material-ui/core/Tabs\";\nimport Tab from \"@material-ui/core/Tab\";\nimport Badge from \"@material-ui/core/Badge\";\nimport MoveToInboxIcon from \"@material-ui/icons/MoveToInbox\";\nimport CheckBoxIcon from \"@material-ui/icons/CheckBox\";\nimport FormControlLabel from \"@material-ui/core/FormControlLabel\";\nimport Switch from \"@material-ui/core/Switch\";\nimport NewTicketModal from \"../NewTicketModal\";\nimport TicketsList from \"../TicketsListCustom\";\nimport TabPanel from \"../TabPanel\";\nimport { i18n } from \"../../translate/i18n\";\nimport { AuthContext } from \"../../context/Auth/AuthContext\";\nimport { Can } from \"../Can\";\nimport TicketsQueueSelect from \"../TicketsQueueSelect\";\nimport { Button } from \"@material-ui/core\";\nimport { TagsFilter } from \"../TagsFilter\";\nimport { UsersFilter } from \"../UsersFilter\";\nconst useStyles = makeStyles(theme => ({\n  ticketsWrapper: {\n    position: \"relative\",\n    display: \"flex\",\n    height: \"100%\",\n    flexDirection: \"column\",\n    overflow: \"hidden\",\n    backgroundColor: 'white'\n  },\n  tabsHeader: {\n    flex: \"none\",\n    backgroundColor: \"white\",\n    borderBottom: '1px solid rgba(0,0,0,0.06)',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.04)'\n  },\n  settingsIcon: {\n    alignSelf: \"center\",\n    marginLeft: \"auto\",\n    padding: 8,\n    color: theme.palette.text.secondary,\n    '&:hover': {\n      backgroundColor: 'rgba(102, 126, 234, 0.08)',\n      color: theme.palette.primary.main\n    }\n  },\n  tab: {\n    minWidth: 120,\n    width: 120,\n    textTransform: 'none',\n    fontWeight: 600,\n    fontSize: '0.875rem',\n    '&.Mui-selected': {\n      color: theme.palette.primary.main\n    }\n  },\n  ticketOptionsBox: {\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    background: \"white\",\n    padding: theme.spacing(2),\n    borderBottom: '1px solid rgba(0,0,0,0.06)'\n  },\n  serachInputWrapper: {\n    flex: 1,\n    background: theme.palette.background.default,\n    display: \"flex\",\n    borderRadius: 12,\n    padding: '8px 12px',\n    marginRight: theme.spacing(1),\n    border: '1px solid rgba(0,0,0,0.08)',\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      borderColor: 'rgba(0,0,0,0.12)'\n    },\n    '&:focus-within': {\n      borderColor: theme.palette.primary.main,\n      boxShadow: `0 0 0 3px ${theme.palette.primary.main}20`\n    }\n  },\n  searchIcon: {\n    color: theme.palette.text.secondary,\n    marginRight: 8,\n    alignSelf: \"center\"\n  },\n  searchInput: {\n    flex: 1,\n    border: \"none\",\n    fontSize: '0.875rem',\n    '&::placeholder': {\n      color: theme.palette.text.secondary\n    }\n  },\n  badge: {\n    right: \"-10px\",\n    '& .MuiBadge-badge': {\n      backgroundColor: theme.palette.primary.main,\n      color: 'white',\n      fontSize: '0.75rem',\n      minWidth: 18,\n      height: 18\n    }\n  },\n  show: {\n    display: \"block\"\n  },\n  hide: {\n    display: \"none !important\"\n  },\n  modernButton: {\n    borderRadius: 8,\n    textTransform: 'none',\n    fontWeight: 600,\n    padding: '6px 16px',\n    fontSize: '0.875rem',\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    '&:hover': {\n      transform: 'translateY(-1px)',\n      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)'\n    }\n  }\n}));\nconst TicketsManagerTabs = () => {\n  const classes = useStyles();\n  const history = useHistory();\n  const [searchParam, setSearchParam] = useState(\"\");\n  const [tab, setTab] = useState(\"open\");\n  const [tabOpen, setTabOpen] = useState(\"open\");\n  const [newTicketModalOpen, setNewTicketModalOpen] = useState(false);\n  const [showAllTickets, setShowAllTickets] = useState(false);\n  const searchInputRef = useRef();\n  const {\n    user\n  } = useContext(AuthContext);\n  const {\n    profile\n  } = user;\n  const [openCount, setOpenCount] = useState(0);\n  const [pendingCount, setPendingCount] = useState(0);\n  const userQueueIds = user.queues.map(q => q.id);\n  const [selectedQueueIds, setSelectedQueueIds] = useState(userQueueIds || []);\n  const [selectedTags, setSelectedTags] = useState([]);\n  const [selectedUsers, setSelectedUsers] = useState([]);\n  useEffect(() => {\n    if (user.profile.toUpperCase() === \"ADMIN\") {\n      setShowAllTickets(true);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  useEffect(() => {\n    if (tab === \"search\") {\n      searchInputRef.current.focus();\n    }\n  }, [tab]);\n  let searchTimeout;\n  const handleSearch = e => {\n    const searchedTerm = e.target.value.toLowerCase();\n    clearTimeout(searchTimeout);\n    if (searchedTerm === \"\") {\n      setSearchParam(searchedTerm);\n      setTab(\"open\");\n      return;\n    }\n    searchTimeout = setTimeout(() => {\n      setSearchParam(searchedTerm);\n    }, 500);\n  };\n  const handleChangeTab = (e, newValue) => {\n    setTab(newValue);\n  };\n  const handleChangeTabOpen = (e, newValue) => {\n    setTabOpen(newValue);\n  };\n  const applyPanelStyle = status => {\n    if (tabOpen !== status) {\n      return {\n        width: 0,\n        height: 0\n      };\n    }\n  };\n  const handleCloseOrOpenTicket = ticket => {\n    setNewTicketModalOpen(false);\n    if (ticket !== undefined && ticket.uuid !== undefined) {\n      history.push(`/tickets/${ticket.uuid}`);\n    }\n  };\n  const handleSelectedTags = selecteds => {\n    const tags = selecteds.map(t => t.id);\n    setSelectedTags(tags);\n  };\n  const handleSelectedUsers = selecteds => {\n    const users = selecteds.map(t => t.id);\n    setSelectedUsers(users);\n  };\n  return /*#__PURE__*/React.createElement(Paper, {\n    elevation: 0,\n    variant: \"outlined\",\n    className: classes.ticketsWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(NewTicketModal, {\n    modalOpen: newTicketModalOpen,\n    onClose: ticket => {\n      handleCloseOrOpenTicket(ticket);\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(Paper, {\n    elevation: 0,\n    square: true,\n    className: classes.tabsHeader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Tabs, {\n    value: tab,\n    onChange: handleChangeTab,\n    variant: \"fullWidth\",\n    indicatorColor: \"primary\",\n    textColor: \"primary\",\n    \"aria-label\": \"icon label tabs example\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Tab, {\n    value: \"open\",\n    icon: /*#__PURE__*/React.createElement(MoveToInboxIcon, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 19\n      }\n    }),\n    label: i18n.t(\"tickets.tabs.open.title\"),\n    classes: {\n      root: classes.tab\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(Tab, {\n    value: \"closed\",\n    icon: /*#__PURE__*/React.createElement(CheckBoxIcon, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 19\n      }\n    }),\n    label: i18n.t(\"tickets.tabs.closed.title\"),\n    classes: {\n      root: classes.tab\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(Tab, {\n    value: \"search\",\n    icon: /*#__PURE__*/React.createElement(SearchIcon, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 19\n      }\n    }),\n    label: i18n.t(\"tickets.tabs.search.title\"),\n    classes: {\n      root: classes.tab\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 11\n    }\n  }))), /*#__PURE__*/React.createElement(Paper, {\n    square: true,\n    elevation: 0,\n    className: classes.ticketOptionsBox,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }\n  }, tab === \"search\" ? /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.serachInputWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(SearchIcon, {\n    className: classes.searchIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(InputBase, {\n    className: classes.searchInput,\n    inputRef: searchInputRef,\n    placeholder: i18n.t(\"tickets.search.placeholder\"),\n    type: \"search\",\n    onChange: handleSearch,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 13\n    }\n  })) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Button, {\n    variant: \"outlined\",\n    color: \"primary\",\n    onClick: () => setNewTicketModalOpen(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 13\n    }\n  }, i18n.t(\"ticketsManager.buttons.newTicket\")), /*#__PURE__*/React.createElement(Can, {\n    role: user.profile,\n    perform: \"tickets-manager:showall\",\n    yes: () => /*#__PURE__*/React.createElement(FormControlLabel, {\n      label: i18n.t(\"tickets.buttons.showAll\"),\n      labelPlacement: \"start\",\n      control: /*#__PURE__*/React.createElement(Switch, {\n        size: \"small\",\n        checked: showAllTickets,\n        onChange: () => setShowAllTickets(prevState => !prevState),\n        name: \"showAllTickets\",\n        color: \"primary\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 21\n        }\n      }),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 17\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 13\n    }\n  })), /*#__PURE__*/React.createElement(TicketsQueueSelect, {\n    style: {\n      marginLeft: 6\n    },\n    selectedQueueIds: selectedQueueIds,\n    userQueues: user === null || user === void 0 ? void 0 : user.queues,\n    onChange: values => setSelectedQueueIds(values),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 9\n    }\n  })), /*#__PURE__*/React.createElement(TabPanel, {\n    value: tab,\n    name: \"open\",\n    className: classes.ticketsWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Tabs, {\n    value: tabOpen,\n    onChange: handleChangeTabOpen,\n    indicatorColor: \"primary\",\n    textColor: \"primary\",\n    variant: \"fullWidth\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Tab, {\n    label: /*#__PURE__*/React.createElement(Badge, {\n      className: classes.badge,\n      badgeContent: openCount,\n      color: \"primary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 15\n      }\n    }, i18n.t(\"ticketsList.assignedHeader\")),\n    value: \"open\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(Tab, {\n    label: /*#__PURE__*/React.createElement(Badge, {\n      className: classes.badge,\n      badgeContent: pendingCount,\n      color: \"secondary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 15\n      }\n    }, i18n.t(\"ticketsList.pendingHeader\")),\n    value: \"pending\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 11\n    }\n  })), /*#__PURE__*/React.createElement(Paper, {\n    className: classes.ticketsWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(TicketsList, {\n    status: \"open\",\n    showAll: showAllTickets,\n    selectedQueueIds: selectedQueueIds,\n    updateCount: val => setOpenCount(val),\n    style: applyPanelStyle(\"open\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(TicketsList, {\n    status: \"pending\",\n    selectedQueueIds: selectedQueueIds,\n    updateCount: val => setPendingCount(val),\n    style: applyPanelStyle(\"pending\"),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 11\n    }\n  }))), /*#__PURE__*/React.createElement(TabPanel, {\n    value: tab,\n    name: \"closed\",\n    className: classes.ticketsWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(TicketsList, {\n    status: \"closed\",\n    showAll: true,\n    selectedQueueIds: selectedQueueIds,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 9\n    }\n  })), /*#__PURE__*/React.createElement(TabPanel, {\n    value: tab,\n    name: \"search\",\n    className: classes.ticketsWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(TagsFilter, {\n    onFiltered: handleSelectedTags,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 9\n    }\n  }), profile === \"admin\" && /*#__PURE__*/React.createElement(UsersFilter, {\n    onFiltered: handleSelectedUsers,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(TicketsList, {\n    searchParam: searchParam,\n    showAll: true,\n    tags: selectedTags,\n    users: selectedUsers,\n    selectedQueueIds: selectedQueueIds,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 9\n    }\n  })));\n};\nexport default TicketsManagerTabs;", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useRef", "useState", "useHistory", "makeStyles", "Paper", "SearchIcon", "InputBase", "Tabs", "Tab", "Badge", "MoveToInboxIcon", "CheckBoxIcon", "FormControlLabel", "Switch", "NewTicketModal", "TicketsList", "TabPanel", "i18n", "AuthContext", "Can", "TicketsQueueSelect", "<PERSON><PERSON>", "TagsFilter", "UsersFilter", "useStyles", "theme", "ticketsWrapper", "position", "display", "height", "flexDirection", "overflow", "backgroundColor", "tabsHeader", "flex", "borderBottom", "boxShadow", "settingsIcon", "alignSelf", "marginLeft", "padding", "color", "palette", "text", "secondary", "primary", "main", "tab", "min<PERSON><PERSON><PERSON>", "width", "textTransform", "fontWeight", "fontSize", "ticketOptionsBox", "justifyContent", "alignItems", "background", "spacing", "serachInputWrapper", "default", "borderRadius", "marginRight", "border", "transition", "borderColor", "searchIcon", "searchInput", "badge", "right", "show", "hide", "modernButton", "transform", "TicketsManagerTabs", "classes", "history", "searchParam", "setSearchParam", "setTab", "tabOpen", "setTabOpen", "newTicketModalOpen", "setNewTicketModalOpen", "showAllTickets", "setShowAllTickets", "searchInputRef", "user", "profile", "openCount", "setOpenCount", "pendingCount", "setPendingCount", "userQueueIds", "queues", "map", "q", "id", "selectedQueueIds", "setSelectedQueueIds", "selectedTags", "setSelectedTags", "selectedUsers", "setSelectedUsers", "toUpperCase", "current", "focus", "searchTimeout", "handleSearch", "e", "searchedTerm", "target", "value", "toLowerCase", "clearTimeout", "setTimeout", "handleChangeTab", "newValue", "handleChangeTabOpen", "applyPanelStyle", "status", "handleCloseOrOpenTicket", "ticket", "undefined", "uuid", "push", "handleSelectedTags", "selecteds", "tags", "t", "handleSelectedUsers", "users", "createElement", "elevation", "variant", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "modalOpen", "onClose", "square", "onChange", "indicatorColor", "textColor", "icon", "label", "root", "inputRef", "placeholder", "type", "Fragment", "onClick", "role", "perform", "yes", "labelPlacement", "control", "size", "checked", "prevState", "name", "style", "userQueues", "values", "badgeContent", "showAll", "updateCount", "val", "onFiltered"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/components/TicketsManagerTabs/index.js"], "sourcesContent": ["import React, { useContext, useEffect, useRef, useState } from \"react\";\r\nimport { useHistory } from \"react-router-dom\";\r\n\r\nimport { makeStyles } from \"@material-ui/core/styles\";\r\nimport Paper from \"@material-ui/core/Paper\";\r\nimport SearchIcon from \"@material-ui/icons/Search\";\r\nimport InputBase from \"@material-ui/core/InputBase\";\r\nimport Tabs from \"@material-ui/core/Tabs\";\r\nimport Tab from \"@material-ui/core/Tab\";\r\nimport Badge from \"@material-ui/core/Badge\";\r\nimport MoveToInboxIcon from \"@material-ui/icons/MoveToInbox\";\r\nimport CheckBoxIcon from \"@material-ui/icons/CheckBox\";\r\n\r\nimport FormControlLabel from \"@material-ui/core/FormControlLabel\";\r\nimport Switch from \"@material-ui/core/Switch\";\r\n\r\nimport NewTicketModal from \"../NewTicketModal\";\r\nimport TicketsList from \"../TicketsListCustom\";\r\nimport TabPanel from \"../TabPanel\";\r\n\r\nimport { i18n } from \"../../translate/i18n\";\r\nimport { AuthContext } from \"../../context/Auth/AuthContext\";\r\nimport { Can } from \"../Can\";\r\nimport TicketsQueueSelect from \"../TicketsQueueSelect\";\r\nimport { Button } from \"@material-ui/core\";\r\nimport { TagsFilter } from \"../TagsFilter\";\r\nimport { UsersFilter } from \"../UsersFilter\";\r\n\r\nconst useStyles = makeStyles((theme) => ({\r\n  ticketsWrapper: {\r\n    position: \"relative\",\r\n    display: \"flex\",\r\n    height: \"100%\",\r\n    flexDirection: \"column\",\r\n    overflow: \"hidden\",\r\n    backgroundColor: 'white',\r\n  },\r\n\r\n  tabsHeader: {\r\n    flex: \"none\",\r\n    backgroundColor: \"white\",\r\n    borderBottom: '1px solid rgba(0,0,0,0.06)',\r\n    boxShadow: '0 2px 8px rgba(0,0,0,0.04)',\r\n  },\r\n\r\n  settingsIcon: {\r\n    alignSelf: \"center\",\r\n    marginLeft: \"auto\",\r\n    padding: 8,\r\n    color: theme.palette.text.secondary,\r\n    '&:hover': {\r\n      backgroundColor: 'rgba(102, 126, 234, 0.08)',\r\n      color: theme.palette.primary.main,\r\n    }\r\n  },\r\n\r\n  tab: {\r\n    minWidth: 120,\r\n    width: 120,\r\n    textTransform: 'none',\r\n    fontWeight: 600,\r\n    fontSize: '0.875rem',\r\n    '&.Mui-selected': {\r\n      color: theme.palette.primary.main,\r\n    }\r\n  },\r\n\r\n  ticketOptionsBox: {\r\n    display: \"flex\",\r\n    justifyContent: \"space-between\",\r\n    alignItems: \"center\",\r\n    background: \"white\",\r\n    padding: theme.spacing(2),\r\n    borderBottom: '1px solid rgba(0,0,0,0.06)',\r\n  },\r\n\r\n  serachInputWrapper: {\r\n    flex: 1,\r\n    background: theme.palette.background.default,\r\n    display: \"flex\",\r\n    borderRadius: 12,\r\n    padding: '8px 12px',\r\n    marginRight: theme.spacing(1),\r\n    border: '1px solid rgba(0,0,0,0.08)',\r\n    transition: 'all 0.2s ease-in-out',\r\n    '&:hover': {\r\n      borderColor: 'rgba(0,0,0,0.12)',\r\n    },\r\n    '&:focus-within': {\r\n      borderColor: theme.palette.primary.main,\r\n      boxShadow: `0 0 0 3px ${theme.palette.primary.main}20`,\r\n    }\r\n  },\r\n\r\n  searchIcon: {\r\n    color: theme.palette.text.secondary,\r\n    marginRight: 8,\r\n    alignSelf: \"center\",\r\n  },\r\n\r\n  searchInput: {\r\n    flex: 1,\r\n    border: \"none\",\r\n    fontSize: '0.875rem',\r\n    '&::placeholder': {\r\n      color: theme.palette.text.secondary,\r\n    }\r\n  },\r\n\r\n  badge: {\r\n    right: \"-10px\",\r\n    '& .MuiBadge-badge': {\r\n      backgroundColor: theme.palette.primary.main,\r\n      color: 'white',\r\n      fontSize: '0.75rem',\r\n      minWidth: 18,\r\n      height: 18,\r\n    }\r\n  },\r\n  show: {\r\n    display: \"block\",\r\n  },\r\n  hide: {\r\n    display: \"none !important\",\r\n  },\r\n  modernButton: {\r\n    borderRadius: 8,\r\n    textTransform: 'none',\r\n    fontWeight: 600,\r\n    padding: '6px 16px',\r\n    fontSize: '0.875rem',\r\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n    '&:hover': {\r\n      transform: 'translateY(-1px)',\r\n      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',\r\n    }\r\n  },\r\n}));\r\n\r\nconst TicketsManagerTabs = () => {\r\n  const classes = useStyles();\r\n  const history = useHistory();\r\n\r\n  const [searchParam, setSearchParam] = useState(\"\");\r\n  const [tab, setTab] = useState(\"open\");\r\n  const [tabOpen, setTabOpen] = useState(\"open\");\r\n  const [newTicketModalOpen, setNewTicketModalOpen] = useState(false);\r\n  const [showAllTickets, setShowAllTickets] = useState(false);\r\n  const searchInputRef = useRef();\r\n  const { user } = useContext(AuthContext);\r\n  const { profile } = user;\r\n\r\n  const [openCount, setOpenCount] = useState(0);\r\n  const [pendingCount, setPendingCount] = useState(0);\r\n\r\n  const userQueueIds = user.queues.map((q) => q.id);\r\n  const [selectedQueueIds, setSelectedQueueIds] = useState(userQueueIds || []);\r\n  const [selectedTags, setSelectedTags] = useState([]);\r\n  const [selectedUsers, setSelectedUsers] = useState([]);\r\n\r\n  useEffect(() => {\r\n    if (user.profile.toUpperCase() === \"ADMIN\") {\r\n      setShowAllTickets(true);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (tab === \"search\") {\r\n      searchInputRef.current.focus();\r\n    }\r\n  }, [tab]);\r\n\r\n  let searchTimeout;\r\n\r\n  const handleSearch = (e) => {\r\n    const searchedTerm = e.target.value.toLowerCase();\r\n\r\n    clearTimeout(searchTimeout);\r\n\r\n    if (searchedTerm === \"\") {\r\n      setSearchParam(searchedTerm);\r\n      setTab(\"open\");\r\n      return;\r\n    }\r\n\r\n    searchTimeout = setTimeout(() => {\r\n      setSearchParam(searchedTerm);\r\n    }, 500);\r\n  };\r\n\r\n  const handleChangeTab = (e, newValue) => {\r\n    setTab(newValue);\r\n  };\r\n\r\n  const handleChangeTabOpen = (e, newValue) => {\r\n    setTabOpen(newValue);\r\n  };\r\n\r\n  const applyPanelStyle = (status) => {\r\n    if (tabOpen !== status) {\r\n      return { width: 0, height: 0 };\r\n    }\r\n  };\r\n\r\n  const handleCloseOrOpenTicket = (ticket) => {\r\n    setNewTicketModalOpen(false);\r\n    if (ticket !== undefined && ticket.uuid !== undefined) {\r\n      history.push(`/tickets/${ticket.uuid}`);\r\n    }\r\n  };\r\n\r\n  const handleSelectedTags = (selecteds) => {\r\n    const tags = selecteds.map((t) => t.id);\r\n    setSelectedTags(tags);\r\n  };\r\n\r\n  const handleSelectedUsers = (selecteds) => {\r\n    const users = selecteds.map((t) => t.id);\r\n    setSelectedUsers(users);\r\n  };\r\n\r\n  return (\r\n    <Paper elevation={0} variant=\"outlined\" className={classes.ticketsWrapper}>\r\n      <NewTicketModal\r\n        modalOpen={newTicketModalOpen}\r\n        onClose={(ticket) => {\r\n       \r\n          handleCloseOrOpenTicket(ticket);\r\n        }}\r\n      />\r\n      <Paper elevation={0} square className={classes.tabsHeader}>\r\n        <Tabs\r\n          value={tab}\r\n          onChange={handleChangeTab}\r\n          variant=\"fullWidth\"\r\n          indicatorColor=\"primary\"\r\n          textColor=\"primary\"\r\n          aria-label=\"icon label tabs example\"\r\n        >\r\n          <Tab\r\n            value={\"open\"}\r\n            icon={<MoveToInboxIcon />}\r\n            label={i18n.t(\"tickets.tabs.open.title\")}\r\n            classes={{ root: classes.tab }}\r\n          />\r\n          <Tab\r\n            value={\"closed\"}\r\n            icon={<CheckBoxIcon />}\r\n            label={i18n.t(\"tickets.tabs.closed.title\")}\r\n            classes={{ root: classes.tab }}\r\n          />\r\n          <Tab\r\n            value={\"search\"}\r\n            icon={<SearchIcon />}\r\n            label={i18n.t(\"tickets.tabs.search.title\")}\r\n            classes={{ root: classes.tab }}\r\n          />\r\n        </Tabs>\r\n      </Paper>\r\n      <Paper square elevation={0} className={classes.ticketOptionsBox}>\r\n        {tab === \"search\" ? (\r\n          <div className={classes.serachInputWrapper}>\r\n            <SearchIcon className={classes.searchIcon} />\r\n            <InputBase\r\n              className={classes.searchInput}\r\n              inputRef={searchInputRef}\r\n              placeholder={i18n.t(\"tickets.search.placeholder\")}\r\n              type=\"search\"\r\n              onChange={handleSearch}\r\n            />\r\n          </div>\r\n        ) : (\r\n          <>\r\n            <Button\r\n              variant=\"outlined\"\r\n              color=\"primary\"\r\n              onClick={() => setNewTicketModalOpen(true)}\r\n            >\r\n              {i18n.t(\"ticketsManager.buttons.newTicket\")}\r\n            </Button>\r\n            <Can\r\n              role={user.profile}\r\n              perform=\"tickets-manager:showall\"\r\n              yes={() => (\r\n                <FormControlLabel\r\n                  label={i18n.t(\"tickets.buttons.showAll\")}\r\n                  labelPlacement=\"start\"\r\n                  control={\r\n                    <Switch\r\n                      size=\"small\"\r\n                      checked={showAllTickets}\r\n                      onChange={() =>\r\n                        setShowAllTickets((prevState) => !prevState)\r\n                      }\r\n                      name=\"showAllTickets\"\r\n                      color=\"primary\"\r\n                    />\r\n                  }\r\n                />\r\n              )}\r\n            />\r\n          </>\r\n        )}\r\n        <TicketsQueueSelect\r\n          style={{ marginLeft: 6 }}\r\n          selectedQueueIds={selectedQueueIds}\r\n          userQueues={user?.queues}\r\n          onChange={(values) => setSelectedQueueIds(values)}\r\n        />\r\n      </Paper>\r\n      <TabPanel value={tab} name=\"open\" className={classes.ticketsWrapper}>\r\n        <Tabs\r\n          value={tabOpen}\r\n          onChange={handleChangeTabOpen}\r\n          indicatorColor=\"primary\"\r\n          textColor=\"primary\"\r\n          variant=\"fullWidth\"\r\n        >\r\n          <Tab\r\n            label={\r\n              <Badge\r\n                className={classes.badge}\r\n                badgeContent={openCount}\r\n                color=\"primary\"\r\n              >\r\n                {i18n.t(\"ticketsList.assignedHeader\")}\r\n              </Badge>\r\n            }\r\n            value={\"open\"}\r\n          />\r\n          <Tab\r\n            label={\r\n              <Badge\r\n                className={classes.badge}\r\n                badgeContent={pendingCount}\r\n                color=\"secondary\"\r\n              >\r\n                {i18n.t(\"ticketsList.pendingHeader\")}\r\n              </Badge>\r\n            }\r\n            value={\"pending\"}\r\n          />\r\n        </Tabs>\r\n        <Paper className={classes.ticketsWrapper}>\r\n          <TicketsList\r\n            status=\"open\"\r\n            showAll={showAllTickets}\r\n            selectedQueueIds={selectedQueueIds}\r\n            updateCount={(val) => setOpenCount(val)}\r\n            style={applyPanelStyle(\"open\")}\r\n          />\r\n          <TicketsList\r\n            status=\"pending\"\r\n            selectedQueueIds={selectedQueueIds}\r\n            updateCount={(val) => setPendingCount(val)}\r\n            style={applyPanelStyle(\"pending\")}\r\n          />\r\n        </Paper>\r\n      </TabPanel>\r\n      <TabPanel value={tab} name=\"closed\" className={classes.ticketsWrapper}>\r\n        <TicketsList\r\n          status=\"closed\"\r\n          showAll={true}\r\n          selectedQueueIds={selectedQueueIds}\r\n        />\r\n      </TabPanel>\r\n      <TabPanel value={tab} name=\"search\" className={classes.ticketsWrapper}>\r\n        <TagsFilter onFiltered={handleSelectedTags} />\r\n        {profile === \"admin\" && (\r\n          <UsersFilter onFiltered={handleSelectedUsers} />\r\n        )}\r\n        <TicketsList\r\n          searchParam={searchParam}\r\n          showAll={true}\r\n          tags={selectedTags}\r\n          users={selectedUsers}\r\n          selectedQueueIds={selectedQueueIds}\r\n        />\r\n      </TabPanel>\r\n    </Paper>\r\n  );\r\n};\r\n\r\nexport default TicketsManagerTabs;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACtE,SAASC,UAAU,QAAQ,kBAAkB;AAE7C,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,IAAI,MAAM,wBAAwB;AACzC,OAAOC,GAAG,MAAM,uBAAuB;AACvC,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,OAAOC,YAAY,MAAM,6BAA6B;AAEtD,OAAOC,gBAAgB,MAAM,oCAAoC;AACjE,OAAOC,MAAM,MAAM,0BAA0B;AAE7C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,QAAQ,MAAM,aAAa;AAElC,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,GAAG,QAAQ,QAAQ;AAC5B,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,MAAMC,SAAS,GAAGrB,UAAU,CAAEsB,KAAK,KAAM;EACvCC,cAAc,EAAE;IACdC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,MAAM;IACdC,aAAa,EAAE,QAAQ;IACvBC,QAAQ,EAAE,QAAQ;IAClBC,eAAe,EAAE;EACnB,CAAC;EAEDC,UAAU,EAAE;IACVC,IAAI,EAAE,MAAM;IACZF,eAAe,EAAE,OAAO;IACxBG,YAAY,EAAE,4BAA4B;IAC1CC,SAAS,EAAE;EACb,CAAC;EAEDC,YAAY,EAAE;IACZC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,MAAM;IAClBC,OAAO,EAAE,CAAC;IACVC,KAAK,EAAEhB,KAAK,CAACiB,OAAO,CAACC,IAAI,CAACC,SAAS;IACnC,SAAS,EAAE;MACTZ,eAAe,EAAE,2BAA2B;MAC5CS,KAAK,EAAEhB,KAAK,CAACiB,OAAO,CAACG,OAAO,CAACC;IAC/B;EACF,CAAC;EAEDC,GAAG,EAAE;IACHC,QAAQ,EAAE,GAAG;IACbC,KAAK,EAAE,GAAG;IACVC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,UAAU;IACpB,gBAAgB,EAAE;MAChBX,KAAK,EAAEhB,KAAK,CAACiB,OAAO,CAACG,OAAO,CAACC;IAC/B;EACF,CAAC;EAEDO,gBAAgB,EAAE;IAChBzB,OAAO,EAAE,MAAM;IACf0B,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE,OAAO;IACnBhB,OAAO,EAAEf,KAAK,CAACgC,OAAO,CAAC,CAAC,CAAC;IACzBtB,YAAY,EAAE;EAChB,CAAC;EAEDuB,kBAAkB,EAAE;IAClBxB,IAAI,EAAE,CAAC;IACPsB,UAAU,EAAE/B,KAAK,CAACiB,OAAO,CAACc,UAAU,CAACG,OAAO;IAC5C/B,OAAO,EAAE,MAAM;IACfgC,YAAY,EAAE,EAAE;IAChBpB,OAAO,EAAE,UAAU;IACnBqB,WAAW,EAAEpC,KAAK,CAACgC,OAAO,CAAC,CAAC,CAAC;IAC7BK,MAAM,EAAE,4BAA4B;IACpCC,UAAU,EAAE,sBAAsB;IAClC,SAAS,EAAE;MACTC,WAAW,EAAE;IACf,CAAC;IACD,gBAAgB,EAAE;MAChBA,WAAW,EAAEvC,KAAK,CAACiB,OAAO,CAACG,OAAO,CAACC,IAAI;MACvCV,SAAS,EAAE,aAAaX,KAAK,CAACiB,OAAO,CAACG,OAAO,CAACC,IAAI;IACpD;EACF,CAAC;EAEDmB,UAAU,EAAE;IACVxB,KAAK,EAAEhB,KAAK,CAACiB,OAAO,CAACC,IAAI,CAACC,SAAS;IACnCiB,WAAW,EAAE,CAAC;IACdvB,SAAS,EAAE;EACb,CAAC;EAED4B,WAAW,EAAE;IACXhC,IAAI,EAAE,CAAC;IACP4B,MAAM,EAAE,MAAM;IACdV,QAAQ,EAAE,UAAU;IACpB,gBAAgB,EAAE;MAChBX,KAAK,EAAEhB,KAAK,CAACiB,OAAO,CAACC,IAAI,CAACC;IAC5B;EACF,CAAC;EAEDuB,KAAK,EAAE;IACLC,KAAK,EAAE,OAAO;IACd,mBAAmB,EAAE;MACnBpC,eAAe,EAAEP,KAAK,CAACiB,OAAO,CAACG,OAAO,CAACC,IAAI;MAC3CL,KAAK,EAAE,OAAO;MACdW,QAAQ,EAAE,SAAS;MACnBJ,QAAQ,EAAE,EAAE;MACZnB,MAAM,EAAE;IACV;EACF,CAAC;EACDwC,IAAI,EAAE;IACJzC,OAAO,EAAE;EACX,CAAC;EACD0C,IAAI,EAAE;IACJ1C,OAAO,EAAE;EACX,CAAC;EACD2C,YAAY,EAAE;IACZX,YAAY,EAAE,CAAC;IACfV,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE,GAAG;IACfX,OAAO,EAAE,UAAU;IACnBY,QAAQ,EAAE,UAAU;IACpBI,UAAU,EAAE,mDAAmD;IAC/D,SAAS,EAAE;MACTgB,SAAS,EAAE,kBAAkB;MAC7BpC,SAAS,EAAE;IACb;EACF;AACF,CAAC,CAAC,CAAC;AAEH,MAAMqC,kBAAkB,GAAGA,CAAA,KAAM;EAC/B,MAAMC,OAAO,GAAGlD,SAAS,CAAC,CAAC;EAC3B,MAAMmD,OAAO,GAAGzE,UAAU,CAAC,CAAC;EAE5B,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8C,GAAG,EAAE+B,MAAM,CAAC,GAAG7E,QAAQ,CAAC,MAAM,CAAC;EACtC,MAAM,CAAC8E,OAAO,EAAEC,UAAU,CAAC,GAAG/E,QAAQ,CAAC,MAAM,CAAC;EAC9C,MAAM,CAACgF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACkF,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMoF,cAAc,GAAGrF,MAAM,CAAC,CAAC;EAC/B,MAAM;IAAEsF;EAAK,CAAC,GAAGxF,UAAU,CAACoB,WAAW,CAAC;EACxC,MAAM;IAAEqE;EAAQ,CAAC,GAAGD,IAAI;EAExB,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACyF,YAAY,EAAEC,eAAe,CAAC,GAAG1F,QAAQ,CAAC,CAAC,CAAC;EAEnD,MAAM2F,YAAY,GAAGN,IAAI,CAACO,MAAM,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,CAAC;EACjD,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjG,QAAQ,CAAC2F,YAAY,IAAI,EAAE,CAAC;EAC5E,MAAM,CAACO,YAAY,EAAEC,eAAe,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoG,aAAa,EAAEC,gBAAgB,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAEtDF,SAAS,CAAC,MAAM;IACd,IAAIuF,IAAI,CAACC,OAAO,CAACgB,WAAW,CAAC,CAAC,KAAK,OAAO,EAAE;MAC1CnB,iBAAiB,CAAC,IAAI,CAAC;IACzB;IACA;EACF,CAAC,EAAE,EAAE,CAAC;EAENrF,SAAS,CAAC,MAAM;IACd,IAAIgD,GAAG,KAAK,QAAQ,EAAE;MACpBsC,cAAc,CAACmB,OAAO,CAACC,KAAK,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAAC1D,GAAG,CAAC,CAAC;EAET,IAAI2D,aAAa;EAEjB,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAMC,YAAY,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC;IAEjDC,YAAY,CAACP,aAAa,CAAC;IAE3B,IAAIG,YAAY,KAAK,EAAE,EAAE;MACvBhC,cAAc,CAACgC,YAAY,CAAC;MAC5B/B,MAAM,CAAC,MAAM,CAAC;MACd;IACF;IAEA4B,aAAa,GAAGQ,UAAU,CAAC,MAAM;MAC/BrC,cAAc,CAACgC,YAAY,CAAC;IAC9B,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMM,eAAe,GAAGA,CAACP,CAAC,EAAEQ,QAAQ,KAAK;IACvCtC,MAAM,CAACsC,QAAQ,CAAC;EAClB,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAACT,CAAC,EAAEQ,QAAQ,KAAK;IAC3CpC,UAAU,CAACoC,QAAQ,CAAC;EACtB,CAAC;EAED,MAAME,eAAe,GAAIC,MAAM,IAAK;IAClC,IAAIxC,OAAO,KAAKwC,MAAM,EAAE;MACtB,OAAO;QAAEtE,KAAK,EAAE,CAAC;QAAEpB,MAAM,EAAE;MAAE,CAAC;IAChC;EACF,CAAC;EAED,MAAM2F,uBAAuB,GAAIC,MAAM,IAAK;IAC1CvC,qBAAqB,CAAC,KAAK,CAAC;IAC5B,IAAIuC,MAAM,KAAKC,SAAS,IAAID,MAAM,CAACE,IAAI,KAAKD,SAAS,EAAE;MACrD/C,OAAO,CAACiD,IAAI,CAAC,YAAYH,MAAM,CAACE,IAAI,EAAE,CAAC;IACzC;EACF,CAAC;EAED,MAAME,kBAAkB,GAAIC,SAAS,IAAK;IACxC,MAAMC,IAAI,GAAGD,SAAS,CAAChC,GAAG,CAAEkC,CAAC,IAAKA,CAAC,CAAChC,EAAE,CAAC;IACvCI,eAAe,CAAC2B,IAAI,CAAC;EACvB,CAAC;EAED,MAAME,mBAAmB,GAAIH,SAAS,IAAK;IACzC,MAAMI,KAAK,GAAGJ,SAAS,CAAChC,GAAG,CAAEkC,CAAC,IAAKA,CAAC,CAAChC,EAAE,CAAC;IACxCM,gBAAgB,CAAC4B,KAAK,CAAC;EACzB,CAAC;EAED,oBACErI,KAAA,CAAAsI,aAAA,CAAC/H,KAAK;IAACgI,SAAS,EAAE,CAAE;IAACC,OAAO,EAAC,UAAU;IAACC,SAAS,EAAE5D,OAAO,CAAChD,cAAe;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxE/I,KAAA,CAAAsI,aAAA,CAACrH,cAAc;IACb+H,SAAS,EAAE5D,kBAAmB;IAC9B6D,OAAO,EAAGrB,MAAM,IAAK;MAEnBD,uBAAuB,CAACC,MAAM,CAAC;IACjC,CAAE;IAAAc,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACH,CAAC,eACF/I,KAAA,CAAAsI,aAAA,CAAC/H,KAAK;IAACgI,SAAS,EAAE,CAAE;IAACW,MAAM;IAACT,SAAS,EAAE5D,OAAO,CAACzC,UAAW;IAAAsG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxD/I,KAAA,CAAAsI,aAAA,CAAC5H,IAAI;IACHwG,KAAK,EAAEhE,GAAI;IACXiG,QAAQ,EAAE7B,eAAgB;IAC1BkB,OAAO,EAAC,WAAW;IACnBY,cAAc,EAAC,SAAS;IACxBC,SAAS,EAAC,SAAS;IACnB,cAAW,yBAAyB;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpC/I,KAAA,CAAAsI,aAAA,CAAC3H,GAAG;IACFuG,KAAK,EAAE,MAAO;IACdoC,IAAI,eAAEtJ,KAAA,CAAAsI,aAAA,CAACzH,eAAe;MAAA6H,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAC1BQ,KAAK,EAAEnI,IAAI,CAAC+G,CAAC,CAAC,yBAAyB,CAAE;IACzCtD,OAAO,EAAE;MAAE2E,IAAI,EAAE3E,OAAO,CAAC3B;IAAI,CAAE;IAAAwF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAChC,CAAC,eACF/I,KAAA,CAAAsI,aAAA,CAAC3H,GAAG;IACFuG,KAAK,EAAE,QAAS;IAChBoC,IAAI,eAAEtJ,KAAA,CAAAsI,aAAA,CAACxH,YAAY;MAAA4H,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IACvBQ,KAAK,EAAEnI,IAAI,CAAC+G,CAAC,CAAC,2BAA2B,CAAE;IAC3CtD,OAAO,EAAE;MAAE2E,IAAI,EAAE3E,OAAO,CAAC3B;IAAI,CAAE;IAAAwF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAChC,CAAC,eACF/I,KAAA,CAAAsI,aAAA,CAAC3H,GAAG;IACFuG,KAAK,EAAE,QAAS;IAChBoC,IAAI,eAAEtJ,KAAA,CAAAsI,aAAA,CAAC9H,UAAU;MAAAkI,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IACrBQ,KAAK,EAAEnI,IAAI,CAAC+G,CAAC,CAAC,2BAA2B,CAAE;IAC3CtD,OAAO,EAAE;MAAE2E,IAAI,EAAE3E,OAAO,CAAC3B;IAAI,CAAE;IAAAwF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAChC,CACG,CACD,CAAC,eACR/I,KAAA,CAAAsI,aAAA,CAAC/H,KAAK;IAAC2I,MAAM;IAACX,SAAS,EAAE,CAAE;IAACE,SAAS,EAAE5D,OAAO,CAACrB,gBAAiB;IAAAkF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7D7F,GAAG,KAAK,QAAQ,gBACflD,KAAA,CAAAsI,aAAA;IAAKG,SAAS,EAAE5D,OAAO,CAAChB,kBAAmB;IAAA6E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzC/I,KAAA,CAAAsI,aAAA,CAAC9H,UAAU;IAACiI,SAAS,EAAE5D,OAAO,CAACT,UAAW;IAAAsE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC7C/I,KAAA,CAAAsI,aAAA,CAAC7H,SAAS;IACRgI,SAAS,EAAE5D,OAAO,CAACR,WAAY;IAC/BoF,QAAQ,EAAEjE,cAAe;IACzBkE,WAAW,EAAEtI,IAAI,CAAC+G,CAAC,CAAC,4BAA4B,CAAE;IAClDwB,IAAI,EAAC,QAAQ;IACbR,QAAQ,EAAErC,YAAa;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACxB,CACE,CAAC,gBAEN/I,KAAA,CAAAsI,aAAA,CAAAtI,KAAA,CAAA4J,QAAA,qBACE5J,KAAA,CAAAsI,aAAA,CAAC9G,MAAM;IACLgH,OAAO,EAAC,UAAU;IAClB5F,KAAK,EAAC,SAAS;IACfiH,OAAO,EAAEA,CAAA,KAAMxE,qBAAqB,CAAC,IAAI,CAAE;IAAAqD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAE1C3H,IAAI,CAAC+G,CAAC,CAAC,kCAAkC,CACpC,CAAC,eACTnI,KAAA,CAAAsI,aAAA,CAAChH,GAAG;IACFwI,IAAI,EAAErE,IAAI,CAACC,OAAQ;IACnBqE,OAAO,EAAC,yBAAyB;IACjCC,GAAG,EAAEA,CAAA,kBACHhK,KAAA,CAAAsI,aAAA,CAACvH,gBAAgB;MACfwI,KAAK,EAAEnI,IAAI,CAAC+G,CAAC,CAAC,yBAAyB,CAAE;MACzC8B,cAAc,EAAC,OAAO;MACtBC,OAAO,eACLlK,KAAA,CAAAsI,aAAA,CAACtH,MAAM;QACLmJ,IAAI,EAAC,OAAO;QACZC,OAAO,EAAE9E,cAAe;QACxB6D,QAAQ,EAAEA,CAAA,KACR5D,iBAAiB,CAAE8E,SAAS,IAAK,CAACA,SAAS,CAC5C;QACDC,IAAI,EAAC,gBAAgB;QACrB1H,KAAK,EAAC,SAAS;QAAA8F,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAChB,CACF;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACF,CACD;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACH,CACD,CACH,eACD/I,KAAA,CAAAsI,aAAA,CAAC/G,kBAAkB;IACjBgJ,KAAK,EAAE;MAAE7H,UAAU,EAAE;IAAE,CAAE;IACzB0D,gBAAgB,EAAEA,gBAAiB;IACnCoE,UAAU,EAAE/E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,MAAO;IACzBmD,QAAQ,EAAGsB,MAAM,IAAKpE,mBAAmB,CAACoE,MAAM,CAAE;IAAA/B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACnD,CACI,CAAC,eACR/I,KAAA,CAAAsI,aAAA,CAACnH,QAAQ;IAAC+F,KAAK,EAAEhE,GAAI;IAACoH,IAAI,EAAC,MAAM;IAAC7B,SAAS,EAAE5D,OAAO,CAAChD,cAAe;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClE/I,KAAA,CAAAsI,aAAA,CAAC5H,IAAI;IACHwG,KAAK,EAAEhC,OAAQ;IACfiE,QAAQ,EAAE3B,mBAAoB;IAC9B4B,cAAc,EAAC,SAAS;IACxBC,SAAS,EAAC,SAAS;IACnBb,OAAO,EAAC,WAAW;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEnB/I,KAAA,CAAAsI,aAAA,CAAC3H,GAAG;IACF4I,KAAK,eACHvJ,KAAA,CAAAsI,aAAA,CAAC1H,KAAK;MACJ6H,SAAS,EAAE5D,OAAO,CAACP,KAAM;MACzBoG,YAAY,EAAE/E,SAAU;MACxB/C,KAAK,EAAC,SAAS;MAAA8F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEd3H,IAAI,CAAC+G,CAAC,CAAC,4BAA4B,CAC/B,CACR;IACDjB,KAAK,EAAE,MAAO;IAAAwB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACf,CAAC,eACF/I,KAAA,CAAAsI,aAAA,CAAC3H,GAAG;IACF4I,KAAK,eACHvJ,KAAA,CAAAsI,aAAA,CAAC1H,KAAK;MACJ6H,SAAS,EAAE5D,OAAO,CAACP,KAAM;MACzBoG,YAAY,EAAE7E,YAAa;MAC3BjD,KAAK,EAAC,WAAW;MAAA8F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEhB3H,IAAI,CAAC+G,CAAC,CAAC,2BAA2B,CAC9B,CACR;IACDjB,KAAK,EAAE,SAAU;IAAAwB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAClB,CACG,CAAC,eACP/I,KAAA,CAAAsI,aAAA,CAAC/H,KAAK;IAACkI,SAAS,EAAE5D,OAAO,CAAChD,cAAe;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvC/I,KAAA,CAAAsI,aAAA,CAACpH,WAAW;IACVwG,MAAM,EAAC,MAAM;IACbiD,OAAO,EAAErF,cAAe;IACxBc,gBAAgB,EAAEA,gBAAiB;IACnCwE,WAAW,EAAGC,GAAG,IAAKjF,YAAY,CAACiF,GAAG,CAAE;IACxCN,KAAK,EAAE9C,eAAe,CAAC,MAAM,CAAE;IAAAiB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAChC,CAAC,eACF/I,KAAA,CAAAsI,aAAA,CAACpH,WAAW;IACVwG,MAAM,EAAC,SAAS;IAChBtB,gBAAgB,EAAEA,gBAAiB;IACnCwE,WAAW,EAAGC,GAAG,IAAK/E,eAAe,CAAC+E,GAAG,CAAE;IAC3CN,KAAK,EAAE9C,eAAe,CAAC,SAAS,CAAE;IAAAiB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACnC,CACI,CACC,CAAC,eACX/I,KAAA,CAAAsI,aAAA,CAACnH,QAAQ;IAAC+F,KAAK,EAAEhE,GAAI;IAACoH,IAAI,EAAC,QAAQ;IAAC7B,SAAS,EAAE5D,OAAO,CAAChD,cAAe;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpE/I,KAAA,CAAAsI,aAAA,CAACpH,WAAW;IACVwG,MAAM,EAAC,QAAQ;IACfiD,OAAO,EAAE,IAAK;IACdvE,gBAAgB,EAAEA,gBAAiB;IAAAsC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACpC,CACO,CAAC,eACX/I,KAAA,CAAAsI,aAAA,CAACnH,QAAQ;IAAC+F,KAAK,EAAEhE,GAAI;IAACoH,IAAI,EAAC,QAAQ;IAAC7B,SAAS,EAAE5D,OAAO,CAAChD,cAAe;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpE/I,KAAA,CAAAsI,aAAA,CAAC7G,UAAU;IAACqJ,UAAU,EAAE9C,kBAAmB;IAAAU,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,EAC7CrD,OAAO,KAAK,OAAO,iBAClB1F,KAAA,CAAAsI,aAAA,CAAC5G,WAAW;IAACoJ,UAAU,EAAE1C,mBAAoB;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChD,eACD/I,KAAA,CAAAsI,aAAA,CAACpH,WAAW;IACV6D,WAAW,EAAEA,WAAY;IACzB4F,OAAO,EAAE,IAAK;IACdzC,IAAI,EAAE5B,YAAa;IACnB+B,KAAK,EAAE7B,aAAc;IACrBJ,gBAAgB,EAAEA,gBAAiB;IAAAsC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACpC,CACO,CACL,CAAC;AAEZ,CAAC;AAED,eAAenE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}