{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\pages\\\\Queues\\\\index.js\";\nimport React, { useEffect, useReducer, useState } from \"react\";\nimport { IconButton, makeStyles, Paper, Table, TableBody, TableCell, TableHead, TableRow, Typography, Box, Chip } from \"@material-ui/core\";\nimport TableRowSkeleton from \"../../components/TableRowSkeleton\";\nimport { i18n } from \"../../translate/i18n\";\nimport toastError from \"../../errors/toastError\";\nimport api from \"../../services/api\";\nimport { DeleteOutline, Edit, Add as AddIcon, Queue as QueueIcon } from \"@material-ui/icons\";\nimport QueueModal from \"../../components/QueueModal\";\nimport { toast } from \"react-toastify\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { socketConnection } from \"../../services/socket\";\nimport ModernPageContainer from \"../../components/ModernPageContainer\";\nimport ModernButton from \"../../components/ModernButton\";\nconst useStyles = makeStyles(theme => ({\n  tableContainer: {\n    borderRadius: 16,\n    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n    border: '1px solid rgba(0,0,0,0.04)',\n    overflow: 'hidden'\n  },\n  tableHeader: {\n    backgroundColor: 'rgba(102, 126, 234, 0.05)',\n    '& .MuiTableCell-head': {\n      fontWeight: 600,\n      color: theme.palette.text.primary,\n      fontSize: '0.875rem'\n    }\n  },\n  tableRow: {\n    '&:hover': {\n      backgroundColor: 'rgba(102, 126, 234, 0.02)'\n    }\n  },\n  queueInfo: {\n    display: 'flex',\n    alignItems: 'center',\n    gap: theme.spacing(2)\n  },\n  queueName: {\n    fontWeight: 500,\n    color: theme.palette.text.primary\n  },\n  queueColor: {\n    width: 20,\n    height: 20,\n    borderRadius: '50%',\n    border: '2px solid white',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n  },\n  actionButtons: {\n    display: 'flex',\n    gap: theme.spacing(1)\n  },\n  actionButton: {\n    padding: theme.spacing(1),\n    borderRadius: 8,\n    '&:hover': {\n      transform: 'scale(1.05)'\n    }\n  },\n  headerActions: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: theme.spacing(3)\n  },\n  sectionTitle: {\n    fontSize: '1.25rem',\n    fontWeight: 600,\n    color: theme.palette.text.primary,\n    display: 'flex',\n    alignItems: 'center',\n    '& svg': {\n      marginRight: theme.spacing(1),\n      color: theme.palette.primary.main\n    }\n  },\n  statusChip: {\n    fontSize: '0.75rem',\n    fontWeight: 500\n  }\n}));\nconst reducer = (state, action) => {\n  if (action.type === \"LOAD_QUEUES\") {\n    const queues = action.payload;\n    const newQueues = [];\n    queues.forEach(queue => {\n      const queueIndex = state.findIndex(q => q.id === queue.id);\n      if (queueIndex !== -1) {\n        state[queueIndex] = queue;\n      } else {\n        newQueues.push(queue);\n      }\n    });\n    return [...state, ...newQueues];\n  }\n  if (action.type === \"UPDATE_QUEUES\") {\n    const queue = action.payload;\n    const queueIndex = state.findIndex(u => u.id === queue.id);\n    if (queueIndex !== -1) {\n      state[queueIndex] = queue;\n      return [...state];\n    } else {\n      return [queue, ...state];\n    }\n  }\n  if (action.type === \"DELETE_QUEUE\") {\n    const queueId = action.payload;\n    const queueIndex = state.findIndex(q => q.id === queueId);\n    if (queueIndex !== -1) {\n      state.splice(queueIndex, 1);\n    }\n    return [...state];\n  }\n  if (action.type === \"RESET\") {\n    return [];\n  }\n};\nconst Queues = () => {\n  const classes = useStyles();\n  const [queues, dispatch] = useReducer(reducer, []);\n  const [loading, setLoading] = useState(false);\n  const [queueModalOpen, setQueueModalOpen] = useState(false);\n  const [selectedQueue, setSelectedQueue] = useState(null);\n  const [confirmModalOpen, setConfirmModalOpen] = useState(false);\n  useEffect(() => {\n    (async () => {\n      setLoading(true);\n      try {\n        const {\n          data\n        } = await api.get(\"/queue\");\n        dispatch({\n          type: \"LOAD_QUEUES\",\n          payload: data\n        });\n        setLoading(false);\n      } catch (err) {\n        toastError(err);\n        setLoading(false);\n      }\n    })();\n  }, []);\n  useEffect(() => {\n    const companyId = localStorage.getItem(\"companyId\");\n    const socket = socketConnection({\n      companyId\n    });\n    socket.on(`company-${companyId}-queue`, data => {\n      if (data.action === \"update\" || data.action === \"create\") {\n        dispatch({\n          type: \"UPDATE_QUEUES\",\n          payload: data.queue\n        });\n      }\n      if (data.action === \"delete\") {\n        dispatch({\n          type: \"DELETE_QUEUE\",\n          payload: data.queueId\n        });\n      }\n    });\n    return () => {\n      socket.disconnect();\n    };\n  }, []);\n  const handleOpenQueueModal = () => {\n    setQueueModalOpen(true);\n    setSelectedQueue(null);\n  };\n  const handleCloseQueueModal = () => {\n    setQueueModalOpen(false);\n    setSelectedQueue(null);\n  };\n  const handleEditQueue = queue => {\n    setSelectedQueue(queue);\n    setQueueModalOpen(true);\n  };\n  const handleCloseConfirmationModal = () => {\n    setConfirmModalOpen(false);\n    setSelectedQueue(null);\n  };\n  const handleDeleteQueue = async queueId => {\n    try {\n      await api.delete(`/queue/${queueId}`);\n      toast.success(i18n.t(\"Queue deleted successfully!\"));\n    } catch (err) {\n      toastError(err);\n    }\n    setSelectedQueue(null);\n  };\n  return /*#__PURE__*/React.createElement(ModernPageContainer, {\n    title: \"Filas\",\n    subtitle: \"Gerencie as filas de atendimento do sistema\",\n    breadcrumbs: [{\n      label: 'Filas',\n      href: '/queues'\n    }],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(ConfirmationModal, {\n    title: selectedQueue && `${i18n.t(\"queues.confirmationModal.deleteTitle\")} ${selectedQueue.name}?`,\n    open: confirmModalOpen,\n    onClose: handleCloseConfirmationModal,\n    onConfirm: () => handleDeleteQueue(selectedQueue.id),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }\n  }, i18n.t(\"queues.confirmationModal.deleteMessage\")), /*#__PURE__*/React.createElement(QueueModal, {\n    open: queueModalOpen,\n    onClose: handleCloseQueueModal,\n    queueId: selectedQueue === null || selectedQueue === void 0 ? void 0 : selectedQueue.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.headerActions,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Typography, {\n    className: classes.sectionTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(QueueIcon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 11\n    }\n  }), \"Filas de Atendimento\"), /*#__PURE__*/React.createElement(ModernButton, {\n    variant: \"primary\",\n    onClick: handleOpenQueueModal,\n    startIcon: /*#__PURE__*/React.createElement(AddIcon, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 22\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 9\n    }\n  }, i18n.t(\"queues.buttons.add\"))), /*#__PURE__*/React.createElement(Paper, {\n    className: classes.tableContainer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Table, {\n    size: \"small\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(TableHead, {\n    className: classes.tableHeader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(TableRow, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(TableCell, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 15\n    }\n  }, i18n.t(\"queues.table.name\")), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 15\n    }\n  }, i18n.t(\"queues.table.color\")), /*#__PURE__*/React.createElement(TableCell, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 15\n    }\n  }, i18n.t(\"queues.table.greeting\")), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 15\n    }\n  }, i18n.t(\"queues.table.actions\")))), /*#__PURE__*/React.createElement(TableBody, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 11\n    }\n  }, queues.map(queue => /*#__PURE__*/React.createElement(TableRow, {\n    key: queue.id,\n    className: classes.tableRow,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(TableCell, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.queueInfo,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.queueColor,\n    style: {\n      backgroundColor: queue.color\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 21\n    }\n  }), /*#__PURE__*/React.createElement(Typography, {\n    className: classes.queueName,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 21\n    }\n  }, queue.name))), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(Chip, {\n    size: \"small\",\n    label: queue.color,\n    style: {\n      backgroundColor: queue.color,\n      color: 'white',\n      fontWeight: 500\n    },\n    className: classes.statusChip,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 19\n    }\n  })), /*#__PURE__*/React.createElement(TableCell, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(Typography, {\n    variant: \"body2\",\n    noWrap: true,\n    style: {\n      maxWidth: 300\n    },\n    title: queue.greetingMessage,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 19\n    }\n  }, queue.greetingMessage || '-')), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.actionButtons,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(IconButton, {\n    size: \"small\",\n    onClick: () => handleEditQueue(queue),\n    className: classes.actionButton,\n    style: {\n      color: '#667eea'\n    },\n    title: \"Editar fila\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(Edit, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 23\n    }\n  })), /*#__PURE__*/React.createElement(IconButton, {\n    size: \"small\",\n    onClick: () => {\n      setSelectedQueue(queue);\n      setConfirmModalOpen(true);\n    },\n    className: classes.actionButton,\n    style: {\n      color: '#f56565'\n    },\n    title: \"Excluir fila\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(DeleteOutline, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 23\n    }\n  })))))), loading && /*#__PURE__*/React.createElement(TableRowSkeleton, {\n    columns: 4,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 25\n    }\n  })))));\n};\nexport default Queues;", "map": {"version": 3, "names": ["React", "useEffect", "useReducer", "useState", "IconButton", "makeStyles", "Paper", "Table", "TableBody", "TableCell", "TableHead", "TableRow", "Typography", "Box", "Chip", "TableRowSkeleton", "i18n", "toastError", "api", "DeleteOutline", "Edit", "Add", "AddIcon", "Queue", "QueueIcon", "QueueModal", "toast", "ConfirmationModal", "socketConnection", "ModernPageContainer", "ModernButton", "useStyles", "theme", "tableContainer", "borderRadius", "boxShadow", "border", "overflow", "tableHeader", "backgroundColor", "fontWeight", "color", "palette", "text", "primary", "fontSize", "tableRow", "queueInfo", "display", "alignItems", "gap", "spacing", "queueName", "queueColor", "width", "height", "actionButtons", "actionButton", "padding", "transform", "headerActions", "justifyContent", "marginBottom", "sectionTitle", "marginRight", "main", "statusChip", "reducer", "state", "action", "type", "queues", "payload", "newQueues", "for<PERSON>ach", "queue", "queueIndex", "findIndex", "q", "id", "push", "u", "queueId", "splice", "Queues", "classes", "dispatch", "loading", "setLoading", "queueModalOpen", "setQueueModalOpen", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedQueue", "confirmModalOpen", "setConfirmModalOpen", "data", "get", "err", "companyId", "localStorage", "getItem", "socket", "on", "disconnect", "handleOpenQueueModal", "handleCloseQueueModal", "handleEditQueue", "handleCloseConfirmationModal", "handleDeleteQueue", "delete", "success", "t", "createElement", "title", "subtitle", "breadcrumbs", "label", "href", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "open", "onClose", "onConfirm", "className", "variant", "onClick", "startIcon", "size", "align", "map", "key", "style", "noWrap", "max<PERSON><PERSON><PERSON>", "greetingMessage", "columns"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/pages/Queues/index.js"], "sourcesContent": ["import React, { useEffect, useReducer, useState } from \"react\";\r\n\r\nimport {\r\n  IconButton,\r\n  makeStyles,\r\n  Paper,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableRow,\r\n  Typography,\r\n  Box,\r\n  Chip,\r\n} from \"@material-ui/core\";\r\n\r\nimport TableRowSkeleton from \"../../components/TableRowSkeleton\";\r\nimport { i18n } from \"../../translate/i18n\";\r\nimport toastError from \"../../errors/toastError\";\r\nimport api from \"../../services/api\";\r\nimport { DeleteOutline, Edit, Add as AddIcon, Queue as QueueIcon } from \"@material-ui/icons\";\r\nimport QueueModal from \"../../components/QueueModal\";\r\nimport { toast } from \"react-toastify\";\r\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\r\nimport { socketConnection } from \"../../services/socket\";\r\nimport ModernPageContainer from \"../../components/ModernPageContainer\";\r\nimport ModernButton from \"../../components/ModernButton\";\r\n\r\nconst useStyles = makeStyles((theme) => ({\r\n  tableContainer: {\r\n    borderRadius: 16,\r\n    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\r\n    border: '1px solid rgba(0,0,0,0.04)',\r\n    overflow: 'hidden',\r\n  },\r\n  tableHeader: {\r\n    backgroundColor: 'rgba(102, 126, 234, 0.05)',\r\n    '& .MuiTableCell-head': {\r\n      fontWeight: 600,\r\n      color: theme.palette.text.primary,\r\n      fontSize: '0.875rem',\r\n    }\r\n  },\r\n  tableRow: {\r\n    '&:hover': {\r\n      backgroundColor: 'rgba(102, 126, 234, 0.02)',\r\n    }\r\n  },\r\n  queueInfo: {\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    gap: theme.spacing(2),\r\n  },\r\n  queueName: {\r\n    fontWeight: 500,\r\n    color: theme.palette.text.primary,\r\n  },\r\n  queueColor: {\r\n    width: 20,\r\n    height: 20,\r\n    borderRadius: '50%',\r\n    border: '2px solid white',\r\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\r\n  },\r\n  actionButtons: {\r\n    display: 'flex',\r\n    gap: theme.spacing(1),\r\n  },\r\n  actionButton: {\r\n    padding: theme.spacing(1),\r\n    borderRadius: 8,\r\n    '&:hover': {\r\n      transform: 'scale(1.05)',\r\n    }\r\n  },\r\n  headerActions: {\r\n    display: 'flex',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    marginBottom: theme.spacing(3),\r\n  },\r\n  sectionTitle: {\r\n    fontSize: '1.25rem',\r\n    fontWeight: 600,\r\n    color: theme.palette.text.primary,\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    '& svg': {\r\n      marginRight: theme.spacing(1),\r\n      color: theme.palette.primary.main,\r\n    }\r\n  },\r\n  statusChip: {\r\n    fontSize: '0.75rem',\r\n    fontWeight: 500,\r\n  }\r\n}));\r\n\r\nconst reducer = (state, action) => {\r\n  if (action.type === \"LOAD_QUEUES\") {\r\n    const queues = action.payload;\r\n    const newQueues = [];\r\n\r\n    queues.forEach((queue) => {\r\n      const queueIndex = state.findIndex((q) => q.id === queue.id);\r\n      if (queueIndex !== -1) {\r\n        state[queueIndex] = queue;\r\n      } else {\r\n        newQueues.push(queue);\r\n      }\r\n    });\r\n\r\n    return [...state, ...newQueues];\r\n  }\r\n\r\n  if (action.type === \"UPDATE_QUEUES\") {\r\n    const queue = action.payload;\r\n    const queueIndex = state.findIndex((u) => u.id === queue.id);\r\n\r\n    if (queueIndex !== -1) {\r\n      state[queueIndex] = queue;\r\n      return [...state];\r\n    } else {\r\n      return [queue, ...state];\r\n    }\r\n  }\r\n\r\n  if (action.type === \"DELETE_QUEUE\") {\r\n    const queueId = action.payload;\r\n    const queueIndex = state.findIndex((q) => q.id === queueId);\r\n    if (queueIndex !== -1) {\r\n      state.splice(queueIndex, 1);\r\n    }\r\n    return [...state];\r\n  }\r\n\r\n  if (action.type === \"RESET\") {\r\n    return [];\r\n  }\r\n};\r\n\r\nconst Queues = () => {\r\n  const classes = useStyles();\r\n\r\n  const [queues, dispatch] = useReducer(reducer, []);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const [queueModalOpen, setQueueModalOpen] = useState(false);\r\n  const [selectedQueue, setSelectedQueue] = useState(null);\r\n  const [confirmModalOpen, setConfirmModalOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    (async () => {\r\n      setLoading(true);\r\n      try {\r\n        const { data } = await api.get(\"/queue\");\r\n        dispatch({ type: \"LOAD_QUEUES\", payload: data });\r\n\r\n        setLoading(false);\r\n      } catch (err) {\r\n        toastError(err);\r\n        setLoading(false);\r\n      }\r\n    })();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const companyId = localStorage.getItem(\"companyId\");\r\n    const socket = socketConnection({ companyId });\r\n\r\n    socket.on(`company-${companyId}-queue`, (data) => {\r\n      if (data.action === \"update\" || data.action === \"create\") {\r\n        dispatch({ type: \"UPDATE_QUEUES\", payload: data.queue });\r\n      }\r\n\r\n      if (data.action === \"delete\") {\r\n        dispatch({ type: \"DELETE_QUEUE\", payload: data.queueId });\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      socket.disconnect();\r\n    };\r\n  }, []);\r\n\r\n  const handleOpenQueueModal = () => {\r\n    setQueueModalOpen(true);\r\n    setSelectedQueue(null);\r\n  };\r\n\r\n  const handleCloseQueueModal = () => {\r\n    setQueueModalOpen(false);\r\n    setSelectedQueue(null);\r\n  };\r\n\r\n  const handleEditQueue = (queue) => {\r\n    setSelectedQueue(queue);\r\n    setQueueModalOpen(true);\r\n  };\r\n\r\n  const handleCloseConfirmationModal = () => {\r\n    setConfirmModalOpen(false);\r\n    setSelectedQueue(null);\r\n  };\r\n\r\n  const handleDeleteQueue = async (queueId) => {\r\n    try {\r\n      await api.delete(`/queue/${queueId}`);\r\n      toast.success(i18n.t(\"Queue deleted successfully!\"));\r\n    } catch (err) {\r\n      toastError(err);\r\n    }\r\n    setSelectedQueue(null);\r\n  };\r\n\r\n  return (\r\n    <ModernPageContainer\r\n      title=\"Filas\"\r\n      subtitle=\"Gerencie as filas de atendimento do sistema\"\r\n      breadcrumbs={[\r\n        { label: 'Filas', href: '/queues' }\r\n      ]}\r\n    >\r\n      <ConfirmationModal\r\n        title={\r\n          selectedQueue &&\r\n          `${i18n.t(\"queues.confirmationModal.deleteTitle\")} ${\r\n            selectedQueue.name\r\n          }?`\r\n        }\r\n        open={confirmModalOpen}\r\n        onClose={handleCloseConfirmationModal}\r\n        onConfirm={() => handleDeleteQueue(selectedQueue.id)}\r\n      >\r\n        {i18n.t(\"queues.confirmationModal.deleteMessage\")}\r\n      </ConfirmationModal>\r\n\r\n      <QueueModal\r\n        open={queueModalOpen}\r\n        onClose={handleCloseQueueModal}\r\n        queueId={selectedQueue?.id}\r\n      />\r\n\r\n      {/* Header Actions */}\r\n      <div className={classes.headerActions}>\r\n        <Typography className={classes.sectionTitle}>\r\n          <QueueIcon />\r\n          Filas de Atendimento\r\n        </Typography>\r\n\r\n        <ModernButton\r\n          variant=\"primary\"\r\n          onClick={handleOpenQueueModal}\r\n          startIcon={<AddIcon />}\r\n        >\r\n          {i18n.t(\"queues.buttons.add\")}\r\n        </ModernButton>\r\n      </div>\r\n\r\n      {/* Queues Table */}\r\n      <Paper className={classes.tableContainer}>\r\n        <Table size=\"small\">\r\n          <TableHead className={classes.tableHeader}>\r\n            <TableRow>\r\n              <TableCell>{i18n.t(\"queues.table.name\")}</TableCell>\r\n              <TableCell align=\"center\">{i18n.t(\"queues.table.color\")}</TableCell>\r\n              <TableCell>{i18n.t(\"queues.table.greeting\")}</TableCell>\r\n              <TableCell align=\"center\">{i18n.t(\"queues.table.actions\")}</TableCell>\r\n            </TableRow>\r\n          </TableHead>\r\n          <TableBody>\r\n            {queues.map((queue) => (\r\n              <TableRow key={queue.id} className={classes.tableRow}>\r\n                <TableCell>\r\n                  <div className={classes.queueInfo}>\r\n                    <div\r\n                      className={classes.queueColor}\r\n                      style={{ backgroundColor: queue.color }}\r\n                    />\r\n                    <Typography className={classes.queueName}>\r\n                      {queue.name}\r\n                    </Typography>\r\n                  </div>\r\n                </TableCell>\r\n                <TableCell align=\"center\">\r\n                  <Chip\r\n                    size=\"small\"\r\n                    label={queue.color}\r\n                    style={{\r\n                      backgroundColor: queue.color,\r\n                      color: 'white',\r\n                      fontWeight: 500\r\n                    }}\r\n                    className={classes.statusChip}\r\n                  />\r\n                </TableCell>\r\n                <TableCell>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    noWrap\r\n                    style={{ maxWidth: 300 }}\r\n                    title={queue.greetingMessage}\r\n                  >\r\n                    {queue.greetingMessage || '-'}\r\n                  </Typography>\r\n                </TableCell>\r\n                <TableCell align=\"center\">\r\n                  <div className={classes.actionButtons}>\r\n                    <IconButton\r\n                      size=\"small\"\r\n                      onClick={() => handleEditQueue(queue)}\r\n                      className={classes.actionButton}\r\n                      style={{ color: '#667eea' }}\r\n                      title=\"Editar fila\"\r\n                    >\r\n                      <Edit />\r\n                    </IconButton>\r\n                    <IconButton\r\n                      size=\"small\"\r\n                      onClick={() => {\r\n                        setSelectedQueue(queue);\r\n                        setConfirmModalOpen(true);\r\n                      }}\r\n                      className={classes.actionButton}\r\n                      style={{ color: '#f56565' }}\r\n                      title=\"Excluir fila\"\r\n                    >\r\n                      <DeleteOutline />\r\n                    </IconButton>\r\n                  </div>\r\n                </TableCell>\r\n              </TableRow>\r\n            ))}\r\n            {loading && <TableRowSkeleton columns={4} />}\r\n          </TableBody>\r\n        </Table>\r\n      </Paper>\r\n    </ModernPageContainer>\r\n  );\r\n};\r\n\r\nexport default Queues;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AAE9D,SACEC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,GAAG,EACHC,IAAI,QACC,mBAAmB;AAE1B,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,GAAG,MAAM,oBAAoB;AACpC,SAASC,aAAa,EAAEC,IAAI,EAAEC,GAAG,IAAIC,OAAO,EAAEC,KAAK,IAAIC,SAAS,QAAQ,oBAAoB;AAC5F,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,OAAOC,YAAY,MAAM,+BAA+B;AAExD,MAAMC,SAAS,GAAG1B,UAAU,CAAE2B,KAAK,KAAM;EACvCC,cAAc,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,6BAA6B;IACxCC,MAAM,EAAE,4BAA4B;IACpCC,QAAQ,EAAE;EACZ,CAAC;EACDC,WAAW,EAAE;IACXC,eAAe,EAAE,2BAA2B;IAC5C,sBAAsB,EAAE;MACtBC,UAAU,EAAE,GAAG;MACfC,KAAK,EAAET,KAAK,CAACU,OAAO,CAACC,IAAI,CAACC,OAAO;MACjCC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,QAAQ,EAAE;IACR,SAAS,EAAE;MACTP,eAAe,EAAE;IACnB;EACF,CAAC;EACDQ,SAAS,EAAE;IACTC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAElB,KAAK,CAACmB,OAAO,CAAC,CAAC;EACtB,CAAC;EACDC,SAAS,EAAE;IACTZ,UAAU,EAAE,GAAG;IACfC,KAAK,EAAET,KAAK,CAACU,OAAO,CAACC,IAAI,CAACC;EAC5B,CAAC;EACDS,UAAU,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVrB,YAAY,EAAE,KAAK;IACnBE,MAAM,EAAE,iBAAiB;IACzBD,SAAS,EAAE;EACb,CAAC;EACDqB,aAAa,EAAE;IACbR,OAAO,EAAE,MAAM;IACfE,GAAG,EAAElB,KAAK,CAACmB,OAAO,CAAC,CAAC;EACtB,CAAC;EACDM,YAAY,EAAE;IACZC,OAAO,EAAE1B,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC;IACzBjB,YAAY,EAAE,CAAC;IACf,SAAS,EAAE;MACTyB,SAAS,EAAE;IACb;EACF,CAAC;EACDC,aAAa,EAAE;IACbZ,OAAO,EAAE,MAAM;IACfa,cAAc,EAAE,eAAe;IAC/BZ,UAAU,EAAE,QAAQ;IACpBa,YAAY,EAAE9B,KAAK,CAACmB,OAAO,CAAC,CAAC;EAC/B,CAAC;EACDY,YAAY,EAAE;IACZlB,QAAQ,EAAE,SAAS;IACnBL,UAAU,EAAE,GAAG;IACfC,KAAK,EAAET,KAAK,CAACU,OAAO,CAACC,IAAI,CAACC,OAAO;IACjCI,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpB,OAAO,EAAE;MACPe,WAAW,EAAEhC,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC;MAC7BV,KAAK,EAAET,KAAK,CAACU,OAAO,CAACE,OAAO,CAACqB;IAC/B;EACF,CAAC;EACDC,UAAU,EAAE;IACVrB,QAAQ,EAAE,SAAS;IACnBL,UAAU,EAAE;EACd;AACF,CAAC,CAAC,CAAC;AAEH,MAAM2B,OAAO,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACjC,IAAIA,MAAM,CAACC,IAAI,KAAK,aAAa,EAAE;IACjC,MAAMC,MAAM,GAAGF,MAAM,CAACG,OAAO;IAC7B,MAAMC,SAAS,GAAG,EAAE;IAEpBF,MAAM,CAACG,OAAO,CAAEC,KAAK,IAAK;MACxB,MAAMC,UAAU,GAAGR,KAAK,CAACS,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAKJ,KAAK,CAACI,EAAE,CAAC;MAC5D,IAAIH,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBR,KAAK,CAACQ,UAAU,CAAC,GAAGD,KAAK;MAC3B,CAAC,MAAM;QACLF,SAAS,CAACO,IAAI,CAACL,KAAK,CAAC;MACvB;IACF,CAAC,CAAC;IAEF,OAAO,CAAC,GAAGP,KAAK,EAAE,GAAGK,SAAS,CAAC;EACjC;EAEA,IAAIJ,MAAM,CAACC,IAAI,KAAK,eAAe,EAAE;IACnC,MAAMK,KAAK,GAAGN,MAAM,CAACG,OAAO;IAC5B,MAAMI,UAAU,GAAGR,KAAK,CAACS,SAAS,CAAEI,CAAC,IAAKA,CAAC,CAACF,EAAE,KAAKJ,KAAK,CAACI,EAAE,CAAC;IAE5D,IAAIH,UAAU,KAAK,CAAC,CAAC,EAAE;MACrBR,KAAK,CAACQ,UAAU,CAAC,GAAGD,KAAK;MACzB,OAAO,CAAC,GAAGP,KAAK,CAAC;IACnB,CAAC,MAAM;MACL,OAAO,CAACO,KAAK,EAAE,GAAGP,KAAK,CAAC;IAC1B;EACF;EAEA,IAAIC,MAAM,CAACC,IAAI,KAAK,cAAc,EAAE;IAClC,MAAMY,OAAO,GAAGb,MAAM,CAACG,OAAO;IAC9B,MAAMI,UAAU,GAAGR,KAAK,CAACS,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAKG,OAAO,CAAC;IAC3D,IAAIN,UAAU,KAAK,CAAC,CAAC,EAAE;MACrBR,KAAK,CAACe,MAAM,CAACP,UAAU,EAAE,CAAC,CAAC;IAC7B;IACA,OAAO,CAAC,GAAGR,KAAK,CAAC;EACnB;EAEA,IAAIC,MAAM,CAACC,IAAI,KAAK,OAAO,EAAE;IAC3B,OAAO,EAAE;EACX;AACF,CAAC;AAED,MAAMc,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,OAAO,GAAGtD,SAAS,CAAC,CAAC;EAE3B,MAAM,CAACwC,MAAM,EAAEe,QAAQ,CAAC,GAAGpF,UAAU,CAACiE,OAAO,EAAE,EAAE,CAAC;EAClD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM,CAACsF,cAAc,EAAEC,iBAAiB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACwF,aAAa,EAAEC,gBAAgB,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC0F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EAE/DF,SAAS,CAAC,MAAM;IACd,CAAC,YAAY;MACXuF,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAM;UAAEO;QAAK,CAAC,GAAG,MAAM7E,GAAG,CAAC8E,GAAG,CAAC,QAAQ,CAAC;QACxCV,QAAQ,CAAC;UAAEhB,IAAI,EAAE,aAAa;UAAEE,OAAO,EAAEuB;QAAK,CAAC,CAAC;QAEhDP,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOS,GAAG,EAAE;QACZhF,UAAU,CAACgF,GAAG,CAAC;QACfT,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,EAAE,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAENvF,SAAS,CAAC,MAAM;IACd,MAAMiG,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,MAAM,GAAGzE,gBAAgB,CAAC;MAAEsE;IAAU,CAAC,CAAC;IAE9CG,MAAM,CAACC,EAAE,CAAC,WAAWJ,SAAS,QAAQ,EAAGH,IAAI,IAAK;MAChD,IAAIA,IAAI,CAAC1B,MAAM,KAAK,QAAQ,IAAI0B,IAAI,CAAC1B,MAAM,KAAK,QAAQ,EAAE;QACxDiB,QAAQ,CAAC;UAAEhB,IAAI,EAAE,eAAe;UAAEE,OAAO,EAAEuB,IAAI,CAACpB;QAAM,CAAC,CAAC;MAC1D;MAEA,IAAIoB,IAAI,CAAC1B,MAAM,KAAK,QAAQ,EAAE;QAC5BiB,QAAQ,CAAC;UAAEhB,IAAI,EAAE,cAAc;UAAEE,OAAO,EAAEuB,IAAI,CAACb;QAAQ,CAAC,CAAC;MAC3D;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACXmB,MAAM,CAACE,UAAU,CAAC,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCd,iBAAiB,CAAC,IAAI,CAAC;IACvBE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMa,qBAAqB,GAAGA,CAAA,KAAM;IAClCf,iBAAiB,CAAC,KAAK,CAAC;IACxBE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMc,eAAe,GAAI/B,KAAK,IAAK;IACjCiB,gBAAgB,CAACjB,KAAK,CAAC;IACvBe,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMiB,4BAA4B,GAAGA,CAAA,KAAM;IACzCb,mBAAmB,CAAC,KAAK,CAAC;IAC1BF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMgB,iBAAiB,GAAG,MAAO1B,OAAO,IAAK;IAC3C,IAAI;MACF,MAAMhE,GAAG,CAAC2F,MAAM,CAAC,UAAU3B,OAAO,EAAE,CAAC;MACrCxD,KAAK,CAACoF,OAAO,CAAC9F,IAAI,CAAC+F,CAAC,CAAC,6BAA6B,CAAC,CAAC;IACtD,CAAC,CAAC,OAAOd,GAAG,EAAE;MACZhF,UAAU,CAACgF,GAAG,CAAC;IACjB;IACAL,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,oBACE5F,KAAA,CAAAgH,aAAA,CAACnF,mBAAmB;IAClBoF,KAAK,EAAC,OAAO;IACbC,QAAQ,EAAC,6CAA6C;IACtDC,WAAW,EAAE,CACX;MAAEC,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAU,CAAC,CACnC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF3H,KAAA,CAAAgH,aAAA,CAACrF,iBAAiB;IAChBsF,KAAK,EACHtB,aAAa,IACb,GAAG3E,IAAI,CAAC+F,CAAC,CAAC,sCAAsC,CAAC,IAC/CpB,aAAa,CAACiC,IAAI,GAErB;IACDC,IAAI,EAAEhC,gBAAiB;IACvBiC,OAAO,EAAEnB,4BAA6B;IACtCoB,SAAS,EAAEA,CAAA,KAAMnB,iBAAiB,CAACjB,aAAa,CAACZ,EAAE,CAAE;IAAAuC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEpD3G,IAAI,CAAC+F,CAAC,CAAC,wCAAwC,CAC/B,CAAC,eAEpB/G,KAAA,CAAAgH,aAAA,CAACvF,UAAU;IACToG,IAAI,EAAEpC,cAAe;IACrBqC,OAAO,EAAErB,qBAAsB;IAC/BvB,OAAO,EAAES,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEZ,EAAG;IAAAuC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC5B,CAAC,eAGF3H,KAAA,CAAAgH,aAAA;IAAKgB,SAAS,EAAE3C,OAAO,CAACzB,aAAc;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpC3H,KAAA,CAAAgH,aAAA,CAACpG,UAAU;IAACoH,SAAS,EAAE3C,OAAO,CAACtB,YAAa;IAAAuD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1C3H,KAAA,CAAAgH,aAAA,CAACxF,SAAS;IAAA8F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,wBAEH,CAAC,eAEb3H,KAAA,CAAAgH,aAAA,CAAClF,YAAY;IACXmG,OAAO,EAAC,SAAS;IACjBC,OAAO,EAAE1B,oBAAqB;IAC9B2B,SAAS,eAAEnI,KAAA,CAAAgH,aAAA,CAAC1F,OAAO;MAAAgG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEtB3G,IAAI,CAAC+F,CAAC,CAAC,oBAAoB,CAChB,CACX,CAAC,eAGN/G,KAAA,CAAAgH,aAAA,CAAC1G,KAAK;IAAC0H,SAAS,EAAE3C,OAAO,CAACpD,cAAe;IAAAqF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvC3H,KAAA,CAAAgH,aAAA,CAACzG,KAAK;IAAC6H,IAAI,EAAC,OAAO;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjB3H,KAAA,CAAAgH,aAAA,CAACtG,SAAS;IAACsH,SAAS,EAAE3C,OAAO,CAAC/C,WAAY;IAAAgF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxC3H,KAAA,CAAAgH,aAAA,CAACrG,QAAQ;IAAA2G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACP3H,KAAA,CAAAgH,aAAA,CAACvG,SAAS;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE3G,IAAI,CAAC+F,CAAC,CAAC,mBAAmB,CAAa,CAAC,eACpD/G,KAAA,CAAAgH,aAAA,CAACvG,SAAS;IAAC4H,KAAK,EAAC,QAAQ;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE3G,IAAI,CAAC+F,CAAC,CAAC,oBAAoB,CAAa,CAAC,eACpE/G,KAAA,CAAAgH,aAAA,CAACvG,SAAS;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE3G,IAAI,CAAC+F,CAAC,CAAC,uBAAuB,CAAa,CAAC,eACxD/G,KAAA,CAAAgH,aAAA,CAACvG,SAAS;IAAC4H,KAAK,EAAC,QAAQ;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE3G,IAAI,CAAC+F,CAAC,CAAC,sBAAsB,CAAa,CAC7D,CACD,CAAC,eACZ/G,KAAA,CAAAgH,aAAA,CAACxG,SAAS;IAAA8G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACPpD,MAAM,CAAC+D,GAAG,CAAE3D,KAAK,iBAChB3E,KAAA,CAAAgH,aAAA,CAACrG,QAAQ;IAAC4H,GAAG,EAAE5D,KAAK,CAACI,EAAG;IAACiD,SAAS,EAAE3C,OAAO,CAACvC,QAAS;IAAAwE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnD3H,KAAA,CAAAgH,aAAA,CAACvG,SAAS;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACR3H,KAAA,CAAAgH,aAAA;IAAKgB,SAAS,EAAE3C,OAAO,CAACtC,SAAU;IAAAuE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChC3H,KAAA,CAAAgH,aAAA;IACEgB,SAAS,EAAE3C,OAAO,CAAChC,UAAW;IAC9BmF,KAAK,EAAE;MAAEjG,eAAe,EAAEoC,KAAK,CAAClC;IAAM,CAAE;IAAA6E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACzC,CAAC,eACF3H,KAAA,CAAAgH,aAAA,CAACpG,UAAU;IAACoH,SAAS,EAAE3C,OAAO,CAACjC,SAAU;IAAAkE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtChD,KAAK,CAACiD,IACG,CACT,CACI,CAAC,eACZ5H,KAAA,CAAAgH,aAAA,CAACvG,SAAS;IAAC4H,KAAK,EAAC,QAAQ;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3H,KAAA,CAAAgH,aAAA,CAAClG,IAAI;IACHsH,IAAI,EAAC,OAAO;IACZhB,KAAK,EAAEzC,KAAK,CAAClC,KAAM;IACnB+F,KAAK,EAAE;MACLjG,eAAe,EAAEoC,KAAK,CAAClC,KAAK;MAC5BA,KAAK,EAAE,OAAO;MACdD,UAAU,EAAE;IACd,CAAE;IACFwF,SAAS,EAAE3C,OAAO,CAACnB,UAAW;IAAAoD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC/B,CACQ,CAAC,eACZ3H,KAAA,CAAAgH,aAAA,CAACvG,SAAS;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACR3H,KAAA,CAAAgH,aAAA,CAACpG,UAAU;IACTqH,OAAO,EAAC,OAAO;IACfQ,MAAM;IACND,KAAK,EAAE;MAAEE,QAAQ,EAAE;IAAI,CAAE;IACzBzB,KAAK,EAAEtC,KAAK,CAACgE,eAAgB;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAE5BhD,KAAK,CAACgE,eAAe,IAAI,GAChB,CACH,CAAC,eACZ3I,KAAA,CAAAgH,aAAA,CAACvG,SAAS;IAAC4H,KAAK,EAAC,QAAQ;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3H,KAAA,CAAAgH,aAAA;IAAKgB,SAAS,EAAE3C,OAAO,CAAC7B,aAAc;IAAA8D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpC3H,KAAA,CAAAgH,aAAA,CAAC5G,UAAU;IACTgI,IAAI,EAAC,OAAO;IACZF,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAAC/B,KAAK,CAAE;IACtCqD,SAAS,EAAE3C,OAAO,CAAC5B,YAAa;IAChC+E,KAAK,EAAE;MAAE/F,KAAK,EAAE;IAAU,CAAE;IAC5BwE,KAAK,EAAC,aAAa;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEnB3H,KAAA,CAAAgH,aAAA,CAAC5F,IAAI;IAAAkG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACG,CAAC,eACb3H,KAAA,CAAAgH,aAAA,CAAC5G,UAAU;IACTgI,IAAI,EAAC,OAAO;IACZF,OAAO,EAAEA,CAAA,KAAM;MACbtC,gBAAgB,CAACjB,KAAK,CAAC;MACvBmB,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAE;IACFkC,SAAS,EAAE3C,OAAO,CAAC5B,YAAa;IAChC+E,KAAK,EAAE;MAAE/F,KAAK,EAAE;IAAU,CAAE;IAC5BwE,KAAK,EAAC,cAAc;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpB3H,KAAA,CAAAgH,aAAA,CAAC7F,aAAa;IAAAmG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACN,CACT,CACI,CACH,CACX,CAAC,EACDpC,OAAO,iBAAIvF,KAAA,CAAAgH,aAAA,CAACjG,gBAAgB;IAAC6H,OAAO,EAAE,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAClC,CACN,CACF,CACY,CAAC;AAE1B,CAAC;AAED,eAAevC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}