import React, { useState, useEffect } from "react";

import { makeStyles } from "@material-ui/core/styles";
import Paper from "@material-ui/core/Paper";
import Typography from "@material-ui/core/Typography";
import Select from "@material-ui/core/Select";
import FormControl from "@material-ui/core/FormControl";
import InputLabel from "@material-ui/core/InputLabel";
import MenuItem from "@material-ui/core/MenuItem";
import Grid from "@material-ui/core/Grid";
import Box from "@material-ui/core/Box";
import Switch from "@material-ui/core/Switch";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import { toast } from "react-toastify";
import { Settings as SettingsIcon, Security, People, Notifications } from "@material-ui/icons";

import api from "../../services/api";
import { i18n } from "../../translate/i18n.js";
import toastError from "../../errors/toastError";
import { socketConnection } from "../../services/socket";
import ModernPageContainer from "../../components/ModernPageContainer";
import ModernCard from "../../components/ModernCard";

const useStyles = makeStyles((theme) => ({
  settingCard: {
    marginBottom: theme.spacing(3),
  },
  settingHeader: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
    '& svg': {
      marginRight: theme.spacing(1),
      color: theme.palette.primary.main,
    }
  },
  settingItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(2),
    borderRadius: 8,
    backgroundColor: 'rgba(102, 126, 234, 0.02)',
    border: '1px solid rgba(102, 126, 234, 0.1)',
    marginBottom: theme.spacing(2),
    '&:last-child': {
      marginBottom: 0,
    }
  },
  settingLabel: {
    fontSize: '1rem',
    fontWeight: 500,
    color: theme.palette.text.primary,
  },
  settingDescription: {
    fontSize: '0.875rem',
    color: theme.palette.text.secondary,
    marginTop: theme.spacing(0.5),
  },
  settingControl: {
    minWidth: 200,
  },
  sectionTitle: {
    fontSize: '1.25rem',
    fontWeight: 600,
    color: theme.palette.text.primary,
    marginBottom: theme.spacing(3),
    display: 'flex',
    alignItems: 'center',
    '& svg': {
      marginRight: theme.spacing(1),
      color: theme.palette.primary.main,
    }
  }
}));

const Settings = () => {
  const classes = useStyles();

  const [settings, setSettings] = useState([]);

  useEffect(() => {
    const fetchSession = async () => {
      try {
        const { data } = await api.get("/settings");
        setSettings(data);
      } catch (err) {
        toastError(err);
      }
    };
    fetchSession();
  }, []);

  useEffect(() => {
    const companyId = localStorage.getItem("companyId");
    const socket = socketConnection({ companyId });

    socket.on(`company-${companyId}-settings`, (data) => {
      if (data.action === "update") {
        setSettings((prevState) => {
          const aux = [...prevState];
          const settingIndex = aux.findIndex((s) => s.key === data.setting.key);
          aux[settingIndex].value = data.setting.value;
          return aux;
        });
      }
    });

    return () => {
      socket.disconnect();
    };
  }, []);

  const handleChangeSetting = async (e) => {
    const selectedValue = e.target.value;
    const settingKey = e.target.name;

    try {
      await api.put(`/settings/${settingKey}`, {
        value: selectedValue,
      });
      toast.success(i18n.t("settings.success"));
    } catch (err) {
      toastError(err);
    }
  };

  const getSettingValue = (key) => {
    const { value } = settings.find((s) => s.key === key);
    return value;
  };

  return (
    <ModernPageContainer
      title="Configurações"
      subtitle="Gerencie as configurações do sistema e personalize sua experiência"
      breadcrumbs={[
        { label: 'Configurações', href: '/settings' }
      ]}
      maxWidth="md"
    >
      {/* User Management Settings */}
      <Paper className={classes.settingCard}>
        <Typography className={classes.sectionTitle}>
          <People />
          Gerenciamento de Usuários
        </Typography>

        <div className={classes.settingItem}>
          <Box>
            <Typography className={classes.settingLabel}>
              {i18n.t("settings.settings.userCreation.name")}
            </Typography>
            <Typography className={classes.settingDescription}>
              Controle quem pode criar novos usuários no sistema
            </Typography>
          </Box>

          <FormControl variant="outlined" size="small" className={classes.settingControl}>
            <InputLabel>Status</InputLabel>
            <Select
              name="userCreation"
              value={
                settings && settings.length > 0 ? getSettingValue("userCreation") : ""
              }
              onChange={handleChangeSetting}
              label="Status"
            >
              <MenuItem value="enabled">
                {i18n.t("settings.settings.userCreation.options.enabled")}
              </MenuItem>
              <MenuItem value="disabled">
                {i18n.t("settings.settings.userCreation.options.disabled")}
              </MenuItem>
            </Select>
          </FormControl>
        </div>
      </Paper>

      {/* Security Settings */}
      <Paper className={classes.settingCard}>
        <Typography className={classes.sectionTitle}>
          <Security />
          Configurações de Segurança
        </Typography>

        <div className={classes.settingItem}>
          <Box>
            <Typography className={classes.settingLabel}>
              Autenticação de Dois Fatores
            </Typography>
            <Typography className={classes.settingDescription}>
              Adicione uma camada extra de segurança às contas
            </Typography>
          </Box>

          <FormControlLabel
            control={
              <Switch
                color="primary"
                // value={getSettingValue("twoFactorAuth")}
                // onChange={handleChangeSetting}
              />
            }
            label=""
          />
        </div>

        <div className={classes.settingItem}>
          <Box>
            <Typography className={classes.settingLabel}>
              Timeout de Sessão
            </Typography>
            <Typography className={classes.settingDescription}>
              Tempo limite para sessões inativas (em minutos)
            </Typography>
          </Box>

          <FormControl variant="outlined" size="small" className={classes.settingControl}>
            <InputLabel>Tempo</InputLabel>
            <Select
              // name="sessionTimeout"
              // value={getSettingValue("sessionTimeout")}
              // onChange={handleChangeSetting}
              label="Tempo"
            >
              <MenuItem value="15">15 minutos</MenuItem>
              <MenuItem value="30">30 minutos</MenuItem>
              <MenuItem value="60">1 hora</MenuItem>
              <MenuItem value="120">2 horas</MenuItem>
            </Select>
          </FormControl>
        </div>
      </Paper>

      {/* Notification Settings */}
      <Paper className={classes.settingCard}>
        <Typography className={classes.sectionTitle}>
          <Notifications />
          Configurações de Notificação
        </Typography>

        <div className={classes.settingItem}>
          <Box>
            <Typography className={classes.settingLabel}>
              Notificações por Email
            </Typography>
            <Typography className={classes.settingDescription}>
              Receber notificações importantes por email
            </Typography>
          </Box>

          <FormControlLabel
            control={
              <Switch
                color="primary"
                defaultChecked
              />
            }
            label=""
          />
        </div>

        <div className={classes.settingItem}>
          <Box>
            <Typography className={classes.settingLabel}>
              Notificações Push
            </Typography>
            <Typography className={classes.settingDescription}>
              Receber notificações push no navegador
            </Typography>
          </Box>

          <FormControlLabel
            control={
              <Switch
                color="primary"
                defaultChecked
              />
            }
            label=""
          />
        </div>
      </Paper>
    </ModernPageContainer>
  );
};

export default Settings;
