{"ast": null, "code": "var _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nimport React from 'react';\nimport reactCSS from 'reactcss';\nimport { ColorWrap, Alpha } from '../common';\nimport AlphaPointer from './AlphaPointer';\nexport var AlphaPicker = function AlphaPicker(_ref) {\n  var rgb = _ref.rgb,\n    hsl = _ref.hsl,\n    width = _ref.width,\n    height = _ref.height,\n    onChange = _ref.onChange,\n    direction = _ref.direction,\n    style = _ref.style,\n    renderers = _ref.renderers,\n    pointer = _ref.pointer,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var styles = reactCSS({\n    'default': {\n      picker: {\n        position: 'relative',\n        width: width,\n        height: height\n      },\n      alpha: {\n        radius: '2px',\n        style: style\n      }\n    }\n  });\n  return React.createElement('div', {\n    style: styles.picker,\n    className: 'alpha-picker ' + className\n  }, React.createElement(Alpha, _extends({}, styles.alpha, {\n    rgb: rgb,\n    hsl: hsl,\n    pointer: pointer,\n    renderers: renderers,\n    onChange: onChange,\n    direction: direction\n  })));\n};\nAlphaPicker.defaultProps = {\n  width: '316px',\n  height: '16px',\n  direction: 'horizontal',\n  pointer: AlphaPointer\n};\nexport default ColorWrap(AlphaPicker);", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "React", "reactCSS", "ColorWrap", "Alpha", "AlphaPointer", "AlphaPicker", "_ref", "rgb", "hsl", "width", "height", "onChange", "direction", "style", "renderers", "pointer", "_ref$className", "className", "undefined", "styles", "picker", "position", "alpha", "radius", "createElement", "defaultProps"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/alpha/Alpha.js"], "sourcesContent": ["var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nimport React from 'react';\nimport reactCSS from 'reactcss';\n\nimport { ColorWrap, Alpha } from '../common';\nimport AlphaPointer from './AlphaPointer';\n\nexport var AlphaPicker = function AlphaPicker(_ref) {\n  var rgb = _ref.rgb,\n      hsl = _ref.hsl,\n      width = _ref.width,\n      height = _ref.height,\n      onChange = _ref.onChange,\n      direction = _ref.direction,\n      style = _ref.style,\n      renderers = _ref.renderers,\n      pointer = _ref.pointer,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var styles = reactCSS({\n    'default': {\n      picker: {\n        position: 'relative',\n        width: width,\n        height: height\n      },\n      alpha: {\n        radius: '2px',\n        style: style\n      }\n    }\n  });\n\n  return React.createElement(\n    'div',\n    { style: styles.picker, className: 'alpha-picker ' + className },\n    React.createElement(Alpha, _extends({}, styles.alpha, {\n      rgb: rgb,\n      hsl: hsl,\n      pointer: pointer,\n      renderers: renderers,\n      onChange: onChange,\n      direction: direction\n    }))\n  );\n};\n\nAlphaPicker.defaultProps = {\n  width: '316px',\n  height: '16px',\n  direction: 'horizontal',\n  pointer: AlphaPointer\n};\n\nexport default ColorWrap(AlphaPicker);"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,OAAOS,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,UAAU;AAE/B,SAASC,SAAS,EAAEC,KAAK,QAAQ,WAAW;AAC5C,OAAOC,YAAY,MAAM,gBAAgB;AAEzC,OAAO,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAClD,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;IACdC,GAAG,GAAGF,IAAI,CAACE,GAAG;IACdC,KAAK,GAAGH,IAAI,CAACG,KAAK;IAClBC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;IACxBC,SAAS,GAAGN,IAAI,CAACM,SAAS;IAC1BC,KAAK,GAAGP,IAAI,CAACO,KAAK;IAClBC,SAAS,GAAGR,IAAI,CAACQ,SAAS;IAC1BC,OAAO,GAAGT,IAAI,CAACS,OAAO;IACtBC,cAAc,GAAGV,IAAI,CAACW,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKE,SAAS,GAAG,EAAE,GAAGF,cAAc;EAElE,IAAIG,MAAM,GAAGlB,QAAQ,CAAC;IACpB,SAAS,EAAE;MACTmB,MAAM,EAAE;QACNC,QAAQ,EAAE,UAAU;QACpBZ,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC;MACDY,KAAK,EAAE;QACLC,MAAM,EAAE,KAAK;QACbV,KAAK,EAAEA;MACT;IACF;EACF,CAAC,CAAC;EAEF,OAAOb,KAAK,CAACwB,aAAa,CACxB,KAAK,EACL;IAAEX,KAAK,EAAEM,MAAM,CAACC,MAAM;IAAEH,SAAS,EAAE,eAAe,GAAGA;EAAU,CAAC,EAChEjB,KAAK,CAACwB,aAAa,CAACrB,KAAK,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAE+B,MAAM,CAACG,KAAK,EAAE;IACpDf,GAAG,EAAEA,GAAG;IACRC,GAAG,EAAEA,GAAG;IACRO,OAAO,EAAEA,OAAO;IAChBD,SAAS,EAAEA,SAAS;IACpBH,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA;EACb,CAAC,CAAC,CACJ,CAAC;AACH,CAAC;AAEDP,WAAW,CAACoB,YAAY,GAAG;EACzBhB,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,MAAM;EACdE,SAAS,EAAE,YAAY;EACvBG,OAAO,EAAEX;AACX,CAAC;AAED,eAAeF,SAAS,CAACG,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}