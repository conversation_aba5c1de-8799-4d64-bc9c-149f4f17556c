import React from "react";
import { useParams } from "react-router-dom";
import Typography from "@material-ui/core/Typography";
import { makeStyles } from "@material-ui/core/styles";

import TicketsManager from "../../components/TicketsManagerTabs/";
import Ticket from "../../components/Ticket/";

import { ChatBubbleOutline } from "@material-ui/icons";

const useStyles = makeStyles(theme => ({
	modernContainer: {
		height: `calc(100vh - 64px)`,
		display: "flex",
		backgroundColor: theme.palette.background.default,
		overflow: "hidden",
	},
	ticketsPanel: {
		width: 400,
		borderRight: '1px solid rgba(0,0,0,0.06)',
		backgroundColor: 'white',
		display: "flex",
		flexDirection: "column",
		boxShadow: '2px 0 8px rgba(0,0,0,0.04)',
	},
	chatPanel: {
		flex: 1,
		display: "flex",
		flexDirection: "column",
		backgroundColor: theme.palette.background.default,
	},
	welcomeContainer: {
		flex: 1,
		display: "flex",
		flexDirection: "column",
		alignItems: "center",
		justifyContent: "center",
		padding: theme.spacing(4),
		textAlign: "center",
		backgroundColor: 'white',
		margin: theme.spacing(2),
		borderRadius: 16,
		boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
	},
	welcomeIcon: {
		fontSize: '4rem',
		color: theme.palette.primary.main,
		marginBottom: theme.spacing(2),
		opacity: 0.7,
	},
	welcomeTitle: {
		fontSize: '1.5rem',
		fontWeight: 600,
		color: theme.palette.text.primary,
		marginBottom: theme.spacing(1),
	},
	welcomeSubtitle: {
		fontSize: '1rem',
		color: theme.palette.text.secondary,
		maxWidth: 400,
		lineHeight: 1.6,
	},
	// Manter estilos antigos para compatibilidade
	chatContainer: {
		flex: 1,
		padding: theme.spacing(2),
		height: `calc(100% - 64px)`,
		overflowY: "hidden",
		backgroundColor: theme.palette.background.default,
	},
	chatPapper: {
		display: "flex",
		height: "100%",
		borderRadius: 16,
		overflow: "hidden",
		boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
	},
	contactsWrapper: {
		display: "flex",
		height: "100%",
		flexDirection: "column",
		overflowY: "hidden",
		backgroundColor: 'white',
	},
	messagesWrapper: {
		display: "flex",
		height: "100%",
		flexDirection: "column",
		backgroundColor: 'white',
	},
	welcomeMsg: {
		backgroundColor: theme.palette.background.default,
		display: "flex",
		justifyContent: "center",
		alignItems: "center",
		height: "100%",
		textAlign: "center",
		borderRadius: 0,
	},
}));

const TicketsCustom = () => {
	const classes = useStyles();
	const { ticketId } = useParams();

	return (
		<div className={classes.modernContainer}>
			{/* Painel de Tickets */}
			<div className={classes.ticketsPanel}>
				<TicketsManager />
			</div>

			{/* Painel de Chat */}
			<div className={classes.chatPanel}>
				{ticketId ? (
					<Ticket />
				) : (
					<div className={classes.welcomeContainer}>
						<ChatBubbleOutline className={classes.welcomeIcon} />
						<Typography className={classes.welcomeTitle}>
							Selecione um ticket para começar
						</Typography>
						<Typography className={classes.welcomeSubtitle}>
							Escolha um atendimento na lista ao lado para visualizar a conversa e começar a responder o cliente.
						</Typography>
					</div>
				)}
			</div>
		</div>
	);
};

export default TicketsCustom;
