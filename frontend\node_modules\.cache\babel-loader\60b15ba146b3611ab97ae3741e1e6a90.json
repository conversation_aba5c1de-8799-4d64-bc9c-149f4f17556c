{"ast": null, "code": "var _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\nimport { ColorWrap, Hue } from '../common';\nimport HuePointer from './HuePointer';\nexport var HuePicker = function HuePicker(_ref) {\n  var width = _ref.width,\n    height = _ref.height,\n    onChange = _ref.onChange,\n    hsl = _ref.hsl,\n    direction = _ref.direction,\n    pointer = _ref.pointer,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var styles = reactCSS(merge({\n    'default': {\n      picker: {\n        position: 'relative',\n        width: width,\n        height: height\n      },\n      hue: {\n        radius: '2px'\n      }\n    }\n  }, passedStyles));\n\n  // Overwrite to provide pure hue color\n  var handleChange = function handleChange(data) {\n    return onChange({\n      a: 1,\n      h: data.h,\n      l: 0.5,\n      s: 1\n    });\n  };\n  return React.createElement('div', {\n    style: styles.picker,\n    className: 'hue-picker ' + className\n  }, React.createElement(Hue, _extends({}, styles.hue, {\n    hsl: hsl,\n    pointer: pointer,\n    onChange: handleChange,\n    direction: direction\n  })));\n};\nHuePicker.propTypes = {\n  styles: PropTypes.object\n};\nHuePicker.defaultProps = {\n  width: '316px',\n  height: '16px',\n  direction: 'horizontal',\n  pointer: HuePointer,\n  styles: {}\n};\nexport default ColorWrap(HuePicker);", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "React", "PropTypes", "reactCSS", "merge", "ColorWrap", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>ePicker", "_ref", "width", "height", "onChange", "hsl", "direction", "pointer", "_ref$styles", "styles", "passedStyles", "undefined", "_ref$className", "className", "picker", "position", "hue", "radius", "handleChange", "data", "a", "h", "l", "s", "createElement", "style", "propTypes", "object", "defaultProps"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/hue/Hue.js"], "sourcesContent": ["var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\n\nimport { ColorWrap, Hue } from '../common';\nimport HuePointer from './HuePointer';\n\nexport var HuePicker = function HuePicker(_ref) {\n  var width = _ref.width,\n      height = _ref.height,\n      onChange = _ref.onChange,\n      hsl = _ref.hsl,\n      direction = _ref.direction,\n      pointer = _ref.pointer,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var styles = reactCSS(merge({\n    'default': {\n      picker: {\n        position: 'relative',\n        width: width,\n        height: height\n      },\n      hue: {\n        radius: '2px'\n      }\n    }\n  }, passedStyles));\n\n  // Overwrite to provide pure hue color\n  var handleChange = function handleChange(data) {\n    return onChange({ a: 1, h: data.h, l: 0.5, s: 1 });\n  };\n\n  return React.createElement(\n    'div',\n    { style: styles.picker, className: 'hue-picker ' + className },\n    React.createElement(Hue, _extends({}, styles.hue, {\n      hsl: hsl,\n      pointer: pointer,\n      onChange: handleChange,\n      direction: direction\n    }))\n  );\n};\n\nHuePicker.propTypes = {\n  styles: PropTypes.object\n};\nHuePicker.defaultProps = {\n  width: '316px',\n  height: '16px',\n  direction: 'horizontal',\n  pointer: HuePointer,\n  styles: {}\n};\n\nexport default ColorWrap(HuePicker);"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,OAAOS,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,SAAS,EAAEC,GAAG,QAAQ,WAAW;AAC1C,OAAOC,UAAU,MAAM,cAAc;AAErC,OAAO,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,IAAI,EAAE;EAC9C,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,GAAG,GAAGJ,IAAI,CAACI,GAAG;IACdC,SAAS,GAAGL,IAAI,CAACK,SAAS;IAC1BC,OAAO,GAAGN,IAAI,CAACM,OAAO;IACtBC,WAAW,GAAGP,IAAI,CAACQ,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,cAAc,GAAGX,IAAI,CAACY,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKD,SAAS,GAAG,EAAE,GAAGC,cAAc;EAElE,IAAIH,MAAM,GAAGd,QAAQ,CAACC,KAAK,CAAC;IAC1B,SAAS,EAAE;MACTkB,MAAM,EAAE;QACNC,QAAQ,EAAE,UAAU;QACpBb,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC;MACDa,GAAG,EAAE;QACHC,MAAM,EAAE;MACV;IACF;EACF,CAAC,EAAEP,YAAY,CAAC,CAAC;;EAEjB;EACA,IAAIQ,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;IAC7C,OAAOf,QAAQ,CAAC;MAAEgB,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAEF,IAAI,CAACE,CAAC;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAE,CAAC,CAAC;EACpD,CAAC;EAED,OAAO9B,KAAK,CAAC+B,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAEhB,MAAM,CAACK,MAAM;IAAED,SAAS,EAAE,aAAa,GAAGA;EAAU,CAAC,EAC9DpB,KAAK,CAAC+B,aAAa,CAAC1B,GAAG,EAAEjB,QAAQ,CAAC,CAAC,CAAC,EAAE4B,MAAM,CAACO,GAAG,EAAE;IAChDX,GAAG,EAAEA,GAAG;IACRE,OAAO,EAAEA,OAAO;IAChBH,QAAQ,EAAEc,YAAY;IACtBZ,SAAS,EAAEA;EACb,CAAC,CAAC,CACJ,CAAC;AACH,CAAC;AAEDN,SAAS,CAAC0B,SAAS,GAAG;EACpBjB,MAAM,EAAEf,SAAS,CAACiC;AACpB,CAAC;AACD3B,SAAS,CAAC4B,YAAY,GAAG;EACvB1B,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,MAAM;EACdG,SAAS,EAAE,YAAY;EACvBC,OAAO,EAAER,UAAU;EACnBU,MAAM,EAAE,CAAC;AACX,CAAC;AAED,eAAeZ,SAAS,CAACG,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}