import React, { useState, useContext } from "react";
import { Link as RouterLink } from "react-router-dom";

import CssBaseline from "@material-ui/core/CssBaseline";
import TextField from "@material-ui/core/TextField";
import Link from "@material-ui/core/Link";
import Grid from "@material-ui/core/Grid";
import Box from "@material-ui/core/Box";
import Typography from "@material-ui/core/Typography";
import { makeStyles } from "@material-ui/core/styles";
import Container from "@material-ui/core/Container";
import Paper from "@material-ui/core/Paper";
import { LockOutlined, Email, Visibility, VisibilityOff } from "@material-ui/icons";
import { IconButton, InputAdornment } from "@material-ui/core";

import { i18n } from "../../translate/i18n";
import { AuthContext } from "../../context/Auth/AuthContext";
import ModernButton from "../../components/ModernButton";
import logo from "../../assets/logologin.png";

// const Copyright = () => {
// 	return (
// 		<Typography variant="body2" color="textSecondary" align="center">
// 			{"Copyleft "}
// 			<Link color="inherit" href="https://github.com/canove">
// 				Canove
// 			</Link>{" "}
// 			{new Date().getFullYear()}
// 			{"."}
// 		</Typography>
// 	);
// };

const useStyles = makeStyles(theme => ({
  root: {
    width: "100vw",
    height: "100vh",
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
    }
  },
  container: {
    position: 'relative',
    zIndex: 1,
  },
  paper: {
    backgroundColor: "white",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    padding: theme.spacing(6, 4),
    borderRadius: 24,
    boxShadow: "0 20px 60px rgba(0, 0, 0, 0.15)",
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    maxWidth: 400,
    width: '100%',
  },
  logoContainer: {
    marginBottom: theme.spacing(3),
    padding: theme.spacing(2),
    borderRadius: 16,
    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
  },
  logo: {
    height: 60,
    width: 'auto',
    maxWidth: '100%',
  },
  title: {
    fontSize: '1.75rem',
    fontWeight: 700,
    color: theme.palette.text.primary,
    marginBottom: theme.spacing(1),
    textAlign: 'center',
  },
  subtitle: {
    fontSize: '0.875rem',
    color: theme.palette.text.secondary,
    marginBottom: theme.spacing(4),
    textAlign: 'center',
  },
  form: {
    width: "100%",
    marginTop: theme.spacing(1)
  },
  textField: {
    marginBottom: theme.spacing(2),
    '& .MuiOutlinedInput-root': {
      borderRadius: 12,
      backgroundColor: 'rgba(102, 126, 234, 0.02)',
      '& fieldset': {
        borderColor: 'rgba(102, 126, 234, 0.2)',
      },
      '&:hover fieldset': {
        borderColor: 'rgba(102, 126, 234, 0.4)',
      },
      '&.Mui-focused fieldset': {
        borderColor: theme.palette.primary.main,
        borderWidth: 2,
      }
    },
    '& .MuiInputLabel-root': {
      color: theme.palette.text.secondary,
    }
  },
  submitButton: {
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(2),
    height: 48,
    borderRadius: 12,
  },
  linkContainer: {
    textAlign: 'center',
    marginTop: theme.spacing(2),
  },
  link: {
    color: theme.palette.primary.main,
    textDecoration: 'none',
    fontSize: '0.875rem',
    fontWeight: 500,
    '&:hover': {
      textDecoration: 'underline',
    }
  },
  copyright: {
    marginTop: theme.spacing(4),
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: '0.875rem',
    textAlign: 'center',
  },
  iconAdornment: {
    color: theme.palette.text.secondary,
  }
}));

const Login = () => {
  const classes = useStyles();

  const [user, setUser] = useState({ email: "", password: "" });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const { handleLogin } = useContext(AuthContext);

  const handleChangeInput = e => {
    setUser({ ...user, [e.target.name]: e.target.value });
  };

  const handlSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    try {
      await handleLogin(user);
    } finally {
      setLoading(false);
    }
  };

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className={classes.root}>
      <CssBaseline />
      <Container component="main" maxWidth="sm" className={classes.container}>
        <Paper className={classes.paper} elevation={0}>
          {/* Logo */}
          <div className={classes.logoContainer}>
            <img
              className={classes.logo}
              src={logo}
              alt="WhatTicket Logo"
            />
          </div>

          {/* Title */}
          <Typography className={classes.title}>
            Bem-vindo de volta
          </Typography>
          <Typography className={classes.subtitle}>
            Faça login para acessar sua conta
          </Typography>

          {/* Form */}
          <form className={classes.form} noValidate onSubmit={handlSubmit}>
            <TextField
              variant="outlined"
              required
              fullWidth
              id="email"
              label={i18n.t("login.form.email")}
              name="email"
              value={user.email}
              onChange={handleChangeInput}
              autoComplete="email"
              autoFocus
              className={classes.textField}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Email className={classes.iconAdornment} />
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              variant="outlined"
              required
              fullWidth
              name="password"
              label={i18n.t("login.form.password")}
              type={showPassword ? "text" : "password"}
              id="password"
              value={user.password}
              onChange={handleChangeInput}
              autoComplete="current-password"
              className={classes.textField}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockOutlined className={classes.iconAdornment} />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleTogglePassword}
                      edge="end"
                      size="small"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <ModernButton
              type="submit"
              fullWidth
              variant="primary"
              className={classes.submitButton}
              loading={loading}
              disabled={!user.email || !user.password}
            >
              {loading ? "Entrando..." : "Entrar"}
            </ModernButton>

            <div className={classes.linkContainer}>
              <Link
                component={RouterLink}
                to="/signup"
                className={classes.link}
              >
                Ainda não tem uma conta? Registre-se
              </Link>
            </div>
          </form>
        </Paper>

        {/* Copyright */}
        <Typography className={classes.copyright}>
          © 2024 WhatTicket - Sistema de Atendimento
        </Typography>
      </Container>
    </div>
  );
};

export default Login;
