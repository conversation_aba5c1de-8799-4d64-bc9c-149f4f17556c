{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M9 12c0 1.66 1.34 3 3 3s3-1.34 3-3-1.34-3-3-3-3 1.34-3 3z\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M8 9c0-.55-.45-1-1-1H5.09C6.47 5.61 9.05 4 12 4c3.49 0 6.45 2.24 7.54 **********.***********.68 0 1.18-.67.96-1.31C20.07 4.79 16.36 2 12 2 8.73 2 5.82 3.58 4 6.01V5c0-.55-.45-1-1-1s-1 .45-1 1v4c0 .55.45 1 1 1h4c.55 0 1-.45 1-1zM16 15c0 .55.45 1 1 1h1.91c-1.38 2.39-3.96 4-6.91 4-3.49 0-6.45-2.24-7.54-5.36-.14-.39-.53-.64-.94-.64-.68 0-1.18.67-.96 1.31C3.93 19.21 7.64 22 12 22c3.27 0 6.18-1.58 8-4.01V19c0 .55.45 1 1 1s1-.45 1-1v-4c0-.55-.45-1-1-1h-4c-.55 0-1 .45-1 1z\"\n})), 'FlipCameraAndroidRounded');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "Fragment", "d"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/FlipCameraAndroidRounded.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M9 12c0 1.66 1.34 3 3 3s3-1.34 3-3-1.34-3-3-3-3 1.34-3 3z\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M8 9c0-.55-.45-1-1-1H5.09C6.47 5.61 9.05 4 12 4c3.49 0 6.45 2.24 7.54 **********.***********.68 0 1.18-.67.96-1.31C20.07 4.79 16.36 2 12 2 8.73 2 5.82 3.58 4 6.01V5c0-.55-.45-1-1-1s-1 .45-1 1v4c0 .55.45 1 1 1h4c.55 0 1-.45 1-1zM16 15c0 .55.45 1 1 1h1.91c-1.38 2.39-3.96 4-6.91 4-3.49 0-6.45-2.24-7.54-5.36-.14-.39-.53-.64-.94-.64-.68 0-1.18.67-.96 1.31C3.93 19.21 7.64 22 12 22c3.27 0 6.18-1.58 8-4.01V19c0 .55.45 1 1 1s1-.45 1-1v-4c0-.55-.45-1-1-1h-4c-.55 0-1 .45-1 1z\"\n})), 'FlipCameraAndroidRounded');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAACF,KAAK,CAACG,QAAQ,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC5HE,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,aAAaJ,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC3CE,CAAC,EAAE;AACL,CAAC,CAAC,CAAC,EAAE,0BAA0B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}