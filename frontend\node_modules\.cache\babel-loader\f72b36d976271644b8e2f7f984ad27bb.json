{"ast": null, "code": "import React from 'react';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\nimport * as color from '../../helpers/color';\nimport { ColorWrap, EditableInput, Raised } from '../common';\nexport var Material = function Material(_ref) {\n  var onChange = _ref.onChange,\n    hex = _ref.hex,\n    rgb = _ref.rgb,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var styles = reactCSS(merge({\n    'default': {\n      material: {\n        width: '98px',\n        height: '98px',\n        padding: '16px',\n        fontFamily: 'Roboto'\n      },\n      HEXwrap: {\n        position: 'relative'\n      },\n      HEXinput: {\n        width: '100%',\n        marginTop: '12px',\n        fontSize: '15px',\n        color: '#333',\n        padding: '0px',\n        border: '0px',\n        borderBottom: '2px solid ' + hex,\n        outline: 'none',\n        height: '30px'\n      },\n      HEXlabel: {\n        position: 'absolute',\n        top: '0px',\n        left: '0px',\n        fontSize: '11px',\n        color: '#999999',\n        textTransform: 'capitalize'\n      },\n      Hex: {\n        style: {}\n      },\n      RGBwrap: {\n        position: 'relative'\n      },\n      RGBinput: {\n        width: '100%',\n        marginTop: '12px',\n        fontSize: '15px',\n        color: '#333',\n        padding: '0px',\n        border: '0px',\n        borderBottom: '1px solid #eee',\n        outline: 'none',\n        height: '30px'\n      },\n      RGBlabel: {\n        position: 'absolute',\n        top: '0px',\n        left: '0px',\n        fontSize: '11px',\n        color: '#999999',\n        textTransform: 'capitalize'\n      },\n      split: {\n        display: 'flex',\n        marginRight: '-10px',\n        paddingTop: '11px'\n      },\n      third: {\n        flex: '1',\n        paddingRight: '10px'\n      }\n    }\n  }, passedStyles));\n  var handleChange = function handleChange(data, e) {\n    if (data.hex) {\n      color.isValidHex(data.hex) && onChange({\n        hex: data.hex,\n        source: 'hex'\n      }, e);\n    } else if (data.r || data.g || data.b) {\n      onChange({\n        r: data.r || rgb.r,\n        g: data.g || rgb.g,\n        b: data.b || rgb.b,\n        source: 'rgb'\n      }, e);\n    }\n  };\n  return React.createElement(Raised, {\n    styles: passedStyles\n  }, React.createElement('div', {\n    style: styles.material,\n    className: 'material-picker ' + className\n  }, React.createElement(EditableInput, {\n    style: {\n      wrap: styles.HEXwrap,\n      input: styles.HEXinput,\n      label: styles.HEXlabel\n    },\n    label: 'hex',\n    value: hex,\n    onChange: handleChange\n  }), React.createElement('div', {\n    style: styles.split,\n    className: 'flexbox-fix'\n  }, React.createElement('div', {\n    style: styles.third\n  }, React.createElement(EditableInput, {\n    style: {\n      wrap: styles.RGBwrap,\n      input: styles.RGBinput,\n      label: styles.RGBlabel\n    },\n    label: 'r',\n    value: rgb.r,\n    onChange: handleChange\n  })), React.createElement('div', {\n    style: styles.third\n  }, React.createElement(EditableInput, {\n    style: {\n      wrap: styles.RGBwrap,\n      input: styles.RGBinput,\n      label: styles.RGBlabel\n    },\n    label: 'g',\n    value: rgb.g,\n    onChange: handleChange\n  })), React.createElement('div', {\n    style: styles.third\n  }, React.createElement(EditableInput, {\n    style: {\n      wrap: styles.RGBwrap,\n      input: styles.RGBinput,\n      label: styles.RGBlabel\n    },\n    label: 'b',\n    value: rgb.b,\n    onChange: handleChange\n  })))));\n};\nexport default ColorWrap(Material);", "map": {"version": 3, "names": ["React", "reactCSS", "merge", "color", "ColorWrap", "EditableInput", "Raised", "Material", "_ref", "onChange", "hex", "rgb", "_ref$styles", "styles", "passedStyles", "undefined", "_ref$className", "className", "material", "width", "height", "padding", "fontFamily", "HEXwrap", "position", "HEXinput", "marginTop", "fontSize", "border", "borderBottom", "outline", "HEXlabel", "top", "left", "textTransform", "Hex", "style", "RGBwrap", "RGBinput", "RGBlabel", "split", "display", "marginRight", "paddingTop", "third", "flex", "paddingRight", "handleChange", "data", "e", "isValidHex", "source", "r", "g", "b", "createElement", "wrap", "input", "label", "value"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/material/Material.js"], "sourcesContent": ["import React from 'react';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\nimport * as color from '../../helpers/color';\n\nimport { ColorWrap, EditableInput, Raised } from '../common';\n\nexport var Material = function Material(_ref) {\n  var onChange = _ref.onChange,\n      hex = _ref.hex,\n      rgb = _ref.rgb,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var styles = reactCSS(merge({\n    'default': {\n      material: {\n        width: '98px',\n        height: '98px',\n        padding: '16px',\n        fontFamily: 'Roboto'\n      },\n      HEXwrap: {\n        position: 'relative'\n      },\n      HEXinput: {\n        width: '100%',\n        marginTop: '12px',\n        fontSize: '15px',\n        color: '#333',\n        padding: '0px',\n        border: '0px',\n        borderBottom: '2px solid ' + hex,\n        outline: 'none',\n        height: '30px'\n      },\n      HEXlabel: {\n        position: 'absolute',\n        top: '0px',\n        left: '0px',\n        fontSize: '11px',\n        color: '#999999',\n        textTransform: 'capitalize'\n      },\n      Hex: {\n        style: {}\n      },\n      RGBwrap: {\n        position: 'relative'\n      },\n      RGBinput: {\n        width: '100%',\n        marginTop: '12px',\n        fontSize: '15px',\n        color: '#333',\n        padding: '0px',\n        border: '0px',\n        borderBottom: '1px solid #eee',\n        outline: 'none',\n        height: '30px'\n      },\n      RGBlabel: {\n        position: 'absolute',\n        top: '0px',\n        left: '0px',\n        fontSize: '11px',\n        color: '#999999',\n        textTransform: 'capitalize'\n      },\n      split: {\n        display: 'flex',\n        marginRight: '-10px',\n        paddingTop: '11px'\n      },\n      third: {\n        flex: '1',\n        paddingRight: '10px'\n      }\n    }\n  }, passedStyles));\n\n  var handleChange = function handleChange(data, e) {\n    if (data.hex) {\n      color.isValidHex(data.hex) && onChange({\n        hex: data.hex,\n        source: 'hex'\n      }, e);\n    } else if (data.r || data.g || data.b) {\n      onChange({\n        r: data.r || rgb.r,\n        g: data.g || rgb.g,\n        b: data.b || rgb.b,\n        source: 'rgb'\n      }, e);\n    }\n  };\n\n  return React.createElement(\n    Raised,\n    { styles: passedStyles },\n    React.createElement(\n      'div',\n      { style: styles.material, className: 'material-picker ' + className },\n      React.createElement(EditableInput, {\n        style: { wrap: styles.HEXwrap, input: styles.HEXinput, label: styles.HEXlabel },\n        label: 'hex',\n        value: hex,\n        onChange: handleChange\n      }),\n      React.createElement(\n        'div',\n        { style: styles.split, className: 'flexbox-fix' },\n        React.createElement(\n          'div',\n          { style: styles.third },\n          React.createElement(EditableInput, {\n            style: { wrap: styles.RGBwrap, input: styles.RGBinput, label: styles.RGBlabel },\n            label: 'r', value: rgb.r,\n            onChange: handleChange\n          })\n        ),\n        React.createElement(\n          'div',\n          { style: styles.third },\n          React.createElement(EditableInput, {\n            style: { wrap: styles.RGBwrap, input: styles.RGBinput, label: styles.RGBlabel },\n            label: 'g',\n            value: rgb.g,\n            onChange: handleChange\n          })\n        ),\n        React.createElement(\n          'div',\n          { style: styles.third },\n          React.createElement(EditableInput, {\n            style: { wrap: styles.RGBwrap, input: styles.RGBinput, label: styles.RGBlabel },\n            label: 'b',\n            value: rgb.b,\n            onChange: handleChange\n          })\n        )\n      )\n    )\n  );\n};\n\nexport default ColorWrap(Material);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAO,KAAKC,KAAK,MAAM,qBAAqB;AAE5C,SAASC,SAAS,EAAEC,aAAa,EAAEC,MAAM,QAAQ,WAAW;AAE5D,OAAO,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;EAC5C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,GAAG,GAAGF,IAAI,CAACE,GAAG;IACdC,GAAG,GAAGH,IAAI,CAACG,GAAG;IACdC,WAAW,GAAGJ,IAAI,CAACK,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,cAAc,GAAGR,IAAI,CAACS,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKD,SAAS,GAAG,EAAE,GAAGC,cAAc;EAElE,IAAIH,MAAM,GAAGZ,QAAQ,CAACC,KAAK,CAAC;IAC1B,SAAS,EAAE;MACTgB,QAAQ,EAAE;QACRC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE;MACd,CAAC;MACDC,OAAO,EAAE;QACPC,QAAQ,EAAE;MACZ,CAAC;MACDC,QAAQ,EAAE;QACRN,KAAK,EAAE,MAAM;QACbO,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBxB,KAAK,EAAE,MAAM;QACbkB,OAAO,EAAE,KAAK;QACdO,MAAM,EAAE,KAAK;QACbC,YAAY,EAAE,YAAY,GAAGnB,GAAG;QAChCoB,OAAO,EAAE,MAAM;QACfV,MAAM,EAAE;MACV,CAAC;MACDW,QAAQ,EAAE;QACRP,QAAQ,EAAE,UAAU;QACpBQ,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE,KAAK;QACXN,QAAQ,EAAE,MAAM;QAChBxB,KAAK,EAAE,SAAS;QAChB+B,aAAa,EAAE;MACjB,CAAC;MACDC,GAAG,EAAE;QACHC,KAAK,EAAE,CAAC;MACV,CAAC;MACDC,OAAO,EAAE;QACPb,QAAQ,EAAE;MACZ,CAAC;MACDc,QAAQ,EAAE;QACRnB,KAAK,EAAE,MAAM;QACbO,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBxB,KAAK,EAAE,MAAM;QACbkB,OAAO,EAAE,KAAK;QACdO,MAAM,EAAE,KAAK;QACbC,YAAY,EAAE,gBAAgB;QAC9BC,OAAO,EAAE,MAAM;QACfV,MAAM,EAAE;MACV,CAAC;MACDmB,QAAQ,EAAE;QACRf,QAAQ,EAAE,UAAU;QACpBQ,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE,KAAK;QACXN,QAAQ,EAAE,MAAM;QAChBxB,KAAK,EAAE,SAAS;QAChB+B,aAAa,EAAE;MACjB,CAAC;MACDM,KAAK,EAAE;QACLC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,OAAO;QACpBC,UAAU,EAAE;MACd,CAAC;MACDC,KAAK,EAAE;QACLC,IAAI,EAAE,GAAG;QACTC,YAAY,EAAE;MAChB;IACF;EACF,CAAC,EAAEhC,YAAY,CAAC,CAAC;EAEjB,IAAIiC,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,CAAC,EAAE;IAChD,IAAID,IAAI,CAACtC,GAAG,EAAE;MACZP,KAAK,CAAC+C,UAAU,CAACF,IAAI,CAACtC,GAAG,CAAC,IAAID,QAAQ,CAAC;QACrCC,GAAG,EAAEsC,IAAI,CAACtC,GAAG;QACbyC,MAAM,EAAE;MACV,CAAC,EAAEF,CAAC,CAAC;IACP,CAAC,MAAM,IAAID,IAAI,CAACI,CAAC,IAAIJ,IAAI,CAACK,CAAC,IAAIL,IAAI,CAACM,CAAC,EAAE;MACrC7C,QAAQ,CAAC;QACP2C,CAAC,EAAEJ,IAAI,CAACI,CAAC,IAAIzC,GAAG,CAACyC,CAAC;QAClBC,CAAC,EAAEL,IAAI,CAACK,CAAC,IAAI1C,GAAG,CAAC0C,CAAC;QAClBC,CAAC,EAAEN,IAAI,CAACM,CAAC,IAAI3C,GAAG,CAAC2C,CAAC;QAClBH,MAAM,EAAE;MACV,CAAC,EAAEF,CAAC,CAAC;IACP;EACF,CAAC;EAED,OAAOjD,KAAK,CAACuD,aAAa,CACxBjD,MAAM,EACN;IAAEO,MAAM,EAAEC;EAAa,CAAC,EACxBd,KAAK,CAACuD,aAAa,CACjB,KAAK,EACL;IAAEnB,KAAK,EAAEvB,MAAM,CAACK,QAAQ;IAAED,SAAS,EAAE,kBAAkB,GAAGA;EAAU,CAAC,EACrEjB,KAAK,CAACuD,aAAa,CAAClD,aAAa,EAAE;IACjC+B,KAAK,EAAE;MAAEoB,IAAI,EAAE3C,MAAM,CAACU,OAAO;MAAEkC,KAAK,EAAE5C,MAAM,CAACY,QAAQ;MAAEiC,KAAK,EAAE7C,MAAM,CAACkB;IAAS,CAAC;IAC/E2B,KAAK,EAAE,KAAK;IACZC,KAAK,EAAEjD,GAAG;IACVD,QAAQ,EAAEsC;EACZ,CAAC,CAAC,EACF/C,KAAK,CAACuD,aAAa,CACjB,KAAK,EACL;IAAEnB,KAAK,EAAEvB,MAAM,CAAC2B,KAAK;IAAEvB,SAAS,EAAE;EAAc,CAAC,EACjDjB,KAAK,CAACuD,aAAa,CACjB,KAAK,EACL;IAAEnB,KAAK,EAAEvB,MAAM,CAAC+B;EAAM,CAAC,EACvB5C,KAAK,CAACuD,aAAa,CAAClD,aAAa,EAAE;IACjC+B,KAAK,EAAE;MAAEoB,IAAI,EAAE3C,MAAM,CAACwB,OAAO;MAAEoB,KAAK,EAAE5C,MAAM,CAACyB,QAAQ;MAAEoB,KAAK,EAAE7C,MAAM,CAAC0B;IAAS,CAAC;IAC/EmB,KAAK,EAAE,GAAG;IAAEC,KAAK,EAAEhD,GAAG,CAACyC,CAAC;IACxB3C,QAAQ,EAAEsC;EACZ,CAAC,CACH,CAAC,EACD/C,KAAK,CAACuD,aAAa,CACjB,KAAK,EACL;IAAEnB,KAAK,EAAEvB,MAAM,CAAC+B;EAAM,CAAC,EACvB5C,KAAK,CAACuD,aAAa,CAAClD,aAAa,EAAE;IACjC+B,KAAK,EAAE;MAAEoB,IAAI,EAAE3C,MAAM,CAACwB,OAAO;MAAEoB,KAAK,EAAE5C,MAAM,CAACyB,QAAQ;MAAEoB,KAAK,EAAE7C,MAAM,CAAC0B;IAAS,CAAC;IAC/EmB,KAAK,EAAE,GAAG;IACVC,KAAK,EAAEhD,GAAG,CAAC0C,CAAC;IACZ5C,QAAQ,EAAEsC;EACZ,CAAC,CACH,CAAC,EACD/C,KAAK,CAACuD,aAAa,CACjB,KAAK,EACL;IAAEnB,KAAK,EAAEvB,MAAM,CAAC+B;EAAM,CAAC,EACvB5C,KAAK,CAACuD,aAAa,CAAClD,aAAa,EAAE;IACjC+B,KAAK,EAAE;MAAEoB,IAAI,EAAE3C,MAAM,CAACwB,OAAO;MAAEoB,KAAK,EAAE5C,MAAM,CAACyB,QAAQ;MAAEoB,KAAK,EAAE7C,MAAM,CAAC0B;IAAS,CAAC;IAC/EmB,KAAK,EAAE,GAAG;IACVC,KAAK,EAAEhD,GAAG,CAAC2C,CAAC;IACZ7C,QAAQ,EAAEsC;EACZ,CAAC,CACH,CACF,CACF,CACF,CAAC;AACH,CAAC;AAED,eAAe3C,SAAS,CAACG,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}