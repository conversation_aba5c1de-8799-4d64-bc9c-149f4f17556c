{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(\"path\", {\n  d: \"M8 9v1.5h2.25V15h1.5v-4.5H14V9H8zM7 9H2v6h1.5v-1.5h2V15H7V9zm-1.5 3h-2v-1.5h2V12zM22 9h-6.5v6H17v-4.5h1V14h1.5v-3.51h1V15H22V9z\"\n}), 'AtmSharp');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "d"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/AtmSharp.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M8 9v1.5h2.25V15h1.5v-4.5H14V9H8zM7 9H2v6h1.5v-1.5h2V15H7V9zm-1.5 3h-2v-1.5h2V12zM22 9h-6.5v6H17v-4.5h1V14h1.5v-3.51h1V15H22V9z\"\n}), 'AtmSharp');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EACrEC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}