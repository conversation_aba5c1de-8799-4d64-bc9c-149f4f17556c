import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  Box,
  Chip
} from '@material-ui/core';

const useStyles = makeStyles((theme) => ({
  sidebarContainer: {
    height: '100%',
    background: 'linear-gradient(180deg, #ffffff 0%, #f8fafc 100%)',
    borderRight: 'none',
    boxShadow: '4px 0 20px rgba(0,0,0,0.08)',
  },
  logoSection: {
    padding: '24px 20px',
    borderBottom: '1px solid rgba(0,0,0,0.06)',
    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',
  },
  logoText: {
    fontSize: '1.5rem',
    fontWeight: 700,
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    backgroundClip: 'text',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    textAlign: 'center',
  },
  menuSection: {
    padding: '16px 0',
  },
  sectionTitle: {
    fontSize: '0.75rem',
    fontWeight: 600,
    color: theme.palette.text.secondary,
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
    padding: '8px 20px',
    marginBottom: '8px',
  },
  menuItem: {
    margin: '4px 12px',
    borderRadius: 12,
    padding: '12px 16px',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      backgroundColor: 'rgba(102, 126, 234, 0.08)',
      transform: 'translateX(4px)',
    },
    '&.active': {
      backgroundColor: 'rgba(102, 126, 234, 0.12)',
      borderLeft: `3px solid ${theme.palette.primary.main}`,
      '& .MuiListItemIcon-root': {
        color: theme.palette.primary.main,
      },
      '& .MuiListItemText-primary': {
        color: theme.palette.primary.main,
        fontWeight: 600,
      }
    }
  },
  menuIcon: {
    minWidth: 40,
    color: theme.palette.text.secondary,
    '& .MuiSvgIcon-root': {
      fontSize: '1.25rem',
    }
  },
  menuText: {
    '& .MuiListItemText-primary': {
      fontSize: '0.875rem',
      fontWeight: 500,
      color: theme.palette.text.primary,
    }
  },
  badge: {
    backgroundColor: theme.palette.error.main,
    color: 'white',
    fontSize: '0.75rem',
    height: 20,
    minWidth: 20,
    borderRadius: 10,
  },
  divider: {
    margin: '16px 20px',
    backgroundColor: 'rgba(0,0,0,0.06)',
  }
}));

const ModernSidebar = ({ 
  logo, 
  menuSections = [], 
  activeItem,
  onItemClick 
}) => {
  const classes = useStyles();

  return (
    <div className={classes.sidebarContainer}>
      {/* Logo Section */}
      <div className={classes.logoSection}>
        {logo ? (
          <Box display="flex" justifyContent="center" alignItems="center">
            {logo}
          </Box>
        ) : (
          <Typography className={classes.logoText}>
            WhatTicket
          </Typography>
        )}
      </div>

      {/* Menu Sections */}
      {menuSections.map((section, sectionIndex) => (
        <div key={sectionIndex} className={classes.menuSection}>
          {section.title && (
            <Typography className={classes.sectionTitle}>
              {section.title}
            </Typography>
          )}
          
          <List component="nav" disablePadding>
            {section.items.map((item, itemIndex) => (
              <ListItem
                key={itemIndex}
                button
                className={`${classes.menuItem} ${activeItem === item.id ? 'active' : ''}`}
                onClick={() => onItemClick && onItemClick(item)}
              >
                <ListItemIcon className={classes.menuIcon}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  className={classes.menuText}
                  primary={item.label} 
                />
                {item.badge && (
                  <Chip 
                    size="small" 
                    label={item.badge} 
                    className={classes.badge}
                  />
                )}
              </ListItem>
            ))}
          </List>
          
          {sectionIndex < menuSections.length - 1 && (
            <Divider className={classes.divider} />
          )}
        </div>
      ))}
    </div>
  );
};

export default ModernSidebar;
