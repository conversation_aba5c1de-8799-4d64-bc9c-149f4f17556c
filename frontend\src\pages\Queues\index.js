import React, { useEffect, useReducer, useState } from "react";

import {
  IconButton,
  makeStyles,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
  Box,
  Chip,
} from "@material-ui/core";

import TableRowSkeleton from "../../components/TableRowSkeleton";
import { i18n } from "../../translate/i18n";
import toastError from "../../errors/toastError";
import api from "../../services/api";
import { DeleteOutline, Edit, Add as AddIcon, Queue as QueueIcon } from "@material-ui/icons";
import QueueModal from "../../components/QueueModal";
import { toast } from "react-toastify";
import ConfirmationModal from "../../components/ConfirmationModal";
import { socketConnection } from "../../services/socket";
import ModernPageContainer from "../../components/ModernPageContainer";
import ModernButton from "../../components/ModernButton";

const useStyles = makeStyles((theme) => ({
  tableContainer: {
    borderRadius: 16,
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    border: '1px solid rgba(0,0,0,0.04)',
    overflow: 'hidden',
  },
  tableHeader: {
    backgroundColor: 'rgba(102, 126, 234, 0.05)',
    '& .MuiTableCell-head': {
      fontWeight: 600,
      color: theme.palette.text.primary,
      fontSize: '0.875rem',
    }
  },
  tableRow: {
    '&:hover': {
      backgroundColor: 'rgba(102, 126, 234, 0.02)',
    }
  },
  queueInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(2),
  },
  queueName: {
    fontWeight: 500,
    color: theme.palette.text.primary,
  },
  queueColor: {
    width: 20,
    height: 20,
    borderRadius: '50%',
    border: '2px solid white',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  actionButtons: {
    display: 'flex',
    gap: theme.spacing(1),
  },
  actionButton: {
    padding: theme.spacing(1),
    borderRadius: 8,
    '&:hover': {
      transform: 'scale(1.05)',
    }
  },
  headerActions: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(3),
  },
  sectionTitle: {
    fontSize: '1.25rem',
    fontWeight: 600,
    color: theme.palette.text.primary,
    display: 'flex',
    alignItems: 'center',
    '& svg': {
      marginRight: theme.spacing(1),
      color: theme.palette.primary.main,
    }
  },
  statusChip: {
    fontSize: '0.75rem',
    fontWeight: 500,
  }
}));

const reducer = (state, action) => {
  if (action.type === "LOAD_QUEUES") {
    const queues = action.payload;
    const newQueues = [];

    queues.forEach((queue) => {
      const queueIndex = state.findIndex((q) => q.id === queue.id);
      if (queueIndex !== -1) {
        state[queueIndex] = queue;
      } else {
        newQueues.push(queue);
      }
    });

    return [...state, ...newQueues];
  }

  if (action.type === "UPDATE_QUEUES") {
    const queue = action.payload;
    const queueIndex = state.findIndex((u) => u.id === queue.id);

    if (queueIndex !== -1) {
      state[queueIndex] = queue;
      return [...state];
    } else {
      return [queue, ...state];
    }
  }

  if (action.type === "DELETE_QUEUE") {
    const queueId = action.payload;
    const queueIndex = state.findIndex((q) => q.id === queueId);
    if (queueIndex !== -1) {
      state.splice(queueIndex, 1);
    }
    return [...state];
  }

  if (action.type === "RESET") {
    return [];
  }
};

const Queues = () => {
  const classes = useStyles();

  const [queues, dispatch] = useReducer(reducer, []);
  const [loading, setLoading] = useState(false);

  const [queueModalOpen, setQueueModalOpen] = useState(false);
  const [selectedQueue, setSelectedQueue] = useState(null);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const { data } = await api.get("/queue");
        dispatch({ type: "LOAD_QUEUES", payload: data });

        setLoading(false);
      } catch (err) {
        toastError(err);
        setLoading(false);
      }
    })();
  }, []);

  useEffect(() => {
    const companyId = localStorage.getItem("companyId");
    const socket = socketConnection({ companyId });

    socket.on(`company-${companyId}-queue`, (data) => {
      if (data.action === "update" || data.action === "create") {
        dispatch({ type: "UPDATE_QUEUES", payload: data.queue });
      }

      if (data.action === "delete") {
        dispatch({ type: "DELETE_QUEUE", payload: data.queueId });
      }
    });

    return () => {
      socket.disconnect();
    };
  }, []);

  const handleOpenQueueModal = () => {
    setQueueModalOpen(true);
    setSelectedQueue(null);
  };

  const handleCloseQueueModal = () => {
    setQueueModalOpen(false);
    setSelectedQueue(null);
  };

  const handleEditQueue = (queue) => {
    setSelectedQueue(queue);
    setQueueModalOpen(true);
  };

  const handleCloseConfirmationModal = () => {
    setConfirmModalOpen(false);
    setSelectedQueue(null);
  };

  const handleDeleteQueue = async (queueId) => {
    try {
      await api.delete(`/queue/${queueId}`);
      toast.success(i18n.t("Queue deleted successfully!"));
    } catch (err) {
      toastError(err);
    }
    setSelectedQueue(null);
  };

  return (
    <ModernPageContainer
      title="Filas"
      subtitle="Gerencie as filas de atendimento do sistema"
      breadcrumbs={[
        { label: 'Filas', href: '/queues' }
      ]}
    >
      <ConfirmationModal
        title={
          selectedQueue &&
          `${i18n.t("queues.confirmationModal.deleteTitle")} ${
            selectedQueue.name
          }?`
        }
        open={confirmModalOpen}
        onClose={handleCloseConfirmationModal}
        onConfirm={() => handleDeleteQueue(selectedQueue.id)}
      >
        {i18n.t("queues.confirmationModal.deleteMessage")}
      </ConfirmationModal>

      <QueueModal
        open={queueModalOpen}
        onClose={handleCloseQueueModal}
        queueId={selectedQueue?.id}
      />

      {/* Header Actions */}
      <div className={classes.headerActions}>
        <Typography className={classes.sectionTitle}>
          <QueueIcon />
          Filas de Atendimento
        </Typography>

        <ModernButton
          variant="primary"
          onClick={handleOpenQueueModal}
          startIcon={<AddIcon />}
        >
          {i18n.t("queues.buttons.add")}
        </ModernButton>
      </div>

      {/* Queues Table */}
      <Paper className={classes.tableContainer}>
        <Table size="small">
          <TableHead className={classes.tableHeader}>
            <TableRow>
              <TableCell>{i18n.t("queues.table.name")}</TableCell>
              <TableCell align="center">{i18n.t("queues.table.color")}</TableCell>
              <TableCell>{i18n.t("queues.table.greeting")}</TableCell>
              <TableCell align="center">{i18n.t("queues.table.actions")}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {queues.map((queue) => (
              <TableRow key={queue.id} className={classes.tableRow}>
                <TableCell>
                  <div className={classes.queueInfo}>
                    <div
                      className={classes.queueColor}
                      style={{ backgroundColor: queue.color }}
                    />
                    <Typography className={classes.queueName}>
                      {queue.name}
                    </Typography>
                  </div>
                </TableCell>
                <TableCell align="center">
                  <Chip
                    size="small"
                    label={queue.color}
                    style={{
                      backgroundColor: queue.color,
                      color: 'white',
                      fontWeight: 500
                    }}
                    className={classes.statusChip}
                  />
                </TableCell>
                <TableCell>
                  <Typography
                    variant="body2"
                    noWrap
                    style={{ maxWidth: 300 }}
                    title={queue.greetingMessage}
                  >
                    {queue.greetingMessage || '-'}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <div className={classes.actionButtons}>
                    <IconButton
                      size="small"
                      onClick={() => handleEditQueue(queue)}
                      className={classes.actionButton}
                      style={{ color: '#667eea' }}
                      title="Editar fila"
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => {
                        setSelectedQueue(queue);
                        setConfirmModalOpen(true);
                      }}
                      className={classes.actionButton}
                      style={{ color: '#f56565' }}
                      title="Excluir fila"
                    >
                      <DeleteOutline />
                    </IconButton>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {loading && <TableRowSkeleton columns={4} />}
          </TableBody>
        </Table>
      </Paper>
    </ModernPageContainer>
  );
};

export default Queues;
