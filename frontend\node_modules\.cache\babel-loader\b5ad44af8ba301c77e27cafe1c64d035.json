{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\pages\\\\Users\\\\index.js\";\nimport React, { useState, useEffect, useReducer } from \"react\";\nimport { toast } from \"react-toastify\";\nimport { makeStyles } from \"@material-ui/core/styles\";\nimport Paper from \"@material-ui/core/Paper\";\nimport Table from \"@material-ui/core/Table\";\nimport TableBody from \"@material-ui/core/TableBody\";\nimport TableCell from \"@material-ui/core/TableCell\";\nimport TableHead from \"@material-ui/core/TableHead\";\nimport TableRow from \"@material-ui/core/TableRow\";\nimport IconButton from \"@material-ui/core/IconButton\";\nimport SearchIcon from \"@material-ui/icons/Search\";\nimport TextField from \"@material-ui/core/TextField\";\nimport InputAdornment from \"@material-ui/core/InputAdornment\";\nimport Avatar from \"@material-ui/core/Avatar\";\nimport Chip from \"@material-ui/core/Chip\";\nimport Box from \"@material-ui/core/Box\";\nimport Typography from \"@material-ui/core/Typography\";\nimport DeleteOutlineIcon from \"@material-ui/icons/DeleteOutline\";\nimport EditIcon from \"@material-ui/icons/Edit\";\nimport PersonAddIcon from \"@material-ui/icons/PersonAdd\";\nimport PeopleIcon from \"@material-ui/icons/People\";\nimport ModernPageContainer from \"../../components/ModernPageContainer\";\nimport ModernButton from \"../../components/ModernButton\";\nimport api from \"../../services/api\";\nimport { i18n } from \"../../translate/i18n\";\nimport TableRowSkeleton from \"../../components/TableRowSkeleton\";\nimport UserModal from \"../../components/UserModal\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport toastError from \"../../errors/toastError\";\nimport { socketConnection } from \"../../services/socket\";\nconst reducer = (state, action) => {\n  if (action.type === \"LOAD_USERS\") {\n    const users = action.payload;\n    const newUsers = [];\n    users.forEach(user => {\n      const userIndex = state.findIndex(u => u.id === user.id);\n      if (userIndex !== -1) {\n        state[userIndex] = user;\n      } else {\n        newUsers.push(user);\n      }\n    });\n    return [...state, ...newUsers];\n  }\n  if (action.type === \"UPDATE_USERS\") {\n    const user = action.payload;\n    const userIndex = state.findIndex(u => u.id === user.id);\n    if (userIndex !== -1) {\n      state[userIndex] = user;\n      return [...state];\n    } else {\n      return [user, ...state];\n    }\n  }\n  if (action.type === \"DELETE_USER\") {\n    const userId = action.payload;\n    const userIndex = state.findIndex(u => u.id === userId);\n    if (userIndex !== -1) {\n      state.splice(userIndex, 1);\n    }\n    return [...state];\n  }\n  if (action.type === \"RESET\") {\n    return [];\n  }\n};\nconst useStyles = makeStyles(theme => ({\n  searchContainer: {\n    marginBottom: theme.spacing(3),\n    padding: theme.spacing(3),\n    borderRadius: 16,\n    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n    border: '1px solid rgba(102, 126, 234, 0.1)'\n  },\n  searchField: {\n    '& .MuiOutlinedInput-root': {\n      borderRadius: 12,\n      backgroundColor: 'white'\n    }\n  },\n  tableContainer: {\n    borderRadius: 16,\n    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n    border: '1px solid rgba(0,0,0,0.04)',\n    overflow: 'hidden'\n  },\n  tableHeader: {\n    backgroundColor: 'rgba(102, 126, 234, 0.05)',\n    '& .MuiTableCell-head': {\n      fontWeight: 600,\n      color: theme.palette.text.primary,\n      fontSize: '0.875rem'\n    }\n  },\n  tableRow: {\n    '&:hover': {\n      backgroundColor: 'rgba(102, 126, 234, 0.02)'\n    }\n  },\n  userAvatar: {\n    width: 40,\n    height: 40,\n    backgroundColor: theme.palette.primary.main,\n    fontSize: '1rem',\n    fontWeight: 600\n  },\n  userInfo: {\n    display: 'flex',\n    alignItems: 'center',\n    gap: theme.spacing(2)\n  },\n  userName: {\n    fontWeight: 500,\n    color: theme.palette.text.primary\n  },\n  userEmail: {\n    fontSize: '0.875rem',\n    color: theme.palette.text.secondary\n  },\n  statusChip: {\n    fontWeight: 500,\n    fontSize: '0.75rem'\n  },\n  actionButtons: {\n    display: 'flex',\n    gap: theme.spacing(1)\n  },\n  actionButton: {\n    padding: theme.spacing(1),\n    borderRadius: 8,\n    '&:hover': {\n      transform: 'scale(1.05)'\n    }\n  },\n  headerActions: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: theme.spacing(3)\n  },\n  sectionTitle: {\n    fontSize: '1.25rem',\n    fontWeight: 600,\n    color: theme.palette.text.primary,\n    display: 'flex',\n    alignItems: 'center',\n    '& svg': {\n      marginRight: theme.spacing(1),\n      color: theme.palette.primary.main\n    }\n  }\n}));\nconst Users = () => {\n  const classes = useStyles();\n  const [loading, setLoading] = useState(false);\n  const [pageNumber, setPageNumber] = useState(1);\n  const [hasMore, setHasMore] = useState(false);\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [deletingUser, setDeletingUser] = useState(null);\n  const [userModalOpen, setUserModalOpen] = useState(false);\n  const [confirmModalOpen, setConfirmModalOpen] = useState(false);\n  const [searchParam, setSearchParam] = useState(\"\");\n  const [users, dispatch] = useReducer(reducer, []);\n  useEffect(() => {\n    dispatch({\n      type: \"RESET\"\n    });\n    setPageNumber(1);\n  }, [searchParam]);\n  useEffect(() => {\n    setLoading(true);\n    const delayDebounceFn = setTimeout(() => {\n      const fetchUsers = async () => {\n        try {\n          const {\n            data\n          } = await api.get(\"/users/\", {\n            params: {\n              searchParam,\n              pageNumber\n            }\n          });\n          dispatch({\n            type: \"LOAD_USERS\",\n            payload: data.users\n          });\n          setHasMore(data.hasMore);\n          setLoading(false);\n        } catch (err) {\n          toastError(err);\n        }\n      };\n      fetchUsers();\n    }, 500);\n    return () => clearTimeout(delayDebounceFn);\n  }, [searchParam, pageNumber]);\n  useEffect(() => {\n    const companyId = localStorage.getItem(\"companyId\");\n    const socket = socketConnection({\n      companyId\n    });\n    socket.on(`company-${companyId}-user`, data => {\n      if (data.action === \"update\" || data.action === \"create\") {\n        dispatch({\n          type: \"UPDATE_USERS\",\n          payload: data.user\n        });\n      }\n      if (data.action === \"delete\") {\n        dispatch({\n          type: \"DELETE_USER\",\n          payload: +data.userId\n        });\n      }\n    });\n    return () => {\n      socket.disconnect();\n    };\n  }, []);\n  const handleOpenUserModal = () => {\n    setSelectedUser(null);\n    setUserModalOpen(true);\n  };\n  const handleCloseUserModal = () => {\n    setSelectedUser(null);\n    setUserModalOpen(false);\n  };\n  const handleSearch = event => {\n    setSearchParam(event.target.value.toLowerCase());\n  };\n  const handleEditUser = user => {\n    setSelectedUser(user);\n    setUserModalOpen(true);\n  };\n  const handleDeleteUser = async userId => {\n    try {\n      await api.delete(`/users/${userId}`);\n      toast.success(i18n.t(\"users.toasts.deleted\"));\n    } catch (err) {\n      toastError(err);\n    }\n    setDeletingUser(null);\n    setSearchParam(\"\");\n    setPageNumber(1);\n  };\n  const loadMore = () => {\n    setPageNumber(prevState => prevState + 1);\n  };\n  const handleScroll = e => {\n    if (!hasMore || loading) return;\n    const {\n      scrollTop,\n      scrollHeight,\n      clientHeight\n    } = e.currentTarget;\n    if (scrollHeight - (scrollTop + 100) < clientHeight) {\n      loadMore();\n    }\n  };\n  return /*#__PURE__*/React.createElement(MainContainer, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(ConfirmationModal, {\n    title: deletingUser && `${i18n.t(\"users.confirmationModal.deleteTitle\")} ${deletingUser.name}?`,\n    open: confirmModalOpen,\n    onClose: setConfirmModalOpen,\n    onConfirm: () => handleDeleteUser(deletingUser.id),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }\n  }, i18n.t(\"users.confirmationModal.deleteMessage\")), /*#__PURE__*/React.createElement(UserModal, {\n    open: userModalOpen,\n    onClose: handleCloseUserModal,\n    \"aria-labelledby\": \"form-dialog-title\",\n    userId: selectedUser && selectedUser.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(MainHeader, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Title, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 9\n    }\n  }, i18n.t(\"users.title\")), /*#__PURE__*/React.createElement(MainHeaderButtonsWrapper, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(TextField, {\n    placeholder: i18n.t(\"contacts.searchPlaceholder\"),\n    type: \"search\",\n    value: searchParam,\n    onChange: handleSearch,\n    InputProps: {\n      startAdornment: /*#__PURE__*/React.createElement(InputAdornment, {\n        position: \"start\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 17\n        }\n      }, /*#__PURE__*/React.createElement(SearchIcon, {\n        style: {\n          color: \"gray\"\n        },\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 19\n        }\n      }))\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(Button, {\n    variant: \"contained\",\n    color: \"primary\",\n    onClick: handleOpenUserModal,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 11\n    }\n  }, i18n.t(\"users.buttons.add\")))), /*#__PURE__*/React.createElement(Paper, {\n    className: classes.mainPaper,\n    variant: \"outlined\",\n    onScroll: handleScroll,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Table, {\n    size: \"small\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(TableHead, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(TableRow, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 15\n    }\n  }, i18n.t(\"users.table.name\")), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 15\n    }\n  }, i18n.t(\"users.table.email\")), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 15\n    }\n  }, i18n.t(\"users.table.profile\")), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 15\n    }\n  }, i18n.t(\"users.table.actions\")))), /*#__PURE__*/React.createElement(TableBody, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(React.Fragment, null, users.map(user => /*#__PURE__*/React.createElement(TableRow, {\n    key: user.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 19\n    }\n  }, user.name), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 19\n    }\n  }, user.email), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 19\n    }\n  }, user.profile), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(IconButton, {\n    size: \"small\",\n    onClick: () => handleEditUser(user),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(EditIcon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 23\n    }\n  })), /*#__PURE__*/React.createElement(IconButton, {\n    size: \"small\",\n    onClick: e => {\n      setConfirmModalOpen(true);\n      setDeletingUser(user);\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(DeleteOutlineIcon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 23\n    }\n  }))))), loading && /*#__PURE__*/React.createElement(TableRowSkeleton, {\n    columns: 4,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 27\n    }\n  }))))));\n};\nexport default Users;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useReducer", "toast", "makeStyles", "Paper", "Table", "TableBody", "TableCell", "TableHead", "TableRow", "IconButton", "SearchIcon", "TextField", "InputAdornment", "Avatar", "Chip", "Box", "Typography", "DeleteOutlineIcon", "EditIcon", "PersonAddIcon", "PeopleIcon", "ModernPageContainer", "ModernButton", "api", "i18n", "TableRowSkeleton", "UserModal", "ConfirmationModal", "toastError", "socketConnection", "reducer", "state", "action", "type", "users", "payload", "newUsers", "for<PERSON>ach", "user", "userIndex", "findIndex", "u", "id", "push", "userId", "splice", "useStyles", "theme", "searchContainer", "marginBottom", "spacing", "padding", "borderRadius", "background", "border", "searchField", "backgroundColor", "tableContainer", "boxShadow", "overflow", "tableHeader", "fontWeight", "color", "palette", "text", "primary", "fontSize", "tableRow", "userAvatar", "width", "height", "main", "userInfo", "display", "alignItems", "gap", "userName", "userEmail", "secondary", "statusChip", "actionButtons", "actionButton", "transform", "headerActions", "justifyContent", "sectionTitle", "marginRight", "Users", "classes", "loading", "setLoading", "pageNumber", "setPageNumber", "hasMore", "setHasMore", "selected<PERSON>ser", "setSelectedUser", "deletingUser", "setDeletingUser", "userModalOpen", "setUserModalOpen", "confirmModalOpen", "setConfirmModalOpen", "searchParam", "setSearchParam", "dispatch", "delayDebounceFn", "setTimeout", "fetchUsers", "data", "get", "params", "err", "clearTimeout", "companyId", "localStorage", "getItem", "socket", "on", "disconnect", "handleOpenUserModal", "handleCloseUserModal", "handleSearch", "event", "target", "value", "toLowerCase", "handleEditUser", "handleDeleteUser", "delete", "success", "t", "loadMore", "prevState", "handleScroll", "e", "scrollTop", "scrollHeight", "clientHeight", "currentTarget", "createElement", "MainContainer", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "name", "open", "onClose", "onConfirm", "<PERSON><PERSON><PERSON><PERSON>", "Title", "MainHeaderButtonsWrapper", "placeholder", "onChange", "InputProps", "startAdornment", "position", "style", "<PERSON><PERSON>", "variant", "onClick", "className", "mainPaper", "onScroll", "size", "align", "Fragment", "map", "key", "email", "profile", "columns"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/pages/Users/<USER>"], "sourcesContent": ["import React, { useState, useEffect, useReducer } from \"react\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nimport { makeStyles } from \"@material-ui/core/styles\";\r\nimport Paper from \"@material-ui/core/Paper\";\r\nimport Table from \"@material-ui/core/Table\";\r\nimport TableBody from \"@material-ui/core/TableBody\";\r\nimport TableCell from \"@material-ui/core/TableCell\";\r\nimport TableHead from \"@material-ui/core/TableHead\";\r\nimport TableRow from \"@material-ui/core/TableRow\";\r\nimport IconButton from \"@material-ui/core/IconButton\";\r\nimport SearchIcon from \"@material-ui/icons/Search\";\r\nimport TextField from \"@material-ui/core/TextField\";\r\nimport InputAdornment from \"@material-ui/core/InputAdornment\";\r\nimport Avatar from \"@material-ui/core/Avatar\";\r\nimport Chip from \"@material-ui/core/Chip\";\r\nimport Box from \"@material-ui/core/Box\";\r\nimport Typography from \"@material-ui/core/Typography\";\r\n\r\nimport DeleteOutlineIcon from \"@material-ui/icons/DeleteOutline\";\r\nimport EditIcon from \"@material-ui/icons/Edit\";\r\nimport PersonAddIcon from \"@material-ui/icons/PersonAdd\";\r\nimport PeopleIcon from \"@material-ui/icons/People\";\r\n\r\nimport ModernPageContainer from \"../../components/ModernPageContainer\";\r\nimport ModernButton from \"../../components/ModernButton\";\r\n\r\nimport api from \"../../services/api\";\r\nimport { i18n } from \"../../translate/i18n\";\r\nimport TableRowSkeleton from \"../../components/TableRowSkeleton\";\r\nimport UserModal from \"../../components/UserModal\";\r\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\r\nimport toastError from \"../../errors/toastError\";\r\nimport { socketConnection } from \"../../services/socket\";\r\n\r\nconst reducer = (state, action) => {\r\n  if (action.type === \"LOAD_USERS\") {\r\n    const users = action.payload;\r\n    const newUsers = [];\r\n\r\n    users.forEach((user) => {\r\n      const userIndex = state.findIndex((u) => u.id === user.id);\r\n      if (userIndex !== -1) {\r\n        state[userIndex] = user;\r\n      } else {\r\n        newUsers.push(user);\r\n      }\r\n    });\r\n\r\n    return [...state, ...newUsers];\r\n  }\r\n\r\n  if (action.type === \"UPDATE_USERS\") {\r\n    const user = action.payload;\r\n    const userIndex = state.findIndex((u) => u.id === user.id);\r\n\r\n    if (userIndex !== -1) {\r\n      state[userIndex] = user;\r\n      return [...state];\r\n    } else {\r\n      return [user, ...state];\r\n    }\r\n  }\r\n\r\n  if (action.type === \"DELETE_USER\") {\r\n    const userId = action.payload;\r\n\r\n    const userIndex = state.findIndex((u) => u.id === userId);\r\n    if (userIndex !== -1) {\r\n      state.splice(userIndex, 1);\r\n    }\r\n    return [...state];\r\n  }\r\n\r\n  if (action.type === \"RESET\") {\r\n    return [];\r\n  }\r\n};\r\n\r\nconst useStyles = makeStyles((theme) => ({\r\n  searchContainer: {\r\n    marginBottom: theme.spacing(3),\r\n    padding: theme.spacing(3),\r\n    borderRadius: 16,\r\n    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\r\n    border: '1px solid rgba(102, 126, 234, 0.1)',\r\n  },\r\n  searchField: {\r\n    '& .MuiOutlinedInput-root': {\r\n      borderRadius: 12,\r\n      backgroundColor: 'white',\r\n    }\r\n  },\r\n  tableContainer: {\r\n    borderRadius: 16,\r\n    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\r\n    border: '1px solid rgba(0,0,0,0.04)',\r\n    overflow: 'hidden',\r\n  },\r\n  tableHeader: {\r\n    backgroundColor: 'rgba(102, 126, 234, 0.05)',\r\n    '& .MuiTableCell-head': {\r\n      fontWeight: 600,\r\n      color: theme.palette.text.primary,\r\n      fontSize: '0.875rem',\r\n    }\r\n  },\r\n  tableRow: {\r\n    '&:hover': {\r\n      backgroundColor: 'rgba(102, 126, 234, 0.02)',\r\n    }\r\n  },\r\n  userAvatar: {\r\n    width: 40,\r\n    height: 40,\r\n    backgroundColor: theme.palette.primary.main,\r\n    fontSize: '1rem',\r\n    fontWeight: 600,\r\n  },\r\n  userInfo: {\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    gap: theme.spacing(2),\r\n  },\r\n  userName: {\r\n    fontWeight: 500,\r\n    color: theme.palette.text.primary,\r\n  },\r\n  userEmail: {\r\n    fontSize: '0.875rem',\r\n    color: theme.palette.text.secondary,\r\n  },\r\n  statusChip: {\r\n    fontWeight: 500,\r\n    fontSize: '0.75rem',\r\n  },\r\n  actionButtons: {\r\n    display: 'flex',\r\n    gap: theme.spacing(1),\r\n  },\r\n  actionButton: {\r\n    padding: theme.spacing(1),\r\n    borderRadius: 8,\r\n    '&:hover': {\r\n      transform: 'scale(1.05)',\r\n    }\r\n  },\r\n  headerActions: {\r\n    display: 'flex',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    marginBottom: theme.spacing(3),\r\n  },\r\n  sectionTitle: {\r\n    fontSize: '1.25rem',\r\n    fontWeight: 600,\r\n    color: theme.palette.text.primary,\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    '& svg': {\r\n      marginRight: theme.spacing(1),\r\n      color: theme.palette.primary.main,\r\n    }\r\n  }\r\n}));\r\n\r\nconst Users = () => {\r\n  const classes = useStyles();\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const [pageNumber, setPageNumber] = useState(1);\r\n  const [hasMore, setHasMore] = useState(false);\r\n  const [selectedUser, setSelectedUser] = useState(null);\r\n  const [deletingUser, setDeletingUser] = useState(null);\r\n  const [userModalOpen, setUserModalOpen] = useState(false);\r\n  const [confirmModalOpen, setConfirmModalOpen] = useState(false);\r\n  const [searchParam, setSearchParam] = useState(\"\");\r\n  const [users, dispatch] = useReducer(reducer, []);\r\n\r\n  useEffect(() => {\r\n    dispatch({ type: \"RESET\" });\r\n    setPageNumber(1);\r\n  }, [searchParam]);\r\n\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    const delayDebounceFn = setTimeout(() => {\r\n      const fetchUsers = async () => {\r\n        try {\r\n          const { data } = await api.get(\"/users/\", {\r\n            params: { searchParam, pageNumber },\r\n          });\r\n          dispatch({ type: \"LOAD_USERS\", payload: data.users });\r\n          setHasMore(data.hasMore);\r\n          setLoading(false);\r\n        } catch (err) {\r\n          toastError(err);\r\n        }\r\n      };\r\n      fetchUsers();\r\n    }, 500);\r\n    return () => clearTimeout(delayDebounceFn);\r\n  }, [searchParam, pageNumber]);\r\n\r\n  useEffect(() => {\r\n    const companyId = localStorage.getItem(\"companyId\");\r\n    const socket = socketConnection({ companyId });\r\n\r\n    socket.on(`company-${companyId}-user`, (data) => {\r\n      if (data.action === \"update\" || data.action === \"create\") {\r\n        dispatch({ type: \"UPDATE_USERS\", payload: data.user });\r\n      }\r\n\r\n      if (data.action === \"delete\") {\r\n        dispatch({ type: \"DELETE_USER\", payload: +data.userId });\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      socket.disconnect();\r\n    };\r\n  }, []);\r\n\r\n  const handleOpenUserModal = () => {\r\n    setSelectedUser(null);\r\n    setUserModalOpen(true);\r\n  };\r\n\r\n  const handleCloseUserModal = () => {\r\n    setSelectedUser(null);\r\n    setUserModalOpen(false);\r\n  };\r\n\r\n  const handleSearch = (event) => {\r\n    setSearchParam(event.target.value.toLowerCase());\r\n  };\r\n\r\n  const handleEditUser = (user) => {\r\n    setSelectedUser(user);\r\n    setUserModalOpen(true);\r\n  };\r\n\r\n  const handleDeleteUser = async (userId) => {\r\n    try {\r\n      await api.delete(`/users/${userId}`);\r\n      toast.success(i18n.t(\"users.toasts.deleted\"));\r\n    } catch (err) {\r\n      toastError(err);\r\n    }\r\n    setDeletingUser(null);\r\n    setSearchParam(\"\");\r\n    setPageNumber(1);\r\n  };\r\n\r\n  const loadMore = () => {\r\n    setPageNumber((prevState) => prevState + 1);\r\n  };\r\n\r\n  const handleScroll = (e) => {\r\n    if (!hasMore || loading) return;\r\n    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;\r\n    if (scrollHeight - (scrollTop + 100) < clientHeight) {\r\n      loadMore();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <MainContainer>\r\n      <ConfirmationModal\r\n        title={\r\n          deletingUser &&\r\n          `${i18n.t(\"users.confirmationModal.deleteTitle\")} ${\r\n            deletingUser.name\r\n          }?`\r\n        }\r\n        open={confirmModalOpen}\r\n        onClose={setConfirmModalOpen}\r\n        onConfirm={() => handleDeleteUser(deletingUser.id)}\r\n      >\r\n        {i18n.t(\"users.confirmationModal.deleteMessage\")}\r\n      </ConfirmationModal>\r\n      <UserModal\r\n        open={userModalOpen}\r\n        onClose={handleCloseUserModal}\r\n        aria-labelledby=\"form-dialog-title\"\r\n        userId={selectedUser && selectedUser.id}\r\n      />\r\n      <MainHeader>\r\n        <Title>{i18n.t(\"users.title\")}</Title>\r\n        <MainHeaderButtonsWrapper>\r\n          <TextField\r\n            placeholder={i18n.t(\"contacts.searchPlaceholder\")}\r\n            type=\"search\"\r\n            value={searchParam}\r\n            onChange={handleSearch}\r\n            InputProps={{\r\n              startAdornment: (\r\n                <InputAdornment position=\"start\">\r\n                  <SearchIcon style={{ color: \"gray\" }} />\r\n                </InputAdornment>\r\n              ),\r\n            }}\r\n          />\r\n          <Button\r\n            variant=\"contained\"\r\n            color=\"primary\"\r\n            onClick={handleOpenUserModal}\r\n          >\r\n            {i18n.t(\"users.buttons.add\")}\r\n          </Button>\r\n        </MainHeaderButtonsWrapper>\r\n      </MainHeader>\r\n      <Paper\r\n        className={classes.mainPaper}\r\n        variant=\"outlined\"\r\n        onScroll={handleScroll}\r\n      >\r\n        <Table size=\"small\">\r\n          <TableHead>\r\n            <TableRow>\r\n              <TableCell align=\"center\">{i18n.t(\"users.table.name\")}</TableCell>\r\n              <TableCell align=\"center\">\r\n                {i18n.t(\"users.table.email\")}\r\n              </TableCell>\r\n              <TableCell align=\"center\">\r\n                {i18n.t(\"users.table.profile\")}\r\n              </TableCell>\r\n              <TableCell align=\"center\">\r\n                {i18n.t(\"users.table.actions\")}\r\n              </TableCell>\r\n            </TableRow>\r\n          </TableHead>\r\n          <TableBody>\r\n            <>\r\n              {users.map((user) => (\r\n                <TableRow key={user.id}>\r\n                  <TableCell align=\"center\">{user.name}</TableCell>\r\n                  <TableCell align=\"center\">{user.email}</TableCell>\r\n                  <TableCell align=\"center\">{user.profile}</TableCell>\r\n                  <TableCell align=\"center\">\r\n                    <IconButton\r\n                      size=\"small\"\r\n                      onClick={() => handleEditUser(user)}\r\n                    >\r\n                      <EditIcon />\r\n                    </IconButton>\r\n\r\n                    <IconButton\r\n                      size=\"small\"\r\n                      onClick={(e) => {\r\n                        setConfirmModalOpen(true);\r\n                        setDeletingUser(user);\r\n                      }}\r\n                    >\r\n                      <DeleteOutlineIcon />\r\n                    </IconButton>\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))}\r\n              {loading && <TableRowSkeleton columns={4} />}\r\n            </>\r\n          </TableBody>\r\n        </Table>\r\n      </Paper>\r\n    </MainContainer>\r\n  );\r\n};\r\n\r\nexport default Users;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,OAAOC,IAAI,MAAM,wBAAwB;AACzC,OAAOC,GAAG,MAAM,uBAAuB;AACvC,OAAOC,UAAU,MAAM,8BAA8B;AAErD,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,UAAU,MAAM,2BAA2B;AAElD,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,OAAOC,YAAY,MAAM,+BAA+B;AAExD,OAAOC,GAAG,MAAM,oBAAoB;AACpC,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,UAAU,MAAM,yBAAyB;AAChD,SAASC,gBAAgB,QAAQ,uBAAuB;AAExD,MAAMC,OAAO,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACjC,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,EAAE;IAChC,MAAMC,KAAK,GAAGF,MAAM,CAACG,OAAO;IAC5B,MAAMC,QAAQ,GAAG,EAAE;IAEnBF,KAAK,CAACG,OAAO,CAAEC,IAAI,IAAK;MACtB,MAAMC,SAAS,GAAGR,KAAK,CAACS,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAKJ,IAAI,CAACI,EAAE,CAAC;MAC1D,IAAIH,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBR,KAAK,CAACQ,SAAS,CAAC,GAAGD,IAAI;MACzB,CAAC,MAAM;QACLF,QAAQ,CAACO,IAAI,CAACL,IAAI,CAAC;MACrB;IACF,CAAC,CAAC;IAEF,OAAO,CAAC,GAAGP,KAAK,EAAE,GAAGK,QAAQ,CAAC;EAChC;EAEA,IAAIJ,MAAM,CAACC,IAAI,KAAK,cAAc,EAAE;IAClC,MAAMK,IAAI,GAAGN,MAAM,CAACG,OAAO;IAC3B,MAAMI,SAAS,GAAGR,KAAK,CAACS,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAKJ,IAAI,CAACI,EAAE,CAAC;IAE1D,IAAIH,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBR,KAAK,CAACQ,SAAS,CAAC,GAAGD,IAAI;MACvB,OAAO,CAAC,GAAGP,KAAK,CAAC;IACnB,CAAC,MAAM;MACL,OAAO,CAACO,IAAI,EAAE,GAAGP,KAAK,CAAC;IACzB;EACF;EAEA,IAAIC,MAAM,CAACC,IAAI,KAAK,aAAa,EAAE;IACjC,MAAMW,MAAM,GAAGZ,MAAM,CAACG,OAAO;IAE7B,MAAMI,SAAS,GAAGR,KAAK,CAACS,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAKE,MAAM,CAAC;IACzD,IAAIL,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBR,KAAK,CAACc,MAAM,CAACN,SAAS,EAAE,CAAC,CAAC;IAC5B;IACA,OAAO,CAAC,GAAGR,KAAK,CAAC;EACnB;EAEA,IAAIC,MAAM,CAACC,IAAI,KAAK,OAAO,EAAE;IAC3B,OAAO,EAAE;EACX;AACF,CAAC;AAED,MAAMa,SAAS,GAAG5C,UAAU,CAAE6C,KAAK,KAAM;EACvCC,eAAe,EAAE;IACfC,YAAY,EAAEF,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IAC9BC,OAAO,EAAEJ,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IACzBE,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,sFAAsF;IAClGC,MAAM,EAAE;EACV,CAAC;EACDC,WAAW,EAAE;IACX,0BAA0B,EAAE;MAC1BH,YAAY,EAAE,EAAE;MAChBI,eAAe,EAAE;IACnB;EACF,CAAC;EACDC,cAAc,EAAE;IACdL,YAAY,EAAE,EAAE;IAChBM,SAAS,EAAE,6BAA6B;IACxCJ,MAAM,EAAE,4BAA4B;IACpCK,QAAQ,EAAE;EACZ,CAAC;EACDC,WAAW,EAAE;IACXJ,eAAe,EAAE,2BAA2B;IAC5C,sBAAsB,EAAE;MACtBK,UAAU,EAAE,GAAG;MACfC,KAAK,EAAEf,KAAK,CAACgB,OAAO,CAACC,IAAI,CAACC,OAAO;MACjCC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,QAAQ,EAAE;IACR,SAAS,EAAE;MACTX,eAAe,EAAE;IACnB;EACF,CAAC;EACDY,UAAU,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVd,eAAe,EAAET,KAAK,CAACgB,OAAO,CAACE,OAAO,CAACM,IAAI;IAC3CL,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAE;EACd,CAAC;EACDW,QAAQ,EAAE;IACRC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE5B,KAAK,CAACG,OAAO,CAAC,CAAC;EACtB,CAAC;EACD0B,QAAQ,EAAE;IACRf,UAAU,EAAE,GAAG;IACfC,KAAK,EAAEf,KAAK,CAACgB,OAAO,CAACC,IAAI,CAACC;EAC5B,CAAC;EACDY,SAAS,EAAE;IACTX,QAAQ,EAAE,UAAU;IACpBJ,KAAK,EAAEf,KAAK,CAACgB,OAAO,CAACC,IAAI,CAACc;EAC5B,CAAC;EACDC,UAAU,EAAE;IACVlB,UAAU,EAAE,GAAG;IACfK,QAAQ,EAAE;EACZ,CAAC;EACDc,aAAa,EAAE;IACbP,OAAO,EAAE,MAAM;IACfE,GAAG,EAAE5B,KAAK,CAACG,OAAO,CAAC,CAAC;EACtB,CAAC;EACD+B,YAAY,EAAE;IACZ9B,OAAO,EAAEJ,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;IACzBE,YAAY,EAAE,CAAC;IACf,SAAS,EAAE;MACT8B,SAAS,EAAE;IACb;EACF,CAAC;EACDC,aAAa,EAAE;IACbV,OAAO,EAAE,MAAM;IACfW,cAAc,EAAE,eAAe;IAC/BV,UAAU,EAAE,QAAQ;IACpBzB,YAAY,EAAEF,KAAK,CAACG,OAAO,CAAC,CAAC;EAC/B,CAAC;EACDmC,YAAY,EAAE;IACZnB,QAAQ,EAAE,SAAS;IACnBL,UAAU,EAAE,GAAG;IACfC,KAAK,EAAEf,KAAK,CAACgB,OAAO,CAACC,IAAI,CAACC,OAAO;IACjCQ,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpB,OAAO,EAAE;MACPY,WAAW,EAAEvC,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;MAC7BY,KAAK,EAAEf,KAAK,CAACgB,OAAO,CAACE,OAAO,CAACM;IAC/B;EACF;AACF,CAAC,CAAC,CAAC;AAEH,MAAMgB,KAAK,GAAGA,CAAA,KAAM;EAClB,MAAMC,OAAO,GAAG1C,SAAS,CAAC,CAAC;EAE3B,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6F,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC+F,OAAO,EAAEC,UAAU,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiG,YAAY,EAAEC,eAAe,CAAC,GAAGlG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmG,YAAY,EAAEC,eAAe,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqG,aAAa,EAAEC,gBAAgB,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyG,WAAW,EAAEC,cAAc,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoC,KAAK,EAAEuE,QAAQ,CAAC,GAAGzG,UAAU,CAAC8B,OAAO,EAAE,EAAE,CAAC;EAEjD/B,SAAS,CAAC,MAAM;IACd0G,QAAQ,CAAC;MAAExE,IAAI,EAAE;IAAQ,CAAC,CAAC;IAC3B2D,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC,EAAE,CAACW,WAAW,CAAC,CAAC;EAEjBxG,SAAS,CAAC,MAAM;IACd2F,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMgB,eAAe,GAAGC,UAAU,CAAC,MAAM;MACvC,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;QAC7B,IAAI;UACF,MAAM;YAAEC;UAAK,CAAC,GAAG,MAAMtF,GAAG,CAACuF,GAAG,CAAC,SAAS,EAAE;YACxCC,MAAM,EAAE;cAAER,WAAW;cAAEZ;YAAW;UACpC,CAAC,CAAC;UACFc,QAAQ,CAAC;YAAExE,IAAI,EAAE,YAAY;YAAEE,OAAO,EAAE0E,IAAI,CAAC3E;UAAM,CAAC,CAAC;UACrD4D,UAAU,CAACe,IAAI,CAAChB,OAAO,CAAC;UACxBH,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,CAAC,OAAOsB,GAAG,EAAE;UACZpF,UAAU,CAACoF,GAAG,CAAC;QACjB;MACF,CAAC;MACDJ,UAAU,CAAC,CAAC;IACd,CAAC,EAAE,GAAG,CAAC;IACP,OAAO,MAAMK,YAAY,CAACP,eAAe,CAAC;EAC5C,CAAC,EAAE,CAACH,WAAW,EAAEZ,UAAU,CAAC,CAAC;EAE7B5F,SAAS,CAAC,MAAM;IACd,MAAMmH,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,MAAM,GAAGxF,gBAAgB,CAAC;MAAEqF;IAAU,CAAC,CAAC;IAE9CG,MAAM,CAACC,EAAE,CAAC,WAAWJ,SAAS,OAAO,EAAGL,IAAI,IAAK;MAC/C,IAAIA,IAAI,CAAC7E,MAAM,KAAK,QAAQ,IAAI6E,IAAI,CAAC7E,MAAM,KAAK,QAAQ,EAAE;QACxDyE,QAAQ,CAAC;UAAExE,IAAI,EAAE,cAAc;UAAEE,OAAO,EAAE0E,IAAI,CAACvE;QAAK,CAAC,CAAC;MACxD;MAEA,IAAIuE,IAAI,CAAC7E,MAAM,KAAK,QAAQ,EAAE;QAC5ByE,QAAQ,CAAC;UAAExE,IAAI,EAAE,aAAa;UAAEE,OAAO,EAAE,CAAC0E,IAAI,CAACjE;QAAO,CAAC,CAAC;MAC1D;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACXyE,MAAM,CAACE,UAAU,CAAC,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCxB,eAAe,CAAC,IAAI,CAAC;IACrBI,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMqB,oBAAoB,GAAGA,CAAA,KAAM;IACjCzB,eAAe,CAAC,IAAI,CAAC;IACrBI,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,MAAMsB,YAAY,GAAIC,KAAK,IAAK;IAC9BnB,cAAc,CAACmB,KAAK,CAACC,MAAM,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAMC,cAAc,GAAIzF,IAAI,IAAK;IAC/B0D,eAAe,CAAC1D,IAAI,CAAC;IACrB8D,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM4B,gBAAgB,GAAG,MAAOpF,MAAM,IAAK;IACzC,IAAI;MACF,MAAMrB,GAAG,CAAC0G,MAAM,CAAC,UAAUrF,MAAM,EAAE,CAAC;MACpC3C,KAAK,CAACiI,OAAO,CAAC1G,IAAI,CAAC2G,CAAC,CAAC,sBAAsB,CAAC,CAAC;IAC/C,CAAC,CAAC,OAAOnB,GAAG,EAAE;MACZpF,UAAU,CAACoF,GAAG,CAAC;IACjB;IACAd,eAAe,CAAC,IAAI,CAAC;IACrBM,cAAc,CAAC,EAAE,CAAC;IAClBZ,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,MAAMwC,QAAQ,GAAGA,CAAA,KAAM;IACrBxC,aAAa,CAAEyC,SAAS,IAAKA,SAAS,GAAG,CAAC,CAAC;EAC7C,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,IAAI,CAAC1C,OAAO,IAAIJ,OAAO,EAAE;IACzB,MAAM;MAAE+C,SAAS;MAAEC,YAAY;MAAEC;IAAa,CAAC,GAAGH,CAAC,CAACI,aAAa;IACjE,IAAIF,YAAY,IAAID,SAAS,GAAG,GAAG,CAAC,GAAGE,YAAY,EAAE;MACnDN,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,oBACEvI,KAAA,CAAA+I,aAAA,CAACC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACZtJ,KAAA,CAAA+I,aAAA,CAACjH,iBAAiB;IAChByH,KAAK,EACHnD,YAAY,IACZ,GAAGzE,IAAI,CAAC2G,CAAC,CAAC,qCAAqC,CAAC,IAC9ClC,YAAY,CAACoD,IAAI,GAEpB;IACDC,IAAI,EAAEjD,gBAAiB;IACvBkD,OAAO,EAAEjD,mBAAoB;IAC7BkD,SAAS,EAAEA,CAAA,KAAMxB,gBAAgB,CAAC/B,YAAY,CAACvD,EAAE,CAAE;IAAAoG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAElD3H,IAAI,CAAC2G,CAAC,CAAC,uCAAuC,CAC9B,CAAC,eACpBtI,KAAA,CAAA+I,aAAA,CAAClH,SAAS;IACR4H,IAAI,EAAEnD,aAAc;IACpBoD,OAAO,EAAE9B,oBAAqB;IAC9B,mBAAgB,mBAAmB;IACnC7E,MAAM,EAAEmD,YAAY,IAAIA,YAAY,CAACrD,EAAG;IAAAoG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACzC,CAAC,eACFtJ,KAAA,CAAA+I,aAAA,CAACa,UAAU;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTtJ,KAAA,CAAA+I,aAAA,CAACc,KAAK;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE3H,IAAI,CAAC2G,CAAC,CAAC,aAAa,CAAS,CAAC,eACtCtI,KAAA,CAAA+I,aAAA,CAACe,wBAAwB;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBtJ,KAAA,CAAA+I,aAAA,CAACjI,SAAS;IACRiJ,WAAW,EAAEpI,IAAI,CAAC2G,CAAC,CAAC,4BAA4B,CAAE;IAClDlG,IAAI,EAAC,QAAQ;IACb4F,KAAK,EAAEtB,WAAY;IACnBsD,QAAQ,EAAEnC,YAAa;IACvBoC,UAAU,EAAE;MACVC,cAAc,eACZlK,KAAA,CAAA+I,aAAA,CAAChI,cAAc;QAACoJ,QAAQ,EAAC,OAAO;QAAAlB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBAC9BtJ,KAAA,CAAA+I,aAAA,CAAClI,UAAU;QAACuJ,KAAK,EAAE;UAAEnG,KAAK,EAAE;QAAO,CAAE;QAAAgF,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CACzB;IAEpB,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACH,CAAC,eACFtJ,KAAA,CAAA+I,aAAA,CAACsB,MAAM;IACLC,OAAO,EAAC,WAAW;IACnBrG,KAAK,EAAC,SAAS;IACfsG,OAAO,EAAE5C,mBAAoB;IAAAsB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAE5B3H,IAAI,CAAC2G,CAAC,CAAC,mBAAmB,CACrB,CACgB,CAChB,CAAC,eACbtI,KAAA,CAAA+I,aAAA,CAACzI,KAAK;IACJkK,SAAS,EAAE7E,OAAO,CAAC8E,SAAU;IAC7BH,OAAO,EAAC,UAAU;IAClBI,QAAQ,EAAEjC,YAAa;IAAAQ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvBtJ,KAAA,CAAA+I,aAAA,CAACxI,KAAK;IAACoK,IAAI,EAAC,OAAO;IAAA1B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjBtJ,KAAA,CAAA+I,aAAA,CAACrI,SAAS;IAAAuI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRtJ,KAAA,CAAA+I,aAAA,CAACpI,QAAQ;IAAAsI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACPtJ,KAAA,CAAA+I,aAAA,CAACtI,SAAS;IAACmK,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE3H,IAAI,CAAC2G,CAAC,CAAC,kBAAkB,CAAa,CAAC,eAClEtI,KAAA,CAAA+I,aAAA,CAACtI,SAAS;IAACmK,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtB3H,IAAI,CAAC2G,CAAC,CAAC,mBAAmB,CAClB,CAAC,eACZtI,KAAA,CAAA+I,aAAA,CAACtI,SAAS;IAACmK,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtB3H,IAAI,CAAC2G,CAAC,CAAC,qBAAqB,CACpB,CAAC,eACZtI,KAAA,CAAA+I,aAAA,CAACtI,SAAS;IAACmK,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtB3H,IAAI,CAAC2G,CAAC,CAAC,qBAAqB,CACpB,CACH,CACD,CAAC,eACZtI,KAAA,CAAA+I,aAAA,CAACvI,SAAS;IAAAyI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRtJ,KAAA,CAAA+I,aAAA,CAAA/I,KAAA,CAAA6K,QAAA,QACGxI,KAAK,CAACyI,GAAG,CAAErI,IAAI,iBACdzC,KAAA,CAAA+I,aAAA,CAACpI,QAAQ;IAACoK,GAAG,EAAEtI,IAAI,CAACI,EAAG;IAAAoG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrBtJ,KAAA,CAAA+I,aAAA,CAACtI,SAAS;IAACmK,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE7G,IAAI,CAAC+G,IAAgB,CAAC,eACjDxJ,KAAA,CAAA+I,aAAA,CAACtI,SAAS;IAACmK,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE7G,IAAI,CAACuI,KAAiB,CAAC,eAClDhL,KAAA,CAAA+I,aAAA,CAACtI,SAAS;IAACmK,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE7G,IAAI,CAACwI,OAAmB,CAAC,eACpDjL,KAAA,CAAA+I,aAAA,CAACtI,SAAS;IAACmK,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBtJ,KAAA,CAAA+I,aAAA,CAACnI,UAAU;IACT+J,IAAI,EAAC,OAAO;IACZJ,OAAO,EAAEA,CAAA,KAAMrC,cAAc,CAACzF,IAAI,CAAE;IAAAwG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpCtJ,KAAA,CAAA+I,aAAA,CAAC1H,QAAQ;IAAA4H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACD,CAAC,eAEbtJ,KAAA,CAAA+I,aAAA,CAACnI,UAAU;IACT+J,IAAI,EAAC,OAAO;IACZJ,OAAO,EAAG7B,CAAC,IAAK;MACdjC,mBAAmB,CAAC,IAAI,CAAC;MACzBJ,eAAe,CAAC5D,IAAI,CAAC;IACvB,CAAE;IAAAwG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFtJ,KAAA,CAAA+I,aAAA,CAAC3H,iBAAiB;IAAA6H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACV,CACH,CACH,CACX,CAAC,EACD1D,OAAO,iBAAI5F,KAAA,CAAA+I,aAAA,CAACnH,gBAAgB;IAACsJ,OAAO,EAAE,CAAE;IAAAjC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAC3C,CACO,CACN,CACF,CACM,CAAC;AAEpB,CAAC;AAED,eAAe5D,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}