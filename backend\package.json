{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "start": "nodemon dist/server.js", "dev:server": "ts-node-dev --respawn --transpile-only --ignore node_modules src/server.ts", "db:migrate": "npx sequelize db:migrate", "db:seed": "sequelize db:seed:all", "pretest": "NODE_ENV=test sequelize db:migrate && NODE_ENV=test sequelize db:seed:all", "test": "NODE_ENV=test jest", "posttest": "NODE_ENV=test sequelize db:migrate:undo:all", "lint": "eslint src/**/*.ts"}, "author": "", "license": "MIT", "dependencies": {"@adiwajshing/keyed-db": "^0.2.4", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@hapi/boom": "^9.1.4", "@sentry/node": "^6.18.1", "@whiskeysockets/baileys": "^6.3.0", "bcryptjs": "^2.4.3", "bull": "^4.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cron": "^2.1.0", "date-fns": "^2.28.0", "dotenv": "^16.0.0", "express": "^4.17.3", "express-async-errors": "^3.1.1", "file-type": "^17.1.1", "glob": "^11.0.3", "gn-api-sdk-typescript": "^1.0.7", "http-graceful-shutdown": "^3.1.6", "jsonwebtoken": "^8.5.1", "multer": "^1.4.4", "mustache": "^4.2.0", "mysql2": "^2.3.3", "nodemailer": "^6.8.0", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "pg-types": "^4.0.2", "pino": "^8.8.0", "pino-pretty": "^9.1.1", "puppeteer": "^19.4.0", "qrcode-terminal": "^0.12.0", "reflect-metadata": "^0.1.13", "require-directory": "^2.1.1", "sequelize": "^5.22.3", "sequelize-typescript": "^1.1.0", "socket.io": "^3.0.5", "uuid": "^8.3.2", "xlsx": "^0.18.3", "xtend": "^4.0.2", "yup": "^0.32.11"}, "devDependencies": {"@types/babel__generator": "^7.27.0", "@types/babel__template": "^7.4.4", "@types/bcryptjs": "^2.4.2", "@types/bluebird": "^3.5.36", "@types/chance": "^1.1.3", "@types/cookie-parser": "^1.4.2", "@types/cors": "^2.8.12", "@types/express": "^4.17.13", "@types/factory-girl": "^5.0.8", "@types/istanbul-lib-report": "^3.0.3", "@types/jest": "^27.4.1", "@types/jsonwebtoken": "^8.5.8", "@types/multer": "^1.4.7", "@types/mustache": "^4.1.2", "@types/node": "^17.0.21", "@types/superagent": "^8.1.9", "@types/supertest": "^2.0.11", "@types/uuid": "^8.3.4", "@types/validator": "^13.7.1", "@types/yargs-parser": "^21.0.3", "@types/yup": "^0.29.13", "@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.13.0", "chance": "^1.1.8", "eslint": "^8.10.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^4.0.0", "factory-girl": "^5.0.4", "jest": "^27.5.1", "nodemon": "^2.0.15", "prettier": "^2.5.1", "sequelize-cli": "^6.6.3", "supertest": "^6.2.2", "ts-jest": "^27.1.3", "ts-node-dev": "^2.0.0", "typescript": "^4.2.4"}}