{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\components\\\\ColorPicker\\\\index.js\";\nimport { Dialog } from \"@material-ui/core\";\nimport React, { useState } from \"react\";\nimport { BlockPicker } from \"react-color\";\nconst ColorPicker = ({\n  onChange,\n  currentColor,\n  handleClose,\n  open\n}) => {\n  const [selectedColor, setSelectedColor] = useState(currentColor);\n  const handleChange = color => {\n    setSelectedColor(color.hex);\n    handleClose();\n  };\n  const colors = [\"#B80000\", \"#DB3E00\", \"#FCCB00\", \"#008B02\", \"#006B76\", \"#1273DE\", \"#004DCF\", \"#5300EB\", \"#EB9694\", \"#FAD0C3\", \"#FEF3BD\", \"#C1E1C5\", \"#BEDADC\", \"#C4DEF6\", \"#BED3F3\", \"#D4C4FB\", \"#4D4D4D\", \"#999999\", \"#F44E3B\", \"#FE9200\", \"#FCDC00\", \"#DBDF00\", \"#A4DD00\", \"#68CCCA\", \"#73D8FF\", \"#AEA1FF\", \"#FDA1FF\", \"#333333\", \"#808080\", \"#cccccc\", \"#D33115\", \"#E27300\", \"#FCC400\", \"#B0BC00\", \"#68BC00\", \"#16A5A5\", \"#009CE0\", \"#7B64FF\", \"#FA28FF\", \"#666666\", \"#B3B3B3\", \"#9F0500\", \"#C45100\", \"#FB9E00\", \"#808900\", \"#194D33\", \"#0C797D\", \"#0062B1\", \"#653294\", \"#AB149E\"];\n  return /*#__PURE__*/React.createElement(Dialog, {\n    onClose: handleClose,\n    \"aria-labelledby\": \"simple-dialog-title\",\n    open: open,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 3\n    }\n  }, /*#__PURE__*/React.createElement(BlockPicker, {\n    width: \"100%\",\n    triangle: \"hide\",\n    color: selectedColor,\n    colors: colors,\n    onChange: handleChange,\n    onChangeComplete: color => onChange(color.hex),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 4\n    }\n  }));\n};\nexport default ColorPicker;", "map": {"version": 3, "names": ["Dialog", "React", "useState", "BlockPicker", "ColorPicker", "onChange", "currentColor", "handleClose", "open", "selectedColor", "setSelectedColor", "handleChange", "color", "hex", "colors", "createElement", "onClose", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "triangle", "onChangeComplete"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/components/ColorPicker/index.js"], "sourcesContent": ["import { Dialog } from \"@material-ui/core\";\r\nimport React, { useState } from \"react\";\r\n\r\nimport { BlockPicker } from \"react-color\";\r\n\r\nconst ColorPicker = ({ onChange, currentColor, handleClose, open }) => {\r\n\tconst [selectedColor, setSelectedColor] = useState(currentColor);\r\n\r\n\tconst handleChange = color => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\thandleClose();\r\n\t};\r\n\r\n\tconst colors = [\r\n\t\t\"#B80000\",\r\n\t\t\"#DB3E00\",\r\n\t\t\"#FCCB00\",\r\n\t\t\"#008B02\",\r\n\t\t\"#006B76\",\r\n\t\t\"#1273DE\",\r\n\t\t\"#004DCF\",\r\n\t\t\"#5300EB\",\r\n\t\t\"#EB9694\",\r\n\t\t\"#FAD0C3\",\r\n\t\t\"#FEF3BD\",\r\n\t\t\"#C1E1C5\",\r\n\t\t\"#BEDADC\",\r\n\t\t\"#C4DEF6\",\r\n\t\t\"#BED3F3\",\r\n\t\t\"#D4C4FB\",\r\n\t\t\"#4D4D4D\",\r\n\t\t\"#999999\",\r\n\t\t\"#F44E3B\",\r\n\t\t\"#FE9200\",\r\n\t\t\"#FCDC00\",\r\n\t\t\"#DBDF00\",\r\n\t\t\"#A4DD00\",\r\n\t\t\"#68CCCA\",\r\n\t\t\"#73D8FF\",\r\n\t\t\"#AEA1FF\",\r\n\t\t\"#FDA1FF\",\r\n\t\t\"#333333\",\r\n\t\t\"#808080\",\r\n\t\t\"#cccccc\",\r\n\t\t\"#D33115\",\r\n\t\t\"#E27300\",\r\n\t\t\"#FCC400\",\r\n\t\t\"#B0BC00\",\r\n\t\t\"#68BC00\",\r\n\t\t\"#16A5A5\",\r\n\t\t\"#009CE0\",\r\n\t\t\"#7B64FF\",\r\n\t\t\"#FA28FF\",\r\n\t\t\"#666666\",\r\n\t\t\"#B3B3B3\",\r\n\t\t\"#9F0500\",\r\n\t\t\"#C45100\",\r\n\t\t\"#FB9E00\",\r\n\t\t\"#808900\",\r\n\t\t\"#194D33\",\r\n\t\t\"#0C797D\",\r\n\t\t\"#0062B1\",\r\n\t\t\"#653294\",\r\n\t\t\"#AB149E\",\r\n\t];\r\n\r\n\treturn (\r\n\t\t<Dialog\r\n\t\t\tonClose={handleClose}\r\n\t\t\taria-labelledby=\"simple-dialog-title\"\r\n\t\t\topen={open}\r\n\t\t>\r\n\t\t\t<BlockPicker\r\n\t\t\t\twidth={\"100%\"}\r\n\t\t\t\ttriangle=\"hide\"\r\n\t\t\t\tcolor={selectedColor}\r\n\t\t\t\tcolors={colors}\r\n\t\t\t\tonChange={handleChange}\r\n\t\t\t\tonChangeComplete={color => onChange(color.hex)}\r\n\t\t\t/>\r\n\t\t</Dialog>\r\n\t);\r\n};\r\n\r\nexport default ColorPicker;\r\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAC1C,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAEvC,SAASC,WAAW,QAAQ,aAAa;AAEzC,MAAMC,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY;EAAEC,WAAW;EAAEC;AAAK,CAAC,KAAK;EACtE,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGR,QAAQ,CAACI,YAAY,CAAC;EAEhE,MAAMK,YAAY,GAAGC,KAAK,IAAI;IAC7BF,gBAAgB,CAACE,KAAK,CAACC,GAAG,CAAC;IAC3BN,WAAW,CAAC,CAAC;EACd,CAAC;EAED,MAAMO,MAAM,GAAG,CACd,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACT;EAED,oBACCb,KAAA,CAAAc,aAAA,CAACf,MAAM;IACNgB,OAAO,EAAET,WAAY;IACrB,mBAAgB,qBAAqB;IACrCC,IAAI,EAAEA,IAAK;IAAAS,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEXrB,KAAA,CAAAc,aAAA,CAACZ,WAAW;IACXoB,KAAK,EAAE,MAAO;IACdC,QAAQ,EAAC,MAAM;IACfZ,KAAK,EAAEH,aAAc;IACrBK,MAAM,EAAEA,MAAO;IACfT,QAAQ,EAAEM,YAAa;IACvBc,gBAAgB,EAAEb,KAAK,IAAIP,QAAQ,CAACO,KAAK,CAACC,GAAG,CAAE;IAAAI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC/C,CACM,CAAC;AAEX,CAAC;AAED,eAAelB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}