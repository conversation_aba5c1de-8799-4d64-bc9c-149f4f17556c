version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: whaticket-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: whaticket_db
      POSTGRES_USER: whaticket
      POSTGRES_PASSWORD: whaticket123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # Redis Cache/Queue
  redis:
    image: redis:7-alpine
    container_name: whaticket-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
