{"ast": null, "code": "import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\nimport { ColorWrap, Saturation, Hue, Alpha, Checkboard } from '../common';\nimport ChromeFields from './ChromeFields';\nimport ChromePointer from './ChromePointer';\nimport ChromePointerCircle from './ChromePointerCircle';\nexport var Chrome = function Chrome(_ref) {\n  var width = _ref.width,\n    onChange = _ref.onChange,\n    disableAlpha = _ref.disableAlpha,\n    rgb = _ref.rgb,\n    hsl = _ref.hsl,\n    hsv = _ref.hsv,\n    hex = _ref.hex,\n    renderers = _ref.renderers,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className,\n    defaultView = _ref.defaultView;\n  var styles = reactCSS(merge({\n    'default': {\n      picker: {\n        width: width,\n        background: '#fff',\n        borderRadius: '2px',\n        boxShadow: '0 0 2px rgba(0,0,0,.3), 0 4px 8px rgba(0,0,0,.3)',\n        boxSizing: 'initial',\n        fontFamily: 'Menlo'\n      },\n      saturation: {\n        width: '100%',\n        paddingBottom: '55%',\n        position: 'relative',\n        borderRadius: '2px 2px 0 0',\n        overflow: 'hidden'\n      },\n      Saturation: {\n        radius: '2px 2px 0 0'\n      },\n      body: {\n        padding: '16px 16px 12px'\n      },\n      controls: {\n        display: 'flex'\n      },\n      color: {\n        width: '32px'\n      },\n      swatch: {\n        marginTop: '6px',\n        width: '16px',\n        height: '16px',\n        borderRadius: '8px',\n        position: 'relative',\n        overflow: 'hidden'\n      },\n      active: {\n        absolute: '0px 0px 0px 0px',\n        borderRadius: '8px',\n        boxShadow: 'inset 0 0 0 1px rgba(0,0,0,.1)',\n        background: 'rgba(' + rgb.r + ', ' + rgb.g + ', ' + rgb.b + ', ' + rgb.a + ')',\n        zIndex: '2'\n      },\n      toggles: {\n        flex: '1'\n      },\n      hue: {\n        height: '10px',\n        position: 'relative',\n        marginBottom: '8px'\n      },\n      Hue: {\n        radius: '2px'\n      },\n      alpha: {\n        height: '10px',\n        position: 'relative'\n      },\n      Alpha: {\n        radius: '2px'\n      }\n    },\n    'disableAlpha': {\n      color: {\n        width: '22px'\n      },\n      alpha: {\n        display: 'none'\n      },\n      hue: {\n        marginBottom: '0px'\n      },\n      swatch: {\n        width: '10px',\n        height: '10px',\n        marginTop: '0px'\n      }\n    }\n  }, passedStyles), {\n    disableAlpha: disableAlpha\n  });\n  return React.createElement('div', {\n    style: styles.picker,\n    className: 'chrome-picker ' + className\n  }, React.createElement('div', {\n    style: styles.saturation\n  }, React.createElement(Saturation, {\n    style: styles.Saturation,\n    hsl: hsl,\n    hsv: hsv,\n    pointer: ChromePointerCircle,\n    onChange: onChange\n  })), React.createElement('div', {\n    style: styles.body\n  }, React.createElement('div', {\n    style: styles.controls,\n    className: 'flexbox-fix'\n  }, React.createElement('div', {\n    style: styles.color\n  }, React.createElement('div', {\n    style: styles.swatch\n  }, React.createElement('div', {\n    style: styles.active\n  }), React.createElement(Checkboard, {\n    renderers: renderers\n  }))), React.createElement('div', {\n    style: styles.toggles\n  }, React.createElement('div', {\n    style: styles.hue\n  }, React.createElement(Hue, {\n    style: styles.Hue,\n    hsl: hsl,\n    pointer: ChromePointer,\n    onChange: onChange\n  })), React.createElement('div', {\n    style: styles.alpha\n  }, React.createElement(Alpha, {\n    style: styles.Alpha,\n    rgb: rgb,\n    hsl: hsl,\n    pointer: ChromePointer,\n    renderers: renderers,\n    onChange: onChange\n  })))), React.createElement(ChromeFields, {\n    rgb: rgb,\n    hsl: hsl,\n    hex: hex,\n    view: defaultView,\n    onChange: onChange,\n    disableAlpha: disableAlpha\n  })));\n};\nChrome.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  disableAlpha: PropTypes.bool,\n  styles: PropTypes.object,\n  defaultView: PropTypes.oneOf([\"hex\", \"rgb\", \"hsl\"])\n};\nChrome.defaultProps = {\n  width: 225,\n  disableAlpha: false,\n  styles: {}\n};\nexport default ColorWrap(Chrome);", "map": {"version": 3, "names": ["React", "PropTypes", "reactCSS", "merge", "ColorWrap", "Saturation", "<PERSON><PERSON>", "Alpha", "Checkboard", "ChromeFields", "ChromePointer", "ChromePointerCircle", "Chrome", "_ref", "width", "onChange", "disable<PERSON><PERSON>pha", "rgb", "hsl", "hsv", "hex", "renderers", "_ref$styles", "styles", "passedStyles", "undefined", "_ref$className", "className", "defaultView", "picker", "background", "borderRadius", "boxShadow", "boxSizing", "fontFamily", "saturation", "paddingBottom", "position", "overflow", "radius", "body", "padding", "controls", "display", "color", "swatch", "marginTop", "height", "active", "absolute", "r", "g", "b", "a", "zIndex", "toggles", "flex", "hue", "marginBottom", "alpha", "createElement", "style", "pointer", "view", "propTypes", "oneOfType", "string", "number", "bool", "object", "oneOf", "defaultProps"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/chrome/Chrome.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\n\nimport { ColorWrap, Saturation, Hue, Alpha, Checkboard } from '../common';\nimport ChromeFields from './ChromeFields';\nimport ChromePointer from './ChromePointer';\nimport ChromePointerCircle from './ChromePointerCircle';\n\nexport var Chrome = function Chrome(_ref) {\n  var width = _ref.width,\n      onChange = _ref.onChange,\n      disableAlpha = _ref.disableAlpha,\n      rgb = _ref.rgb,\n      hsl = _ref.hsl,\n      hsv = _ref.hsv,\n      hex = _ref.hex,\n      renderers = _ref.renderers,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className,\n      defaultView = _ref.defaultView;\n\n  var styles = reactCSS(merge({\n    'default': {\n      picker: {\n        width: width,\n        background: '#fff',\n        borderRadius: '2px',\n        boxShadow: '0 0 2px rgba(0,0,0,.3), 0 4px 8px rgba(0,0,0,.3)',\n        boxSizing: 'initial',\n        fontFamily: 'Menlo'\n      },\n      saturation: {\n        width: '100%',\n        paddingBottom: '55%',\n        position: 'relative',\n        borderRadius: '2px 2px 0 0',\n        overflow: 'hidden'\n      },\n      Saturation: {\n        radius: '2px 2px 0 0'\n      },\n      body: {\n        padding: '16px 16px 12px'\n      },\n      controls: {\n        display: 'flex'\n      },\n      color: {\n        width: '32px'\n      },\n      swatch: {\n        marginTop: '6px',\n        width: '16px',\n        height: '16px',\n        borderRadius: '8px',\n        position: 'relative',\n        overflow: 'hidden'\n      },\n      active: {\n        absolute: '0px 0px 0px 0px',\n        borderRadius: '8px',\n        boxShadow: 'inset 0 0 0 1px rgba(0,0,0,.1)',\n        background: 'rgba(' + rgb.r + ', ' + rgb.g + ', ' + rgb.b + ', ' + rgb.a + ')',\n        zIndex: '2'\n      },\n      toggles: {\n        flex: '1'\n      },\n      hue: {\n        height: '10px',\n        position: 'relative',\n        marginBottom: '8px'\n      },\n      Hue: {\n        radius: '2px'\n      },\n      alpha: {\n        height: '10px',\n        position: 'relative'\n      },\n      Alpha: {\n        radius: '2px'\n      }\n    },\n    'disableAlpha': {\n      color: {\n        width: '22px'\n      },\n      alpha: {\n        display: 'none'\n      },\n      hue: {\n        marginBottom: '0px'\n      },\n      swatch: {\n        width: '10px',\n        height: '10px',\n        marginTop: '0px'\n      }\n    }\n  }, passedStyles), { disableAlpha: disableAlpha });\n\n  return React.createElement(\n    'div',\n    { style: styles.picker, className: 'chrome-picker ' + className },\n    React.createElement(\n      'div',\n      { style: styles.saturation },\n      React.createElement(Saturation, {\n        style: styles.Saturation,\n        hsl: hsl,\n        hsv: hsv,\n        pointer: ChromePointerCircle,\n        onChange: onChange\n      })\n    ),\n    React.createElement(\n      'div',\n      { style: styles.body },\n      React.createElement(\n        'div',\n        { style: styles.controls, className: 'flexbox-fix' },\n        React.createElement(\n          'div',\n          { style: styles.color },\n          React.createElement(\n            'div',\n            { style: styles.swatch },\n            React.createElement('div', { style: styles.active }),\n            React.createElement(Checkboard, { renderers: renderers })\n          )\n        ),\n        React.createElement(\n          'div',\n          { style: styles.toggles },\n          React.createElement(\n            'div',\n            { style: styles.hue },\n            React.createElement(Hue, {\n              style: styles.Hue,\n              hsl: hsl,\n              pointer: ChromePointer,\n              onChange: onChange\n            })\n          ),\n          React.createElement(\n            'div',\n            { style: styles.alpha },\n            React.createElement(Alpha, {\n              style: styles.Alpha,\n              rgb: rgb,\n              hsl: hsl,\n              pointer: ChromePointer,\n              renderers: renderers,\n              onChange: onChange\n            })\n          )\n        )\n      ),\n      React.createElement(ChromeFields, {\n        rgb: rgb,\n        hsl: hsl,\n        hex: hex,\n        view: defaultView,\n        onChange: onChange,\n        disableAlpha: disableAlpha\n      })\n    )\n  );\n};\n\nChrome.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  disableAlpha: PropTypes.bool,\n  styles: PropTypes.object,\n  defaultView: PropTypes.oneOf([\"hex\", \"rgb\", \"hsl\"])\n};\n\nChrome.defaultProps = {\n  width: 225,\n  disableAlpha: false,\n  styles: {}\n};\n\nexport default ColorWrap(Chrome);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,SAAS,EAAEC,UAAU,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,QAAQ,WAAW;AACzE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,mBAAmB,MAAM,uBAAuB;AAEvD,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACxC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,YAAY,GAAGH,IAAI,CAACG,YAAY;IAChCC,GAAG,GAAGJ,IAAI,CAACI,GAAG;IACdC,GAAG,GAAGL,IAAI,CAACK,GAAG;IACdC,GAAG,GAAGN,IAAI,CAACM,GAAG;IACdC,GAAG,GAAGP,IAAI,CAACO,GAAG;IACdC,SAAS,GAAGR,IAAI,CAACQ,SAAS;IAC1BC,WAAW,GAAGT,IAAI,CAACU,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,cAAc,GAAGb,IAAI,CAACc,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKD,SAAS,GAAG,EAAE,GAAGC,cAAc;IAC9DE,WAAW,GAAGf,IAAI,CAACe,WAAW;EAElC,IAAIL,MAAM,GAAGrB,QAAQ,CAACC,KAAK,CAAC;IAC1B,SAAS,EAAE;MACT0B,MAAM,EAAE;QACNf,KAAK,EAAEA,KAAK;QACZgB,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,kDAAkD;QAC7DC,SAAS,EAAE,SAAS;QACpBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVrB,KAAK,EAAE,MAAM;QACbsB,aAAa,EAAE,KAAK;QACpBC,QAAQ,EAAE,UAAU;QACpBN,YAAY,EAAE,aAAa;QAC3BO,QAAQ,EAAE;MACZ,CAAC;MACDjC,UAAU,EAAE;QACVkC,MAAM,EAAE;MACV,CAAC;MACDC,IAAI,EAAE;QACJC,OAAO,EAAE;MACX,CAAC;MACDC,QAAQ,EAAE;QACRC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACL9B,KAAK,EAAE;MACT,CAAC;MACD+B,MAAM,EAAE;QACNC,SAAS,EAAE,KAAK;QAChBhC,KAAK,EAAE,MAAM;QACbiC,MAAM,EAAE,MAAM;QACdhB,YAAY,EAAE,KAAK;QACnBM,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDU,MAAM,EAAE;QACNC,QAAQ,EAAE,iBAAiB;QAC3BlB,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,gCAAgC;QAC3CF,UAAU,EAAE,OAAO,GAAGb,GAAG,CAACiC,CAAC,GAAG,IAAI,GAAGjC,GAAG,CAACkC,CAAC,GAAG,IAAI,GAAGlC,GAAG,CAACmC,CAAC,GAAG,IAAI,GAAGnC,GAAG,CAACoC,CAAC,GAAG,GAAG;QAC9EC,MAAM,EAAE;MACV,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE;MACR,CAAC;MACDC,GAAG,EAAE;QACHV,MAAM,EAAE,MAAM;QACdV,QAAQ,EAAE,UAAU;QACpBqB,YAAY,EAAE;MAChB,CAAC;MACDpD,GAAG,EAAE;QACHiC,MAAM,EAAE;MACV,CAAC;MACDoB,KAAK,EAAE;QACLZ,MAAM,EAAE,MAAM;QACdV,QAAQ,EAAE;MACZ,CAAC;MACD9B,KAAK,EAAE;QACLgC,MAAM,EAAE;MACV;IACF,CAAC;IACD,cAAc,EAAE;MACdK,KAAK,EAAE;QACL9B,KAAK,EAAE;MACT,CAAC;MACD6C,KAAK,EAAE;QACLhB,OAAO,EAAE;MACX,CAAC;MACDc,GAAG,EAAE;QACHC,YAAY,EAAE;MAChB,CAAC;MACDb,MAAM,EAAE;QACN/B,KAAK,EAAE,MAAM;QACbiC,MAAM,EAAE,MAAM;QACdD,SAAS,EAAE;MACb;IACF;EACF,CAAC,EAAEtB,YAAY,CAAC,EAAE;IAAER,YAAY,EAAEA;EAAa,CAAC,CAAC;EAEjD,OAAOhB,KAAK,CAAC4D,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAEtC,MAAM,CAACM,MAAM;IAAEF,SAAS,EAAE,gBAAgB,GAAGA;EAAU,CAAC,EACjE3B,KAAK,CAAC4D,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEtC,MAAM,CAACY;EAAW,CAAC,EAC5BnC,KAAK,CAAC4D,aAAa,CAACvD,UAAU,EAAE;IAC9BwD,KAAK,EAAEtC,MAAM,CAAClB,UAAU;IACxBa,GAAG,EAAEA,GAAG;IACRC,GAAG,EAAEA,GAAG;IACR2C,OAAO,EAAEnD,mBAAmB;IAC5BI,QAAQ,EAAEA;EACZ,CAAC,CACH,CAAC,EACDf,KAAK,CAAC4D,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEtC,MAAM,CAACiB;EAAK,CAAC,EACtBxC,KAAK,CAAC4D,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEtC,MAAM,CAACmB,QAAQ;IAAEf,SAAS,EAAE;EAAc,CAAC,EACpD3B,KAAK,CAAC4D,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEtC,MAAM,CAACqB;EAAM,CAAC,EACvB5C,KAAK,CAAC4D,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEtC,MAAM,CAACsB;EAAO,CAAC,EACxB7C,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEtC,MAAM,CAACyB;EAAO,CAAC,CAAC,EACpDhD,KAAK,CAAC4D,aAAa,CAACpD,UAAU,EAAE;IAAEa,SAAS,EAAEA;EAAU,CAAC,CAC1D,CACF,CAAC,EACDrB,KAAK,CAAC4D,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEtC,MAAM,CAACgC;EAAQ,CAAC,EACzBvD,KAAK,CAAC4D,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEtC,MAAM,CAACkC;EAAI,CAAC,EACrBzD,KAAK,CAAC4D,aAAa,CAACtD,GAAG,EAAE;IACvBuD,KAAK,EAAEtC,MAAM,CAACjB,GAAG;IACjBY,GAAG,EAAEA,GAAG;IACR4C,OAAO,EAAEpD,aAAa;IACtBK,QAAQ,EAAEA;EACZ,CAAC,CACH,CAAC,EACDf,KAAK,CAAC4D,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEtC,MAAM,CAACoC;EAAM,CAAC,EACvB3D,KAAK,CAAC4D,aAAa,CAACrD,KAAK,EAAE;IACzBsD,KAAK,EAAEtC,MAAM,CAAChB,KAAK;IACnBU,GAAG,EAAEA,GAAG;IACRC,GAAG,EAAEA,GAAG;IACR4C,OAAO,EAAEpD,aAAa;IACtBW,SAAS,EAAEA,SAAS;IACpBN,QAAQ,EAAEA;EACZ,CAAC,CACH,CACF,CACF,CAAC,EACDf,KAAK,CAAC4D,aAAa,CAACnD,YAAY,EAAE;IAChCQ,GAAG,EAAEA,GAAG;IACRC,GAAG,EAAEA,GAAG;IACRE,GAAG,EAAEA,GAAG;IACR2C,IAAI,EAAEnC,WAAW;IACjBb,QAAQ,EAAEA,QAAQ;IAClBC,YAAY,EAAEA;EAChB,CAAC,CACH,CACF,CAAC;AACH,CAAC;AAEDJ,MAAM,CAACoD,SAAS,GAAG;EACjBlD,KAAK,EAAEb,SAAS,CAACgE,SAAS,CAAC,CAAChE,SAAS,CAACiE,MAAM,EAAEjE,SAAS,CAACkE,MAAM,CAAC,CAAC;EAChEnD,YAAY,EAAEf,SAAS,CAACmE,IAAI;EAC5B7C,MAAM,EAAEtB,SAAS,CAACoE,MAAM;EACxBzC,WAAW,EAAE3B,SAAS,CAACqE,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACpD,CAAC;AAED1D,MAAM,CAAC2D,YAAY,GAAG;EACpBzD,KAAK,EAAE,GAAG;EACVE,YAAY,EAAE,KAAK;EACnBO,MAAM,EAAE,CAAC;AACX,CAAC;AAED,eAAenB,SAAS,CAACQ,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}