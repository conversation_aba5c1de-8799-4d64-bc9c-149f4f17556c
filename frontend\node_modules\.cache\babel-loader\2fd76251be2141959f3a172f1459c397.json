{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\components\\\\TicketMessagesDialog\\\\index.js\";\nimport React, { useContext, useEffect, useState } from \"react\";\nimport { toast } from \"react-toastify\";\nimport api from \"../../services/api\";\nimport toastError from \"../../errors/toastError\";\nimport { Box, Button, Dialog, DialogActions, makeStyles } from \"@material-ui/core\";\nimport { useHistory } from \"react-router-dom\";\nimport { AuthContext } from \"../../context/Auth/AuthContext\";\nimport MessagesList from \"../MessagesList\";\nimport { ReplyMessageProvider } from \"../../context/ReplyingMessage/ReplyingMessageContext\";\nimport TicketHeader from \"../TicketHeader\";\nimport TicketInfo from \"../TicketInfo\";\nimport { socketConnection } from \"../../services/socket\";\nconst drawerWidth = 320;\nconst useStyles = makeStyles(theme => ({\n  root: {\n    display: \"flex\",\n    height: \"100%\",\n    position: \"relative\",\n    overflow: \"hidden\"\n  },\n  mainWrapper: {\n    flex: 1,\n    height: \"100%\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    overflow: \"hidden\",\n    borderTopLeftRadius: 0,\n    borderBottomLeftRadius: 0,\n    borderLeft: \"0\",\n    marginRight: -drawerWidth,\n    transition: theme.transitions.create(\"margin\", {\n      easing: theme.transitions.easing.sharp,\n      duration: theme.transitions.duration.leavingScreen\n    })\n  },\n  mainWrapperShift: {\n    borderTopRightRadius: 0,\n    borderBottomRightRadius: 0,\n    transition: theme.transitions.create(\"margin\", {\n      easing: theme.transitions.easing.easeOut,\n      duration: theme.transitions.duration.enteringScreen\n    }),\n    marginRight: 0\n  }\n}));\nexport default function TicketMessagesDialog({\n  open,\n  handleClose,\n  ticketId\n}) {\n  const history = useHistory();\n  const classes = useStyles();\n  const {\n    user\n  } = useContext(AuthContext);\n  const [, setDrawerOpen] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [contact, setContact] = useState({});\n  const [ticket, setTicket] = useState({});\n  useEffect(() => {\n    let delayDebounceFn = null;\n    if (open) {\n      setLoading(true);\n      delayDebounceFn = setTimeout(() => {\n        const fetchTicket = async () => {\n          try {\n            const {\n              data\n            } = await api.get(\"/tickets/\" + ticketId);\n            const {\n              queueId\n            } = data;\n            const {\n              queues,\n              profile\n            } = user;\n            const queueAllowed = queues.find(q => q.id === queueId);\n            if (queueAllowed === undefined && profile !== \"admin\") {\n              toast.error(\"Acesso não permitido\");\n              history.push(\"/tickets\");\n              return;\n            }\n            setContact(data.contact);\n            setTicket(data);\n            setLoading(false);\n          } catch (err) {\n            setLoading(false);\n            toastError(err);\n          }\n        };\n        fetchTicket();\n      }, 500);\n    }\n    return () => {\n      if (delayDebounceFn !== null) {\n        clearTimeout(delayDebounceFn);\n      }\n    };\n  }, [ticketId, user, history, open]);\n  useEffect(() => {\n    const companyId = localStorage.getItem(\"companyId\");\n    let socket = null;\n    if (open) {\n      socket = socketConnection({\n        companyId\n      });\n      socket.on(\"connect\", () => socket.emit(\"joinChatBox\", `${ticket.id}`));\n      socket.on(`company-${companyId}-ticket`, data => {\n        if (data.action === \"update\") {\n          setTicket(data.ticket);\n        }\n        if (data.action === \"delete\") {\n          toast.success(\"Ticket deleted sucessfully.\");\n          history.push(\"/tickets\");\n        }\n      });\n      socket.on(`company-${companyId}-contact`, data => {\n        if (data.action === \"update\") {\n          setContact(prevState => {\n            var _data$contact;\n            if (prevState.id === ((_data$contact = data.contact) === null || _data$contact === void 0 ? void 0 : _data$contact.id)) {\n              return {\n                ...prevState,\n                ...data.contact\n              };\n            }\n            return prevState;\n          });\n        }\n      });\n    }\n    return () => {\n      if (socket !== null) {\n        socket.disconnect();\n      }\n    };\n  }, [ticketId, ticket, history, open]);\n  const handleDrawerOpen = () => {\n    setDrawerOpen(true);\n  };\n  const renderTicketInfo = () => {\n    if (ticket.user !== undefined) {\n      return /*#__PURE__*/React.createElement(TicketInfo, {\n        contact: contact,\n        ticket: ticket,\n        onClick: handleDrawerOpen,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 9\n        }\n      });\n    }\n  };\n  const renderMessagesList = () => {\n    return /*#__PURE__*/React.createElement(Box, {\n      className: classes.root,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(MessagesList, {\n      ticket: ticket,\n      ticketId: ticket.id,\n      isGroup: ticket.isGroup,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }\n    }));\n  };\n  return /*#__PURE__*/React.createElement(Dialog, {\n    maxWidth: \"md\",\n    onClose: handleClose,\n    open: open,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(TicketHeader, {\n    loading: loading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }\n  }, renderTicketInfo()), /*#__PURE__*/React.createElement(ReplyMessageProvider, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }\n  }, renderMessagesList()), /*#__PURE__*/React.createElement(DialogActions, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    onClick: handleClose,\n    color: \"primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 9\n    }\n  }, \"Fechar\")));\n}", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "toast", "api", "toastError", "Box", "<PERSON><PERSON>", "Dialog", "DialogActions", "makeStyles", "useHistory", "AuthContext", "MessagesList", "ReplyMessageProvider", "Tick<PERSON><PERSON><PERSON><PERSON>", "TicketInfo", "socketConnection", "drawerWidth", "useStyles", "theme", "root", "display", "height", "position", "overflow", "mainWrapper", "flex", "flexDirection", "borderTopLeftRadius", "borderBottomLeftRadius", "borderLeft", "marginRight", "transition", "transitions", "create", "easing", "sharp", "duration", "leavingScreen", "mainWrapperShift", "borderTopRightRadius", "borderBottomRightRadius", "easeOut", "enteringScreen", "TicketMessagesDialog", "open", "handleClose", "ticketId", "history", "classes", "user", "setDrawerOpen", "loading", "setLoading", "contact", "setContact", "ticket", "setTicket", "delayDebounceFn", "setTimeout", "fetchTicket", "data", "get", "queueId", "queues", "profile", "queueAllowed", "find", "q", "id", "undefined", "error", "push", "err", "clearTimeout", "companyId", "localStorage", "getItem", "socket", "on", "emit", "action", "success", "prevState", "_data$contact", "disconnect", "handleDrawerOpen", "renderTicketInfo", "createElement", "onClick", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderMessagesList", "className", "isGroup", "max<PERSON><PERSON><PERSON>", "onClose", "color"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/components/TicketMessagesDialog/index.js"], "sourcesContent": ["import React, { useContext, useEffect, useState } from \"react\";\r\n\r\nimport { toast } from \"react-toastify\";\r\nimport api from \"../../services/api\";\r\nimport toastError from \"../../errors/toastError\";\r\n\r\nimport {\r\n  Box,\r\n  Button,\r\n  Dialog,\r\n  DialogActions,\r\n  makeStyles,\r\n} from \"@material-ui/core\";\r\nimport { useHistory } from \"react-router-dom\";\r\nimport { AuthContext } from \"../../context/Auth/AuthContext\";\r\nimport MessagesList from \"../MessagesList\";\r\nimport { ReplyMessageProvider } from \"../../context/ReplyingMessage/ReplyingMessageContext\";\r\nimport TicketHeader from \"../TicketHeader\";\r\nimport TicketInfo from \"../TicketInfo\";\r\nimport { socketConnection } from \"../../services/socket\";\r\n\r\nconst drawerWidth = 320;\r\n\r\nconst useStyles = makeStyles((theme) => ({\r\n  root: {\r\n    display: \"flex\",\r\n    height: \"100%\",\r\n    position: \"relative\",\r\n    overflow: \"hidden\",\r\n  },\r\n\r\n  mainWrapper: {\r\n    flex: 1,\r\n    height: \"100%\",\r\n    display: \"flex\",\r\n    flexDirection: \"column\",\r\n    overflow: \"hidden\",\r\n    borderTopLeftRadius: 0,\r\n    borderBottomLeftRadius: 0,\r\n    borderLeft: \"0\",\r\n    marginRight: -drawerWidth,\r\n    transition: theme.transitions.create(\"margin\", {\r\n      easing: theme.transitions.easing.sharp,\r\n      duration: theme.transitions.duration.leavingScreen,\r\n    }),\r\n  },\r\n\r\n  mainWrapperShift: {\r\n    borderTopRightRadius: 0,\r\n    borderBottomRightRadius: 0,\r\n    transition: theme.transitions.create(\"margin\", {\r\n      easing: theme.transitions.easing.easeOut,\r\n      duration: theme.transitions.duration.enteringScreen,\r\n    }),\r\n    marginRight: 0,\r\n  },\r\n}));\r\n\r\nexport default function TicketMessagesDialog({ open, handleClose, ticketId }) {\r\n  const history = useHistory();\r\n  const classes = useStyles();\r\n\r\n  const { user } = useContext(AuthContext);\r\n\r\n  const [, setDrawerOpen] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n  const [contact, setContact] = useState({});\r\n  const [ticket, setTicket] = useState({});\r\n\r\n  useEffect(() => {\r\n    let delayDebounceFn = null;\r\n    if (open) {\r\n      setLoading(true);\r\n      delayDebounceFn = setTimeout(() => {\r\n        const fetchTicket = async () => {\r\n          try {\r\n            const { data } = await api.get(\"/tickets/\" + ticketId);\r\n            const { queueId } = data;\r\n            const { queues, profile } = user;\r\n\r\n            const queueAllowed = queues.find((q) => q.id === queueId);\r\n            if (queueAllowed === undefined && profile !== \"admin\") {\r\n              toast.error(\"Acesso não permitido\");\r\n              history.push(\"/tickets\");\r\n              return;\r\n            }\r\n\r\n            setContact(data.contact);\r\n            setTicket(data);\r\n            setLoading(false);\r\n          } catch (err) {\r\n            setLoading(false);\r\n            toastError(err);\r\n          }\r\n        };\r\n        fetchTicket();\r\n      }, 500);\r\n    }\r\n    return () => {\r\n      if (delayDebounceFn !== null) {\r\n        clearTimeout(delayDebounceFn);\r\n      }\r\n    };\r\n  }, [ticketId, user, history, open]);\r\n\r\n  useEffect(() => {\r\n    const companyId = localStorage.getItem(\"companyId\");\r\n    let socket = null;\r\n\r\n    if (open) {\r\n      socket = socketConnection({ companyId });\r\n      socket.on(\"connect\", () => socket.emit(\"joinChatBox\", `${ticket.id}`));\r\n\r\n      socket.on(`company-${companyId}-ticket`, (data) => {\r\n        if (data.action === \"update\") {\r\n          setTicket(data.ticket);\r\n        }\r\n\r\n        if (data.action === \"delete\") {\r\n          toast.success(\"Ticket deleted sucessfully.\");\r\n          history.push(\"/tickets\");\r\n        }\r\n      });\r\n\r\n      socket.on(`company-${companyId}-contact`, (data) => {\r\n        if (data.action === \"update\") {\r\n          setContact((prevState) => {\r\n            if (prevState.id === data.contact?.id) {\r\n              return { ...prevState, ...data.contact };\r\n            }\r\n            return prevState;\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    return () => {\r\n      if (socket !== null) {\r\n        socket.disconnect();\r\n      }\r\n    };\r\n  }, [ticketId, ticket, history, open]);\r\n\r\n  const handleDrawerOpen = () => {\r\n    setDrawerOpen(true);\r\n  };\r\n\r\n  const renderTicketInfo = () => {\r\n    if (ticket.user !== undefined) {\r\n      return (\r\n        <TicketInfo\r\n          contact={contact}\r\n          ticket={ticket}\r\n          onClick={handleDrawerOpen}\r\n        />\r\n      );\r\n    }\r\n  };\r\n\r\n  const renderMessagesList = () => {\r\n    return (\r\n      <Box className={classes.root}>\r\n        <MessagesList\r\n          ticket={ticket}\r\n          ticketId={ticket.id}\r\n          isGroup={ticket.isGroup}\r\n        ></MessagesList>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Dialog maxWidth=\"md\" onClose={handleClose} open={open}>\r\n      <TicketHeader loading={loading}>{renderTicketInfo()}</TicketHeader>\r\n      <ReplyMessageProvider>{renderMessagesList()}</ReplyMessageProvider>\r\n      <DialogActions>\r\n        <Button onClick={handleClose} color=\"primary\">\r\n          Fechar\r\n        </Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE9D,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAOC,UAAU,MAAM,yBAAyB;AAEhD,SACEC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,aAAa,EACbC,UAAU,QACL,mBAAmB;AAC1B,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,SAASC,oBAAoB,QAAQ,sDAAsD;AAC3F,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,gBAAgB,QAAQ,uBAAuB;AAExD,MAAMC,WAAW,GAAG,GAAG;AAEvB,MAAMC,SAAS,GAAGT,UAAU,CAAEU,KAAK,KAAM;EACvCC,IAAI,EAAE;IACJC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE;EACZ,CAAC;EAEDC,WAAW,EAAE;IACXC,IAAI,EAAE,CAAC;IACPJ,MAAM,EAAE,MAAM;IACdD,OAAO,EAAE,MAAM;IACfM,aAAa,EAAE,QAAQ;IACvBH,QAAQ,EAAE,QAAQ;IAClBI,mBAAmB,EAAE,CAAC;IACtBC,sBAAsB,EAAE,CAAC;IACzBC,UAAU,EAAE,GAAG;IACfC,WAAW,EAAE,CAACd,WAAW;IACzBe,UAAU,EAAEb,KAAK,CAACc,WAAW,CAACC,MAAM,CAAC,QAAQ,EAAE;MAC7CC,MAAM,EAAEhB,KAAK,CAACc,WAAW,CAACE,MAAM,CAACC,KAAK;MACtCC,QAAQ,EAAElB,KAAK,CAACc,WAAW,CAACI,QAAQ,CAACC;IACvC,CAAC;EACH,CAAC;EAEDC,gBAAgB,EAAE;IAChBC,oBAAoB,EAAE,CAAC;IACvBC,uBAAuB,EAAE,CAAC;IAC1BT,UAAU,EAAEb,KAAK,CAACc,WAAW,CAACC,MAAM,CAAC,QAAQ,EAAE;MAC7CC,MAAM,EAAEhB,KAAK,CAACc,WAAW,CAACE,MAAM,CAACO,OAAO;MACxCL,QAAQ,EAAElB,KAAK,CAACc,WAAW,CAACI,QAAQ,CAACM;IACvC,CAAC,CAAC;IACFZ,WAAW,EAAE;EACf;AACF,CAAC,CAAC,CAAC;AAEH,eAAe,SAASa,oBAAoBA,CAAC;EAAEC,IAAI;EAAEC,WAAW;EAAEC;AAAS,CAAC,EAAE;EAC5E,MAAMC,OAAO,GAAGtC,UAAU,CAAC,CAAC;EAC5B,MAAMuC,OAAO,GAAG/B,SAAS,CAAC,CAAC;EAE3B,MAAM;IAAEgC;EAAK,CAAC,GAAGnD,UAAU,CAACY,WAAW,CAAC;EAExC,MAAM,GAAGwC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACuD,MAAM,EAAEC,SAAS,CAAC,GAAGxD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExCD,SAAS,CAAC,MAAM;IACd,IAAI0D,eAAe,GAAG,IAAI;IAC1B,IAAIb,IAAI,EAAE;MACRQ,UAAU,CAAC,IAAI,CAAC;MAChBK,eAAe,GAAGC,UAAU,CAAC,MAAM;QACjC,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;UAC9B,IAAI;YACF,MAAM;cAAEC;YAAK,CAAC,GAAG,MAAM1D,GAAG,CAAC2D,GAAG,CAAC,WAAW,GAAGf,QAAQ,CAAC;YACtD,MAAM;cAAEgB;YAAQ,CAAC,GAAGF,IAAI;YACxB,MAAM;cAAEG,MAAM;cAAEC;YAAQ,CAAC,GAAGf,IAAI;YAEhC,MAAMgB,YAAY,GAAGF,MAAM,CAACG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAKN,OAAO,CAAC;YACzD,IAAIG,YAAY,KAAKI,SAAS,IAAIL,OAAO,KAAK,OAAO,EAAE;cACrD/D,KAAK,CAACqE,KAAK,CAAC,sBAAsB,CAAC;cACnCvB,OAAO,CAACwB,IAAI,CAAC,UAAU,CAAC;cACxB;YACF;YAEAjB,UAAU,CAACM,IAAI,CAACP,OAAO,CAAC;YACxBG,SAAS,CAACI,IAAI,CAAC;YACfR,UAAU,CAAC,KAAK,CAAC;UACnB,CAAC,CAAC,OAAOoB,GAAG,EAAE;YACZpB,UAAU,CAAC,KAAK,CAAC;YACjBjD,UAAU,CAACqE,GAAG,CAAC;UACjB;QACF,CAAC;QACDb,WAAW,CAAC,CAAC;MACf,CAAC,EAAE,GAAG,CAAC;IACT;IACA,OAAO,MAAM;MACX,IAAIF,eAAe,KAAK,IAAI,EAAE;QAC5BgB,YAAY,CAAChB,eAAe,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACX,QAAQ,EAAEG,IAAI,EAAEF,OAAO,EAAEH,IAAI,CAAC,CAAC;EAEnC7C,SAAS,CAAC,MAAM;IACd,MAAM2E,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAIjC,IAAI,EAAE;MACRiC,MAAM,GAAG9D,gBAAgB,CAAC;QAAE2D;MAAU,CAAC,CAAC;MACxCG,MAAM,CAACC,EAAE,CAAC,SAAS,EAAE,MAAMD,MAAM,CAACE,IAAI,CAAC,aAAa,EAAE,GAAGxB,MAAM,CAACa,EAAE,EAAE,CAAC,CAAC;MAEtES,MAAM,CAACC,EAAE,CAAC,WAAWJ,SAAS,SAAS,EAAGd,IAAI,IAAK;QACjD,IAAIA,IAAI,CAACoB,MAAM,KAAK,QAAQ,EAAE;UAC5BxB,SAAS,CAACI,IAAI,CAACL,MAAM,CAAC;QACxB;QAEA,IAAIK,IAAI,CAACoB,MAAM,KAAK,QAAQ,EAAE;UAC5B/E,KAAK,CAACgF,OAAO,CAAC,6BAA6B,CAAC;UAC5ClC,OAAO,CAACwB,IAAI,CAAC,UAAU,CAAC;QAC1B;MACF,CAAC,CAAC;MAEFM,MAAM,CAACC,EAAE,CAAC,WAAWJ,SAAS,UAAU,EAAGd,IAAI,IAAK;QAClD,IAAIA,IAAI,CAACoB,MAAM,KAAK,QAAQ,EAAE;UAC5B1B,UAAU,CAAE4B,SAAS,IAAK;YAAA,IAAAC,aAAA;YACxB,IAAID,SAAS,CAACd,EAAE,OAAAe,aAAA,GAAKvB,IAAI,CAACP,OAAO,cAAA8B,aAAA,uBAAZA,aAAA,CAAcf,EAAE,GAAE;cACrC,OAAO;gBAAE,GAAGc,SAAS;gBAAE,GAAGtB,IAAI,CAACP;cAAQ,CAAC;YAC1C;YACA,OAAO6B,SAAS;UAClB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACX,IAAIL,MAAM,KAAK,IAAI,EAAE;QACnBA,MAAM,CAACO,UAAU,CAAC,CAAC;MACrB;IACF,CAAC;EACH,CAAC,EAAE,CAACtC,QAAQ,EAAES,MAAM,EAAER,OAAO,EAAEH,IAAI,CAAC,CAAC;EAErC,MAAMyC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BnC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI/B,MAAM,CAACN,IAAI,KAAKoB,SAAS,EAAE;MAC7B,oBACExE,KAAA,CAAA0F,aAAA,CAACzE,UAAU;QACTuC,OAAO,EAAEA,OAAQ;QACjBE,MAAM,EAAEA,MAAO;QACfiC,OAAO,EAAEH,gBAAiB;QAAAI,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAC3B,CAAC;IAEN;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,oBACElG,KAAA,CAAA0F,aAAA,CAACnF,GAAG;MAAC4F,SAAS,EAAEhD,OAAO,CAAC7B,IAAK;MAAAsE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC3BjG,KAAA,CAAA0F,aAAA,CAAC5E,YAAY;MACX4C,MAAM,EAAEA,MAAO;MACfT,QAAQ,EAAES,MAAM,CAACa,EAAG;MACpB6B,OAAO,EAAE1C,MAAM,CAAC0C,OAAQ;MAAAR,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACX,CACZ,CAAC;EAEV,CAAC;EAED,oBACEjG,KAAA,CAAA0F,aAAA,CAACjF,MAAM;IAAC4F,QAAQ,EAAC,IAAI;IAACC,OAAO,EAAEtD,WAAY;IAACD,IAAI,EAAEA,IAAK;IAAA6C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrDjG,KAAA,CAAA0F,aAAA,CAAC1E,YAAY;IAACsC,OAAO,EAAEA,OAAQ;IAAAsC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAER,gBAAgB,CAAC,CAAgB,CAAC,eACnEzF,KAAA,CAAA0F,aAAA,CAAC3E,oBAAoB;IAAA6E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEC,kBAAkB,CAAC,CAAwB,CAAC,eACnElG,KAAA,CAAA0F,aAAA,CAAChF,aAAa;IAAAkF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACZjG,KAAA,CAAA0F,aAAA,CAAClF,MAAM;IAACmF,OAAO,EAAE3C,WAAY;IAACuD,KAAK,EAAC,SAAS;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAEtC,CACK,CACT,CAAC;AAEb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}