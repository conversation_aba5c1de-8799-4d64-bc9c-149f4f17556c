import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { Button, CircularProgress } from '@material-ui/core';

const useStyles = makeStyles((theme) => ({
  primaryButton: {
    borderRadius: 8,
    textTransform: 'none',
    fontWeight: 600,
    padding: '10px 24px',
    fontSize: '0.875rem',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    border: 'none',
    boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 16px rgba(102, 126, 234, 0.4)',
    },
    '&:active': {
      transform: 'translateY(0)',
    },
    '&:disabled': {
      background: theme.palette.action.disabled,
      color: theme.palette.action.disabled,
      transform: 'none',
      boxShadow: 'none',
    }
  },
  secondaryButton: {
    borderRadius: 8,
    textTransform: 'none',
    fontWeight: 600,
    padding: '10px 24px',
    fontSize: '0.875rem',
    backgroundColor: 'white',
    color: theme.palette.primary.main,
    border: `1px solid ${theme.palette.primary.main}`,
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      backgroundColor: 'rgba(102, 126, 234, 0.08)',
      borderColor: theme.palette.primary.dark,
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.2)',
    },
    '&:active': {
      transform: 'translateY(0)',
    }
  },
  outlinedButton: {
    borderRadius: 8,
    textTransform: 'none',
    fontWeight: 600,
    padding: '10px 24px',
    fontSize: '0.875rem',
    backgroundColor: 'transparent',
    color: theme.palette.text.primary,
    border: '1px solid rgba(0,0,0,0.12)',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      backgroundColor: 'rgba(0,0,0,0.04)',
      borderColor: 'rgba(0,0,0,0.24)',
      transform: 'translateY(-1px)',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    },
    '&:active': {
      transform: 'translateY(0)',
    }
  },
  textButton: {
    borderRadius: 8,
    textTransform: 'none',
    fontWeight: 600,
    padding: '8px 16px',
    fontSize: '0.875rem',
    color: theme.palette.primary.main,
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      backgroundColor: 'rgba(102, 126, 234, 0.08)',
      transform: 'translateY(-1px)',
    },
    '&:active': {
      transform: 'translateY(0)',
    }
  },
  dangerButton: {
    borderRadius: 8,
    textTransform: 'none',
    fontWeight: 600,
    padding: '10px 24px',
    fontSize: '0.875rem',
    background: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',
    color: 'white',
    border: 'none',
    boxShadow: '0 2px 8px rgba(245, 101, 101, 0.3)',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      background: 'linear-gradient(135deg, #e53e3e 0%, #c53030 100%)',
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 16px rgba(245, 101, 101, 0.4)',
    },
    '&:active': {
      transform: 'translateY(0)',
    }
  },
  successButton: {
    borderRadius: 8,
    textTransform: 'none',
    fontWeight: 600,
    padding: '10px 24px',
    fontSize: '0.875rem',
    background: 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)',
    color: 'white',
    border: 'none',
    boxShadow: '0 2px 8px rgba(72, 187, 120, 0.3)',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      background: 'linear-gradient(135deg, #38a169 0%, #2f855a 100%)',
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 16px rgba(72, 187, 120, 0.4)',
    },
    '&:active': {
      transform: 'translateY(0)',
    }
  },
  smallButton: {
    padding: '6px 16px',
    fontSize: '0.75rem',
    minHeight: 32,
  },
  largeButton: {
    padding: '12px 32px',
    fontSize: '1rem',
    minHeight: 48,
  },
  loadingSpinner: {
    marginRight: theme.spacing(1),
    color: 'inherit',
  }
}));

const ModernButton = ({
  variant = 'primary',
  size = 'medium',
  loading = false,
  children,
  startIcon,
  endIcon,
  className,
  ...props
}) => {
  const classes = useStyles();

  const getButtonClass = () => {
    let baseClass = '';
    
    switch (variant) {
      case 'primary':
        baseClass = classes.primaryButton;
        break;
      case 'secondary':
        baseClass = classes.secondaryButton;
        break;
      case 'outlined':
        baseClass = classes.outlinedButton;
        break;
      case 'text':
        baseClass = classes.textButton;
        break;
      case 'danger':
        baseClass = classes.dangerButton;
        break;
      case 'success':
        baseClass = classes.successButton;
        break;
      default:
        baseClass = classes.primaryButton;
    }

    if (size === 'small') {
      baseClass += ` ${classes.smallButton}`;
    } else if (size === 'large') {
      baseClass += ` ${classes.largeButton}`;
    }

    return baseClass;
  };

  return (
    <Button
      className={`${getButtonClass()} ${className || ''}`}
      disabled={loading || props.disabled}
      startIcon={loading ? <CircularProgress size={16} className={classes.loadingSpinner} /> : startIcon}
      endIcon={!loading ? endIcon : null}
      {...props}
    >
      {children}
    </Button>
  );
};

export default ModernButton;
