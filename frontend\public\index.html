<!DOCTYPE html>
<html lang="en">
	<head>
		<title>Sua empresa</title>
		<link
			rel="stylesheet"
			href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
		/>
		<link rel="apple-touch-icon" href="%PUBLIC_URL%/apple-touch-icon.png" />
		<link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
		<link rel=”shortcut icon” href=”%PUBLIC_URL%/favicon.ico”>
		<link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
		<meta name="theme-color" content="#000000" />
		<meta
			name="viewport"
			content="minimum-scale=1, initial-scale=1, width=device-width"
		/>
		<!-- Issue workaround for React v16. -->
		<script>
			// See https://github.com/facebook/react/issues/20829#issuecomment-802088260
			if (!crossOriginIsolated) SharedArrayBuffer = ArrayBuffer;
		</script>
	</head>
	<body>
		<noscript>You need to enable JavaScript to run this app.</noscript>
		<div id="root"></div>
	</body>
</html>
