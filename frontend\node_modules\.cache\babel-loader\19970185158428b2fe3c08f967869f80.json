{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\components\\\\ModernSidebar\\\\index.js\";\nimport React from 'react';\nimport { makeStyles } from '@material-ui/core/styles';\nimport { List, ListItem, ListItemIcon, ListItemText, Divider, Typography, Box, Chip } from '@material-ui/core';\nconst useStyles = makeStyles(theme => ({\n  sidebarContainer: {\n    height: '100%',\n    background: 'linear-gradient(180deg, #ffffff 0%, #f8fafc 100%)',\n    borderRight: 'none',\n    boxShadow: '4px 0 20px rgba(0,0,0,0.08)'\n  },\n  logoSection: {\n    padding: '24px 20px',\n    borderBottom: '1px solid rgba(0,0,0,0.06)',\n    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)'\n  },\n  logoText: {\n    fontSize: '1.5rem',\n    fontWeight: 700,\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n    textAlign: 'center'\n  },\n  menuSection: {\n    padding: '16px 0'\n  },\n  sectionTitle: {\n    fontSize: '0.75rem',\n    fontWeight: 600,\n    color: theme.palette.text.secondary,\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px',\n    padding: '8px 20px',\n    marginBottom: '8px'\n  },\n  menuItem: {\n    margin: '4px 12px',\n    borderRadius: 12,\n    padding: '12px 16px',\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      backgroundColor: 'rgba(102, 126, 234, 0.08)',\n      transform: 'translateX(4px)'\n    },\n    '&.active': {\n      backgroundColor: 'rgba(102, 126, 234, 0.12)',\n      borderLeft: `3px solid ${theme.palette.primary.main}`,\n      '& .MuiListItemIcon-root': {\n        color: theme.palette.primary.main\n      },\n      '& .MuiListItemText-primary': {\n        color: theme.palette.primary.main,\n        fontWeight: 600\n      }\n    }\n  },\n  menuIcon: {\n    minWidth: 40,\n    color: theme.palette.text.secondary,\n    '& .MuiSvgIcon-root': {\n      fontSize: '1.25rem'\n    }\n  },\n  menuText: {\n    '& .MuiListItemText-primary': {\n      fontSize: '0.875rem',\n      fontWeight: 500,\n      color: theme.palette.text.primary\n    }\n  },\n  badge: {\n    backgroundColor: theme.palette.error.main,\n    color: 'white',\n    fontSize: '0.75rem',\n    height: 20,\n    minWidth: 20,\n    borderRadius: 10\n  },\n  divider: {\n    margin: '16px 20px',\n    backgroundColor: 'rgba(0,0,0,0.06)'\n  }\n}));\nconst ModernSidebar = ({\n  logo,\n  menuSections = [],\n  activeItem,\n  onItemClick\n}) => {\n  const classes = useStyles();\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.sidebarContainer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.logoSection,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }\n  }, logo ? /*#__PURE__*/React.createElement(Box, {\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 11\n    }\n  }, logo) : /*#__PURE__*/React.createElement(Typography, {\n    className: classes.logoText,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 11\n    }\n  }, \"WhatTicket\")), menuSections.map((section, sectionIndex) => /*#__PURE__*/React.createElement(\"div\", {\n    key: sectionIndex,\n    className: classes.menuSection,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 9\n    }\n  }, section.title && /*#__PURE__*/React.createElement(Typography, {\n    className: classes.sectionTitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 13\n    }\n  }, section.title), /*#__PURE__*/React.createElement(List, {\n    component: \"nav\",\n    disablePadding: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 11\n    }\n  }, section.items.map((item, itemIndex) => /*#__PURE__*/React.createElement(ListItem, {\n    key: itemIndex,\n    button: true,\n    className: `${classes.menuItem} ${activeItem === item.id ? 'active' : ''}`,\n    onClick: () => onItemClick && onItemClick(item),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(ListItemIcon, {\n    className: classes.menuIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 17\n    }\n  }, item.icon), /*#__PURE__*/React.createElement(ListItemText, {\n    className: classes.menuText,\n    primary: item.label,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 17\n    }\n  }), item.badge && /*#__PURE__*/React.createElement(Chip, {\n    size: \"small\",\n    label: item.badge,\n    className: classes.badge,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 19\n    }\n  })))), sectionIndex < menuSections.length - 1 && /*#__PURE__*/React.createElement(Divider, {\n    className: classes.divider,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 13\n    }\n  }))));\n};\nexport default ModernSidebar;", "map": {"version": 3, "names": ["React", "makeStyles", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Typography", "Box", "Chip", "useStyles", "theme", "sidebarContainer", "height", "background", "borderRight", "boxShadow", "logoSection", "padding", "borderBottom", "logoText", "fontSize", "fontWeight", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "textAlign", "menuSection", "sectionTitle", "color", "palette", "text", "secondary", "textTransform", "letterSpacing", "marginBottom", "menuItem", "margin", "borderRadius", "transition", "backgroundColor", "transform", "borderLeft", "primary", "main", "menuIcon", "min<PERSON><PERSON><PERSON>", "menuText", "badge", "error", "divider", "ModernSidebar", "logo", "menuSections", "activeItem", "onItemClick", "classes", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "alignItems", "map", "section", "sectionIndex", "key", "title", "component", "disablePadding", "items", "item", "itemIndex", "button", "id", "onClick", "icon", "label", "size", "length"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/components/ModernSidebar/index.js"], "sourcesContent": ["import React from 'react';\nimport { makeStyles } from '@material-ui/core/styles';\nimport {\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Typography,\n  Box,\n  Chip\n} from '@material-ui/core';\n\nconst useStyles = makeStyles((theme) => ({\n  sidebarContainer: {\n    height: '100%',\n    background: 'linear-gradient(180deg, #ffffff 0%, #f8fafc 100%)',\n    borderRight: 'none',\n    boxShadow: '4px 0 20px rgba(0,0,0,0.08)',\n  },\n  logoSection: {\n    padding: '24px 20px',\n    borderBottom: '1px solid rgba(0,0,0,0.06)',\n    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n  },\n  logoText: {\n    fontSize: '1.5rem',\n    fontWeight: 700,\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n    textAlign: 'center',\n  },\n  menuSection: {\n    padding: '16px 0',\n  },\n  sectionTitle: {\n    fontSize: '0.75rem',\n    fontWeight: 600,\n    color: theme.palette.text.secondary,\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px',\n    padding: '8px 20px',\n    marginBottom: '8px',\n  },\n  menuItem: {\n    margin: '4px 12px',\n    borderRadius: 12,\n    padding: '12px 16px',\n    transition: 'all 0.2s ease-in-out',\n    '&:hover': {\n      backgroundColor: 'rgba(102, 126, 234, 0.08)',\n      transform: 'translateX(4px)',\n    },\n    '&.active': {\n      backgroundColor: 'rgba(102, 126, 234, 0.12)',\n      borderLeft: `3px solid ${theme.palette.primary.main}`,\n      '& .MuiListItemIcon-root': {\n        color: theme.palette.primary.main,\n      },\n      '& .MuiListItemText-primary': {\n        color: theme.palette.primary.main,\n        fontWeight: 600,\n      }\n    }\n  },\n  menuIcon: {\n    minWidth: 40,\n    color: theme.palette.text.secondary,\n    '& .MuiSvgIcon-root': {\n      fontSize: '1.25rem',\n    }\n  },\n  menuText: {\n    '& .MuiListItemText-primary': {\n      fontSize: '0.875rem',\n      fontWeight: 500,\n      color: theme.palette.text.primary,\n    }\n  },\n  badge: {\n    backgroundColor: theme.palette.error.main,\n    color: 'white',\n    fontSize: '0.75rem',\n    height: 20,\n    minWidth: 20,\n    borderRadius: 10,\n  },\n  divider: {\n    margin: '16px 20px',\n    backgroundColor: 'rgba(0,0,0,0.06)',\n  }\n}));\n\nconst ModernSidebar = ({ \n  logo, \n  menuSections = [], \n  activeItem,\n  onItemClick \n}) => {\n  const classes = useStyles();\n\n  return (\n    <div className={classes.sidebarContainer}>\n      {/* Logo Section */}\n      <div className={classes.logoSection}>\n        {logo ? (\n          <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\">\n            {logo}\n          </Box>\n        ) : (\n          <Typography className={classes.logoText}>\n            WhatTicket\n          </Typography>\n        )}\n      </div>\n\n      {/* Menu Sections */}\n      {menuSections.map((section, sectionIndex) => (\n        <div key={sectionIndex} className={classes.menuSection}>\n          {section.title && (\n            <Typography className={classes.sectionTitle}>\n              {section.title}\n            </Typography>\n          )}\n          \n          <List component=\"nav\" disablePadding>\n            {section.items.map((item, itemIndex) => (\n              <ListItem\n                key={itemIndex}\n                button\n                className={`${classes.menuItem} ${activeItem === item.id ? 'active' : ''}`}\n                onClick={() => onItemClick && onItemClick(item)}\n              >\n                <ListItemIcon className={classes.menuIcon}>\n                  {item.icon}\n                </ListItemIcon>\n                <ListItemText \n                  className={classes.menuText}\n                  primary={item.label} \n                />\n                {item.badge && (\n                  <Chip \n                    size=\"small\" \n                    label={item.badge} \n                    className={classes.badge}\n                  />\n                )}\n              </ListItem>\n            ))}\n          </List>\n          \n          {sectionIndex < menuSections.length - 1 && (\n            <Divider className={classes.divider} />\n          )}\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default ModernSidebar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SACEC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,IAAI,QACC,mBAAmB;AAE1B,MAAMC,SAAS,GAAGT,UAAU,CAAEU,KAAK,KAAM;EACvCC,gBAAgB,EAAE;IAChBC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,mDAAmD;IAC/DC,WAAW,EAAE,MAAM;IACnBC,SAAS,EAAE;EACb,CAAC;EACDC,WAAW,EAAE;IACXC,OAAO,EAAE,WAAW;IACpBC,YAAY,EAAE,4BAA4B;IAC1CL,UAAU,EAAE;EACd,CAAC;EACDM,QAAQ,EAAE;IACRC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,GAAG;IACfR,UAAU,EAAE,mDAAmD;IAC/DS,cAAc,EAAE,MAAM;IACtBC,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE,aAAa;IAClCC,SAAS,EAAE;EACb,CAAC;EACDC,WAAW,EAAE;IACXT,OAAO,EAAE;EACX,CAAC;EACDU,YAAY,EAAE;IACZP,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfO,KAAK,EAAElB,KAAK,CAACmB,OAAO,CAACC,IAAI,CAACC,SAAS;IACnCC,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,OAAO;IACtBhB,OAAO,EAAE,UAAU;IACnBiB,YAAY,EAAE;EAChB,CAAC;EACDC,QAAQ,EAAE;IACRC,MAAM,EAAE,UAAU;IAClBC,YAAY,EAAE,EAAE;IAChBpB,OAAO,EAAE,WAAW;IACpBqB,UAAU,EAAE,sBAAsB;IAClC,SAAS,EAAE;MACTC,eAAe,EAAE,2BAA2B;MAC5CC,SAAS,EAAE;IACb,CAAC;IACD,UAAU,EAAE;MACVD,eAAe,EAAE,2BAA2B;MAC5CE,UAAU,EAAE,aAAa/B,KAAK,CAACmB,OAAO,CAACa,OAAO,CAACC,IAAI,EAAE;MACrD,yBAAyB,EAAE;QACzBf,KAAK,EAAElB,KAAK,CAACmB,OAAO,CAACa,OAAO,CAACC;MAC/B,CAAC;MACD,4BAA4B,EAAE;QAC5Bf,KAAK,EAAElB,KAAK,CAACmB,OAAO,CAACa,OAAO,CAACC,IAAI;QACjCtB,UAAU,EAAE;MACd;IACF;EACF,CAAC;EACDuB,QAAQ,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZjB,KAAK,EAAElB,KAAK,CAACmB,OAAO,CAACC,IAAI,CAACC,SAAS;IACnC,oBAAoB,EAAE;MACpBX,QAAQ,EAAE;IACZ;EACF,CAAC;EACD0B,QAAQ,EAAE;IACR,4BAA4B,EAAE;MAC5B1B,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE,GAAG;MACfO,KAAK,EAAElB,KAAK,CAACmB,OAAO,CAACC,IAAI,CAACY;IAC5B;EACF,CAAC;EACDK,KAAK,EAAE;IACLR,eAAe,EAAE7B,KAAK,CAACmB,OAAO,CAACmB,KAAK,CAACL,IAAI;IACzCf,KAAK,EAAE,OAAO;IACdR,QAAQ,EAAE,SAAS;IACnBR,MAAM,EAAE,EAAE;IACViC,QAAQ,EAAE,EAAE;IACZR,YAAY,EAAE;EAChB,CAAC;EACDY,OAAO,EAAE;IACPb,MAAM,EAAE,WAAW;IACnBG,eAAe,EAAE;EACnB;AACF,CAAC,CAAC,CAAC;AAEH,MAAMW,aAAa,GAAGA,CAAC;EACrBC,IAAI;EACJC,YAAY,GAAG,EAAE;EACjBC,UAAU;EACVC;AACF,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAG9C,SAAS,CAAC,CAAC;EAE3B,oBACEV,KAAA,CAAAyD,aAAA;IAAKC,SAAS,EAAEF,OAAO,CAAC5C,gBAAiB;IAAA+C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvChE,KAAA,CAAAyD,aAAA;IAAKC,SAAS,EAAEF,OAAO,CAACvC,WAAY;IAAA0C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjCZ,IAAI,gBACHpD,KAAA,CAAAyD,aAAA,CAACjD,GAAG;IAACyD,OAAO,EAAC,MAAM;IAACC,cAAc,EAAC,QAAQ;IAACC,UAAU,EAAC,QAAQ;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5DZ,IACE,CAAC,gBAENpD,KAAA,CAAAyD,aAAA,CAAClD,UAAU;IAACmD,SAAS,EAAEF,OAAO,CAACpC,QAAS;IAAAuC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,YAE7B,CAEX,CAAC,EAGLX,YAAY,CAACe,GAAG,CAAC,CAACC,OAAO,EAAEC,YAAY,kBACtCtE,KAAA,CAAAyD,aAAA;IAAKc,GAAG,EAAED,YAAa;IAACZ,SAAS,EAAEF,OAAO,CAAC7B,WAAY;IAAAgC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpDK,OAAO,CAACG,KAAK,iBACZxE,KAAA,CAAAyD,aAAA,CAAClD,UAAU;IAACmD,SAAS,EAAEF,OAAO,CAAC5B,YAAa;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzCK,OAAO,CAACG,KACC,CACb,eAEDxE,KAAA,CAAAyD,aAAA,CAACvD,IAAI;IAACuE,SAAS,EAAC,KAAK;IAACC,cAAc;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjCK,OAAO,CAACM,KAAK,CAACP,GAAG,CAAC,CAACQ,IAAI,EAAEC,SAAS,kBACjC7E,KAAA,CAAAyD,aAAA,CAACtD,QAAQ;IACPoE,GAAG,EAAEM,SAAU;IACfC,MAAM;IACNpB,SAAS,EAAE,GAAGF,OAAO,CAACpB,QAAQ,IAAIkB,UAAU,KAAKsB,IAAI,CAACG,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC3EC,OAAO,EAAEA,CAAA,KAAMzB,WAAW,IAAIA,WAAW,CAACqB,IAAI,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhDhE,KAAA,CAAAyD,aAAA,CAACrD,YAAY;IAACsD,SAAS,EAAEF,OAAO,CAACX,QAAS;IAAAc,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvCY,IAAI,CAACK,IACM,CAAC,eACfjF,KAAA,CAAAyD,aAAA,CAACpD,YAAY;IACXqD,SAAS,EAAEF,OAAO,CAACT,QAAS;IAC5BJ,OAAO,EAAEiC,IAAI,CAACM,KAAM;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACrB,CAAC,EACDY,IAAI,CAAC5B,KAAK,iBACThD,KAAA,CAAAyD,aAAA,CAAChD,IAAI;IACH0E,IAAI,EAAC,OAAO;IACZD,KAAK,EAAEN,IAAI,CAAC5B,KAAM;IAClBU,SAAS,EAAEF,OAAO,CAACR,KAAM;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC1B,CAEK,CACX,CACG,CAAC,EAENM,YAAY,GAAGjB,YAAY,CAAC+B,MAAM,GAAG,CAAC,iBACrCpF,KAAA,CAAAyD,aAAA,CAACnD,OAAO;IAACoD,SAAS,EAAEF,OAAO,CAACN,OAAQ;IAAAS,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAErC,CACN,CACE,CAAC;AAEV,CAAC;AAED,eAAeb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}