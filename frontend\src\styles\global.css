/* Global Styles for Modern WhatTicket */

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  border: 1px solid #f1f1f1;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Modern Card Animations */
.modern-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
}

/* Button Hover Effects */
.MuiButton-root {
  transition: all 0.2s ease-in-out !important;
}

.MuiButton-contained:hover {
  transform: translateY(-1px);
}

/* Input Focus Effects */
.MuiOutlinedInput-root:focus-within {
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* Tab Indicator */
.MuiTabs-indicator {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  height: 3px !important;
  border-radius: 3px 3px 0 0 !important;
}

/* Modern Shadows */
.elevation-1 {
  box-shadow: 0 2px 8px rgba(0,0,0,0.08) !important;
}

.elevation-2 {
  box-shadow: 0 4px 16px rgba(0,0,0,0.12) !important;
}

.elevation-3 {
  box-shadow: 0 8px 24px rgba(0,0,0,0.15) !important;
}

/* Loading Animation */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Modern Badge */
.modern-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  border-radius: 12px !important;
}

/* Status Indicators */
.status-online {
  background-color: #48bb78 !important;
}

.status-away {
  background-color: #ed8936 !important;
}

.status-busy {
  background-color: #f56565 !important;
}

.status-offline {
  background-color: #a0aec0 !important;
}

/* Modern List Items */
.MuiListItem-root {
  border-radius: 8px !important;
  margin: 2px 8px !important;
  transition: all 0.2s ease-in-out !important;
}

.MuiListItem-root:hover {
  background-color: rgba(102, 126, 234, 0.08) !important;
  transform: translateX(4px) !important;
}

.MuiListItem-root.Mui-selected {
  background-color: rgba(102, 126, 234, 0.12) !important;
  border-left: 3px solid #667eea !important;
}

/* Modern Paper */
.MuiPaper-root {
  border-radius: 12px !important;
}

/* Modern Drawer */
.MuiDrawer-paper {
  border-right: none !important;
  box-shadow: 4px 0 20px rgba(0,0,0,0.08) !important;
}

/* Modern AppBar */
.MuiAppBar-root {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
}

/* Modern Chip */
.MuiChip-root {
  border-radius: 8px !important;
  font-weight: 500 !important;
}

/* Modern Avatar */
.MuiAvatar-root {
  transition: all 0.2s ease-in-out !important;
}

.MuiAvatar-root:hover {
  transform: scale(1.1) !important;
}

/* Modern TextField */
.MuiTextField-root .MuiOutlinedInput-root {
  border-radius: 8px !important;
}

.MuiTextField-root .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline {
  border-color: #e2e8f0 !important;
}

.MuiTextField-root .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: #cbd5e0 !important;
}

.MuiTextField-root .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #667eea !important;
  border-width: 2px !important;
}

/* Modern Menu */
.MuiMenu-paper {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12) !important;
  margin-top: 8px !important;
}

/* Modern Dialog */
.MuiDialog-paper {
  border-radius: 16px !important;
  box-shadow: 0 20px 60px rgba(0,0,0,0.2) !important;
}

/* Modern Backdrop */
.MuiBackdrop-root {
  backdrop-filter: blur(4px) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-card {
    margin: 8px !important;
  }
  
  .MuiContainer-root {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles can be added here */
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for accessibility */
.MuiButton-root:focus-visible,
.MuiIconButton-root:focus-visible {
  outline: 2px solid #667eea !important;
  outline-offset: 2px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .MuiButton-contained {
    border: 2px solid currentColor !important;
  }
}
