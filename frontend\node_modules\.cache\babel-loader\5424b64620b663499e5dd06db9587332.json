{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\pages\\\\Users\\\\index.js\";\nimport React, { useState, useEffect, useReducer } from \"react\";\nimport { toast } from \"react-toastify\";\nimport { makeStyles } from \"@material-ui/core/styles\";\nimport Paper from \"@material-ui/core/Paper\";\nimport Table from \"@material-ui/core/Table\";\nimport TableBody from \"@material-ui/core/TableBody\";\nimport TableCell from \"@material-ui/core/TableCell\";\nimport TableHead from \"@material-ui/core/TableHead\";\nimport TableRow from \"@material-ui/core/TableRow\";\nimport IconButton from \"@material-ui/core/IconButton\";\nimport SearchIcon from \"@material-ui/icons/Search\";\nimport TextField from \"@material-ui/core/TextField\";\nimport InputAdornment from \"@material-ui/core/InputAdornment\";\nimport Avatar from \"@material-ui/core/Avatar\";\nimport Chip from \"@material-ui/core/Chip\";\nimport Box from \"@material-ui/core/Box\";\nimport Typography from \"@material-ui/core/Typography\";\nimport DeleteOutlineIcon from \"@material-ui/icons/DeleteOutline\";\nimport EditIcon from \"@material-ui/icons/Edit\";\nimport PersonAddIcon from \"@material-ui/icons/PersonAdd\";\nimport PeopleIcon from \"@material-ui/icons/People\";\nimport ModernPageContainer from \"../../components/ModernPageContainer\";\nimport ModernButton from \"../../components/ModernButton\";\nimport api from \"../../services/api\";\nimport { i18n } from \"../../translate/i18n\";\nimport TableRowSkeleton from \"../../components/TableRowSkeleton\";\nimport UserModal from \"../../components/UserModal\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport toastError from \"../../errors/toastError\";\nimport { socketConnection } from \"../../services/socket\";\nconst reducer = (state, action) => {\n  if (action.type === \"LOAD_USERS\") {\n    const users = action.payload;\n    const newUsers = [];\n    users.forEach(user => {\n      const userIndex = state.findIndex(u => u.id === user.id);\n      if (userIndex !== -1) {\n        state[userIndex] = user;\n      } else {\n        newUsers.push(user);\n      }\n    });\n    return [...state, ...newUsers];\n  }\n  if (action.type === \"UPDATE_USERS\") {\n    const user = action.payload;\n    const userIndex = state.findIndex(u => u.id === user.id);\n    if (userIndex !== -1) {\n      state[userIndex] = user;\n      return [...state];\n    } else {\n      return [user, ...state];\n    }\n  }\n  if (action.type === \"DELETE_USER\") {\n    const userId = action.payload;\n    const userIndex = state.findIndex(u => u.id === userId);\n    if (userIndex !== -1) {\n      state.splice(userIndex, 1);\n    }\n    return [...state];\n  }\n  if (action.type === \"RESET\") {\n    return [];\n  }\n};\nconst useStyles = makeStyles(theme => ({\n  mainPaper: {\n    flex: 1,\n    padding: theme.spacing(1),\n    overflowY: \"scroll\",\n    ...theme.scrollbarStyles\n  }\n}));\nconst Users = () => {\n  const classes = useStyles();\n  const [loading, setLoading] = useState(false);\n  const [pageNumber, setPageNumber] = useState(1);\n  const [hasMore, setHasMore] = useState(false);\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [deletingUser, setDeletingUser] = useState(null);\n  const [userModalOpen, setUserModalOpen] = useState(false);\n  const [confirmModalOpen, setConfirmModalOpen] = useState(false);\n  const [searchParam, setSearchParam] = useState(\"\");\n  const [users, dispatch] = useReducer(reducer, []);\n  useEffect(() => {\n    dispatch({\n      type: \"RESET\"\n    });\n    setPageNumber(1);\n  }, [searchParam]);\n  useEffect(() => {\n    setLoading(true);\n    const delayDebounceFn = setTimeout(() => {\n      const fetchUsers = async () => {\n        try {\n          const {\n            data\n          } = await api.get(\"/users/\", {\n            params: {\n              searchParam,\n              pageNumber\n            }\n          });\n          dispatch({\n            type: \"LOAD_USERS\",\n            payload: data.users\n          });\n          setHasMore(data.hasMore);\n          setLoading(false);\n        } catch (err) {\n          toastError(err);\n        }\n      };\n      fetchUsers();\n    }, 500);\n    return () => clearTimeout(delayDebounceFn);\n  }, [searchParam, pageNumber]);\n  useEffect(() => {\n    const companyId = localStorage.getItem(\"companyId\");\n    const socket = socketConnection({\n      companyId\n    });\n    socket.on(`company-${companyId}-user`, data => {\n      if (data.action === \"update\" || data.action === \"create\") {\n        dispatch({\n          type: \"UPDATE_USERS\",\n          payload: data.user\n        });\n      }\n      if (data.action === \"delete\") {\n        dispatch({\n          type: \"DELETE_USER\",\n          payload: +data.userId\n        });\n      }\n    });\n    return () => {\n      socket.disconnect();\n    };\n  }, []);\n  const handleOpenUserModal = () => {\n    setSelectedUser(null);\n    setUserModalOpen(true);\n  };\n  const handleCloseUserModal = () => {\n    setSelectedUser(null);\n    setUserModalOpen(false);\n  };\n  const handleSearch = event => {\n    setSearchParam(event.target.value.toLowerCase());\n  };\n  const handleEditUser = user => {\n    setSelectedUser(user);\n    setUserModalOpen(true);\n  };\n  const handleDeleteUser = async userId => {\n    try {\n      await api.delete(`/users/${userId}`);\n      toast.success(i18n.t(\"users.toasts.deleted\"));\n    } catch (err) {\n      toastError(err);\n    }\n    setDeletingUser(null);\n    setSearchParam(\"\");\n    setPageNumber(1);\n  };\n  const loadMore = () => {\n    setPageNumber(prevState => prevState + 1);\n  };\n  const handleScroll = e => {\n    if (!hasMore || loading) return;\n    const {\n      scrollTop,\n      scrollHeight,\n      clientHeight\n    } = e.currentTarget;\n    if (scrollHeight - (scrollTop + 100) < clientHeight) {\n      loadMore();\n    }\n  };\n  return /*#__PURE__*/React.createElement(MainContainer, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(ConfirmationModal, {\n    title: deletingUser && `${i18n.t(\"users.confirmationModal.deleteTitle\")} ${deletingUser.name}?`,\n    open: confirmModalOpen,\n    onClose: setConfirmModalOpen,\n    onConfirm: () => handleDeleteUser(deletingUser.id),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }\n  }, i18n.t(\"users.confirmationModal.deleteMessage\")), /*#__PURE__*/React.createElement(UserModal, {\n    open: userModalOpen,\n    onClose: handleCloseUserModal,\n    \"aria-labelledby\": \"form-dialog-title\",\n    userId: selectedUser && selectedUser.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(MainHeader, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Title, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 9\n    }\n  }, i18n.t(\"users.title\")), /*#__PURE__*/React.createElement(MainHeaderButtonsWrapper, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(TextField, {\n    placeholder: i18n.t(\"contacts.searchPlaceholder\"),\n    type: \"search\",\n    value: searchParam,\n    onChange: handleSearch,\n    InputProps: {\n      startAdornment: /*#__PURE__*/React.createElement(InputAdornment, {\n        position: \"start\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 17\n        }\n      }, /*#__PURE__*/React.createElement(SearchIcon, {\n        style: {\n          color: \"gray\"\n        },\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 19\n        }\n      }))\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(Button, {\n    variant: \"contained\",\n    color: \"primary\",\n    onClick: handleOpenUserModal,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 11\n    }\n  }, i18n.t(\"users.buttons.add\")))), /*#__PURE__*/React.createElement(Paper, {\n    className: classes.mainPaper,\n    variant: \"outlined\",\n    onScroll: handleScroll,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Table, {\n    size: \"small\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(TableHead, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(TableRow, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 15\n    }\n  }, i18n.t(\"users.table.name\")), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 15\n    }\n  }, i18n.t(\"users.table.email\")), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 15\n    }\n  }, i18n.t(\"users.table.profile\")), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 15\n    }\n  }, i18n.t(\"users.table.actions\")))), /*#__PURE__*/React.createElement(TableBody, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(React.Fragment, null, users.map(user => /*#__PURE__*/React.createElement(TableRow, {\n    key: user.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 19\n    }\n  }, user.name), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 19\n    }\n  }, user.email), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 19\n    }\n  }, user.profile), /*#__PURE__*/React.createElement(TableCell, {\n    align: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(IconButton, {\n    size: \"small\",\n    onClick: () => handleEditUser(user),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(EditIcon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 23\n    }\n  })), /*#__PURE__*/React.createElement(IconButton, {\n    size: \"small\",\n    onClick: e => {\n      setConfirmModalOpen(true);\n      setDeletingUser(user);\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(DeleteOutlineIcon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 23\n    }\n  }))))), loading && /*#__PURE__*/React.createElement(TableRowSkeleton, {\n    columns: 4,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 27\n    }\n  }))))));\n};\nexport default Users;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useReducer", "toast", "makeStyles", "Paper", "Table", "TableBody", "TableCell", "TableHead", "TableRow", "IconButton", "SearchIcon", "TextField", "InputAdornment", "Avatar", "Chip", "Box", "Typography", "DeleteOutlineIcon", "EditIcon", "PersonAddIcon", "PeopleIcon", "ModernPageContainer", "ModernButton", "api", "i18n", "TableRowSkeleton", "UserModal", "ConfirmationModal", "toastError", "socketConnection", "reducer", "state", "action", "type", "users", "payload", "newUsers", "for<PERSON>ach", "user", "userIndex", "findIndex", "u", "id", "push", "userId", "splice", "useStyles", "theme", "mainPaper", "flex", "padding", "spacing", "overflowY", "scrollbarStyles", "Users", "classes", "loading", "setLoading", "pageNumber", "setPageNumber", "hasMore", "setHasMore", "selected<PERSON>ser", "setSelectedUser", "deletingUser", "setDeletingUser", "userModalOpen", "setUserModalOpen", "confirmModalOpen", "setConfirmModalOpen", "searchParam", "setSearchParam", "dispatch", "delayDebounceFn", "setTimeout", "fetchUsers", "data", "get", "params", "err", "clearTimeout", "companyId", "localStorage", "getItem", "socket", "on", "disconnect", "handleOpenUserModal", "handleCloseUserModal", "handleSearch", "event", "target", "value", "toLowerCase", "handleEditUser", "handleDeleteUser", "delete", "success", "t", "loadMore", "prevState", "handleScroll", "e", "scrollTop", "scrollHeight", "clientHeight", "currentTarget", "createElement", "MainContainer", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "name", "open", "onClose", "onConfirm", "<PERSON><PERSON><PERSON><PERSON>", "Title", "MainHeaderButtonsWrapper", "placeholder", "onChange", "InputProps", "startAdornment", "position", "style", "color", "<PERSON><PERSON>", "variant", "onClick", "className", "onScroll", "size", "align", "Fragment", "map", "key", "email", "profile", "columns"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/pages/Users/<USER>"], "sourcesContent": ["import React, { useState, useEffect, useReducer } from \"react\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nimport { makeStyles } from \"@material-ui/core/styles\";\r\nimport Paper from \"@material-ui/core/Paper\";\r\nimport Table from \"@material-ui/core/Table\";\r\nimport TableBody from \"@material-ui/core/TableBody\";\r\nimport TableCell from \"@material-ui/core/TableCell\";\r\nimport TableHead from \"@material-ui/core/TableHead\";\r\nimport TableRow from \"@material-ui/core/TableRow\";\r\nimport IconButton from \"@material-ui/core/IconButton\";\r\nimport SearchIcon from \"@material-ui/icons/Search\";\r\nimport TextField from \"@material-ui/core/TextField\";\r\nimport InputAdornment from \"@material-ui/core/InputAdornment\";\r\nimport Avatar from \"@material-ui/core/Avatar\";\r\nimport Chip from \"@material-ui/core/Chip\";\r\nimport Box from \"@material-ui/core/Box\";\r\nimport Typography from \"@material-ui/core/Typography\";\r\n\r\nimport DeleteOutlineIcon from \"@material-ui/icons/DeleteOutline\";\r\nimport EditIcon from \"@material-ui/icons/Edit\";\r\nimport PersonAddIcon from \"@material-ui/icons/PersonAdd\";\r\nimport PeopleIcon from \"@material-ui/icons/People\";\r\n\r\nimport ModernPageContainer from \"../../components/ModernPageContainer\";\r\nimport ModernButton from \"../../components/ModernButton\";\r\n\r\nimport api from \"../../services/api\";\r\nimport { i18n } from \"../../translate/i18n\";\r\nimport TableRowSkeleton from \"../../components/TableRowSkeleton\";\r\nimport UserModal from \"../../components/UserModal\";\r\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\r\nimport toastError from \"../../errors/toastError\";\r\nimport { socketConnection } from \"../../services/socket\";\r\n\r\nconst reducer = (state, action) => {\r\n  if (action.type === \"LOAD_USERS\") {\r\n    const users = action.payload;\r\n    const newUsers = [];\r\n\r\n    users.forEach((user) => {\r\n      const userIndex = state.findIndex((u) => u.id === user.id);\r\n      if (userIndex !== -1) {\r\n        state[userIndex] = user;\r\n      } else {\r\n        newUsers.push(user);\r\n      }\r\n    });\r\n\r\n    return [...state, ...newUsers];\r\n  }\r\n\r\n  if (action.type === \"UPDATE_USERS\") {\r\n    const user = action.payload;\r\n    const userIndex = state.findIndex((u) => u.id === user.id);\r\n\r\n    if (userIndex !== -1) {\r\n      state[userIndex] = user;\r\n      return [...state];\r\n    } else {\r\n      return [user, ...state];\r\n    }\r\n  }\r\n\r\n  if (action.type === \"DELETE_USER\") {\r\n    const userId = action.payload;\r\n\r\n    const userIndex = state.findIndex((u) => u.id === userId);\r\n    if (userIndex !== -1) {\r\n      state.splice(userIndex, 1);\r\n    }\r\n    return [...state];\r\n  }\r\n\r\n  if (action.type === \"RESET\") {\r\n    return [];\r\n  }\r\n};\r\n\r\nconst useStyles = makeStyles((theme) => ({\r\n  mainPaper: {\r\n    flex: 1,\r\n    padding: theme.spacing(1),\r\n    overflowY: \"scroll\",\r\n    ...theme.scrollbarStyles,\r\n  },\r\n}));\r\n\r\nconst Users = () => {\r\n  const classes = useStyles();\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const [pageNumber, setPageNumber] = useState(1);\r\n  const [hasMore, setHasMore] = useState(false);\r\n  const [selectedUser, setSelectedUser] = useState(null);\r\n  const [deletingUser, setDeletingUser] = useState(null);\r\n  const [userModalOpen, setUserModalOpen] = useState(false);\r\n  const [confirmModalOpen, setConfirmModalOpen] = useState(false);\r\n  const [searchParam, setSearchParam] = useState(\"\");\r\n  const [users, dispatch] = useReducer(reducer, []);\r\n\r\n  useEffect(() => {\r\n    dispatch({ type: \"RESET\" });\r\n    setPageNumber(1);\r\n  }, [searchParam]);\r\n\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    const delayDebounceFn = setTimeout(() => {\r\n      const fetchUsers = async () => {\r\n        try {\r\n          const { data } = await api.get(\"/users/\", {\r\n            params: { searchParam, pageNumber },\r\n          });\r\n          dispatch({ type: \"LOAD_USERS\", payload: data.users });\r\n          setHasMore(data.hasMore);\r\n          setLoading(false);\r\n        } catch (err) {\r\n          toastError(err);\r\n        }\r\n      };\r\n      fetchUsers();\r\n    }, 500);\r\n    return () => clearTimeout(delayDebounceFn);\r\n  }, [searchParam, pageNumber]);\r\n\r\n  useEffect(() => {\r\n    const companyId = localStorage.getItem(\"companyId\");\r\n    const socket = socketConnection({ companyId });\r\n\r\n    socket.on(`company-${companyId}-user`, (data) => {\r\n      if (data.action === \"update\" || data.action === \"create\") {\r\n        dispatch({ type: \"UPDATE_USERS\", payload: data.user });\r\n      }\r\n\r\n      if (data.action === \"delete\") {\r\n        dispatch({ type: \"DELETE_USER\", payload: +data.userId });\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      socket.disconnect();\r\n    };\r\n  }, []);\r\n\r\n  const handleOpenUserModal = () => {\r\n    setSelectedUser(null);\r\n    setUserModalOpen(true);\r\n  };\r\n\r\n  const handleCloseUserModal = () => {\r\n    setSelectedUser(null);\r\n    setUserModalOpen(false);\r\n  };\r\n\r\n  const handleSearch = (event) => {\r\n    setSearchParam(event.target.value.toLowerCase());\r\n  };\r\n\r\n  const handleEditUser = (user) => {\r\n    setSelectedUser(user);\r\n    setUserModalOpen(true);\r\n  };\r\n\r\n  const handleDeleteUser = async (userId) => {\r\n    try {\r\n      await api.delete(`/users/${userId}`);\r\n      toast.success(i18n.t(\"users.toasts.deleted\"));\r\n    } catch (err) {\r\n      toastError(err);\r\n    }\r\n    setDeletingUser(null);\r\n    setSearchParam(\"\");\r\n    setPageNumber(1);\r\n  };\r\n\r\n  const loadMore = () => {\r\n    setPageNumber((prevState) => prevState + 1);\r\n  };\r\n\r\n  const handleScroll = (e) => {\r\n    if (!hasMore || loading) return;\r\n    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;\r\n    if (scrollHeight - (scrollTop + 100) < clientHeight) {\r\n      loadMore();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <MainContainer>\r\n      <ConfirmationModal\r\n        title={\r\n          deletingUser &&\r\n          `${i18n.t(\"users.confirmationModal.deleteTitle\")} ${\r\n            deletingUser.name\r\n          }?`\r\n        }\r\n        open={confirmModalOpen}\r\n        onClose={setConfirmModalOpen}\r\n        onConfirm={() => handleDeleteUser(deletingUser.id)}\r\n      >\r\n        {i18n.t(\"users.confirmationModal.deleteMessage\")}\r\n      </ConfirmationModal>\r\n      <UserModal\r\n        open={userModalOpen}\r\n        onClose={handleCloseUserModal}\r\n        aria-labelledby=\"form-dialog-title\"\r\n        userId={selectedUser && selectedUser.id}\r\n      />\r\n      <MainHeader>\r\n        <Title>{i18n.t(\"users.title\")}</Title>\r\n        <MainHeaderButtonsWrapper>\r\n          <TextField\r\n            placeholder={i18n.t(\"contacts.searchPlaceholder\")}\r\n            type=\"search\"\r\n            value={searchParam}\r\n            onChange={handleSearch}\r\n            InputProps={{\r\n              startAdornment: (\r\n                <InputAdornment position=\"start\">\r\n                  <SearchIcon style={{ color: \"gray\" }} />\r\n                </InputAdornment>\r\n              ),\r\n            }}\r\n          />\r\n          <Button\r\n            variant=\"contained\"\r\n            color=\"primary\"\r\n            onClick={handleOpenUserModal}\r\n          >\r\n            {i18n.t(\"users.buttons.add\")}\r\n          </Button>\r\n        </MainHeaderButtonsWrapper>\r\n      </MainHeader>\r\n      <Paper\r\n        className={classes.mainPaper}\r\n        variant=\"outlined\"\r\n        onScroll={handleScroll}\r\n      >\r\n        <Table size=\"small\">\r\n          <TableHead>\r\n            <TableRow>\r\n              <TableCell align=\"center\">{i18n.t(\"users.table.name\")}</TableCell>\r\n              <TableCell align=\"center\">\r\n                {i18n.t(\"users.table.email\")}\r\n              </TableCell>\r\n              <TableCell align=\"center\">\r\n                {i18n.t(\"users.table.profile\")}\r\n              </TableCell>\r\n              <TableCell align=\"center\">\r\n                {i18n.t(\"users.table.actions\")}\r\n              </TableCell>\r\n            </TableRow>\r\n          </TableHead>\r\n          <TableBody>\r\n            <>\r\n              {users.map((user) => (\r\n                <TableRow key={user.id}>\r\n                  <TableCell align=\"center\">{user.name}</TableCell>\r\n                  <TableCell align=\"center\">{user.email}</TableCell>\r\n                  <TableCell align=\"center\">{user.profile}</TableCell>\r\n                  <TableCell align=\"center\">\r\n                    <IconButton\r\n                      size=\"small\"\r\n                      onClick={() => handleEditUser(user)}\r\n                    >\r\n                      <EditIcon />\r\n                    </IconButton>\r\n\r\n                    <IconButton\r\n                      size=\"small\"\r\n                      onClick={(e) => {\r\n                        setConfirmModalOpen(true);\r\n                        setDeletingUser(user);\r\n                      }}\r\n                    >\r\n                      <DeleteOutlineIcon />\r\n                    </IconButton>\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))}\r\n              {loading && <TableRowSkeleton columns={4} />}\r\n            </>\r\n          </TableBody>\r\n        </Table>\r\n      </Paper>\r\n    </MainContainer>\r\n  );\r\n};\r\n\r\nexport default Users;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,OAAOC,IAAI,MAAM,wBAAwB;AACzC,OAAOC,GAAG,MAAM,uBAAuB;AACvC,OAAOC,UAAU,MAAM,8BAA8B;AAErD,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,UAAU,MAAM,2BAA2B;AAElD,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,OAAOC,YAAY,MAAM,+BAA+B;AAExD,OAAOC,GAAG,MAAM,oBAAoB;AACpC,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,UAAU,MAAM,yBAAyB;AAChD,SAASC,gBAAgB,QAAQ,uBAAuB;AAExD,MAAMC,OAAO,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACjC,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,EAAE;IAChC,MAAMC,KAAK,GAAGF,MAAM,CAACG,OAAO;IAC5B,MAAMC,QAAQ,GAAG,EAAE;IAEnBF,KAAK,CAACG,OAAO,CAAEC,IAAI,IAAK;MACtB,MAAMC,SAAS,GAAGR,KAAK,CAACS,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAKJ,IAAI,CAACI,EAAE,CAAC;MAC1D,IAAIH,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBR,KAAK,CAACQ,SAAS,CAAC,GAAGD,IAAI;MACzB,CAAC,MAAM;QACLF,QAAQ,CAACO,IAAI,CAACL,IAAI,CAAC;MACrB;IACF,CAAC,CAAC;IAEF,OAAO,CAAC,GAAGP,KAAK,EAAE,GAAGK,QAAQ,CAAC;EAChC;EAEA,IAAIJ,MAAM,CAACC,IAAI,KAAK,cAAc,EAAE;IAClC,MAAMK,IAAI,GAAGN,MAAM,CAACG,OAAO;IAC3B,MAAMI,SAAS,GAAGR,KAAK,CAACS,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAKJ,IAAI,CAACI,EAAE,CAAC;IAE1D,IAAIH,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBR,KAAK,CAACQ,SAAS,CAAC,GAAGD,IAAI;MACvB,OAAO,CAAC,GAAGP,KAAK,CAAC;IACnB,CAAC,MAAM;MACL,OAAO,CAACO,IAAI,EAAE,GAAGP,KAAK,CAAC;IACzB;EACF;EAEA,IAAIC,MAAM,CAACC,IAAI,KAAK,aAAa,EAAE;IACjC,MAAMW,MAAM,GAAGZ,MAAM,CAACG,OAAO;IAE7B,MAAMI,SAAS,GAAGR,KAAK,CAACS,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAKE,MAAM,CAAC;IACzD,IAAIL,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBR,KAAK,CAACc,MAAM,CAACN,SAAS,EAAE,CAAC,CAAC;IAC5B;IACA,OAAO,CAAC,GAAGR,KAAK,CAAC;EACnB;EAEA,IAAIC,MAAM,CAACC,IAAI,KAAK,OAAO,EAAE;IAC3B,OAAO,EAAE;EACX;AACF,CAAC;AAED,MAAMa,SAAS,GAAG5C,UAAU,CAAE6C,KAAK,KAAM;EACvCC,SAAS,EAAE;IACTC,IAAI,EAAE,CAAC;IACPC,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;IACzBC,SAAS,EAAE,QAAQ;IACnB,GAAGL,KAAK,CAACM;EACX;AACF,CAAC,CAAC,CAAC;AAEH,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAClB,MAAMC,OAAO,GAAGT,SAAS,CAAC,CAAC;EAE3B,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoE,aAAa,EAAEC,gBAAgB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoC,KAAK,EAAEsC,QAAQ,CAAC,GAAGxE,UAAU,CAAC8B,OAAO,EAAE,EAAE,CAAC;EAEjD/B,SAAS,CAAC,MAAM;IACdyE,QAAQ,CAAC;MAAEvC,IAAI,EAAE;IAAQ,CAAC,CAAC;IAC3B0B,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC,EAAE,CAACW,WAAW,CAAC,CAAC;EAEjBvE,SAAS,CAAC,MAAM;IACd0D,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMgB,eAAe,GAAGC,UAAU,CAAC,MAAM;MACvC,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;QAC7B,IAAI;UACF,MAAM;YAAEC;UAAK,CAAC,GAAG,MAAMrD,GAAG,CAACsD,GAAG,CAAC,SAAS,EAAE;YACxCC,MAAM,EAAE;cAAER,WAAW;cAAEZ;YAAW;UACpC,CAAC,CAAC;UACFc,QAAQ,CAAC;YAAEvC,IAAI,EAAE,YAAY;YAAEE,OAAO,EAAEyC,IAAI,CAAC1C;UAAM,CAAC,CAAC;UACrD2B,UAAU,CAACe,IAAI,CAAChB,OAAO,CAAC;UACxBH,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,CAAC,OAAOsB,GAAG,EAAE;UACZnD,UAAU,CAACmD,GAAG,CAAC;QACjB;MACF,CAAC;MACDJ,UAAU,CAAC,CAAC;IACd,CAAC,EAAE,GAAG,CAAC;IACP,OAAO,MAAMK,YAAY,CAACP,eAAe,CAAC;EAC5C,CAAC,EAAE,CAACH,WAAW,EAAEZ,UAAU,CAAC,CAAC;EAE7B3D,SAAS,CAAC,MAAM;IACd,MAAMkF,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,MAAM,GAAGvD,gBAAgB,CAAC;MAAEoD;IAAU,CAAC,CAAC;IAE9CG,MAAM,CAACC,EAAE,CAAC,WAAWJ,SAAS,OAAO,EAAGL,IAAI,IAAK;MAC/C,IAAIA,IAAI,CAAC5C,MAAM,KAAK,QAAQ,IAAI4C,IAAI,CAAC5C,MAAM,KAAK,QAAQ,EAAE;QACxDwC,QAAQ,CAAC;UAAEvC,IAAI,EAAE,cAAc;UAAEE,OAAO,EAAEyC,IAAI,CAACtC;QAAK,CAAC,CAAC;MACxD;MAEA,IAAIsC,IAAI,CAAC5C,MAAM,KAAK,QAAQ,EAAE;QAC5BwC,QAAQ,CAAC;UAAEvC,IAAI,EAAE,aAAa;UAAEE,OAAO,EAAE,CAACyC,IAAI,CAAChC;QAAO,CAAC,CAAC;MAC1D;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACXwC,MAAM,CAACE,UAAU,CAAC,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCxB,eAAe,CAAC,IAAI,CAAC;IACrBI,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMqB,oBAAoB,GAAGA,CAAA,KAAM;IACjCzB,eAAe,CAAC,IAAI,CAAC;IACrBI,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,MAAMsB,YAAY,GAAIC,KAAK,IAAK;IAC9BnB,cAAc,CAACmB,KAAK,CAACC,MAAM,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAMC,cAAc,GAAIxD,IAAI,IAAK;IAC/ByB,eAAe,CAACzB,IAAI,CAAC;IACrB6B,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM4B,gBAAgB,GAAG,MAAOnD,MAAM,IAAK;IACzC,IAAI;MACF,MAAMrB,GAAG,CAACyE,MAAM,CAAC,UAAUpD,MAAM,EAAE,CAAC;MACpC3C,KAAK,CAACgG,OAAO,CAACzE,IAAI,CAAC0E,CAAC,CAAC,sBAAsB,CAAC,CAAC;IAC/C,CAAC,CAAC,OAAOnB,GAAG,EAAE;MACZnD,UAAU,CAACmD,GAAG,CAAC;IACjB;IACAd,eAAe,CAAC,IAAI,CAAC;IACrBM,cAAc,CAAC,EAAE,CAAC;IAClBZ,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,MAAMwC,QAAQ,GAAGA,CAAA,KAAM;IACrBxC,aAAa,CAAEyC,SAAS,IAAKA,SAAS,GAAG,CAAC,CAAC;EAC7C,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,IAAI,CAAC1C,OAAO,IAAIJ,OAAO,EAAE;IACzB,MAAM;MAAE+C,SAAS;MAAEC,YAAY;MAAEC;IAAa,CAAC,GAAGH,CAAC,CAACI,aAAa;IACjE,IAAIF,YAAY,IAAID,SAAS,GAAG,GAAG,CAAC,GAAGE,YAAY,EAAE;MACnDN,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,oBACEtG,KAAA,CAAA8G,aAAA,CAACC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACZrH,KAAA,CAAA8G,aAAA,CAAChF,iBAAiB;IAChBwF,KAAK,EACHnD,YAAY,IACZ,GAAGxC,IAAI,CAAC0E,CAAC,CAAC,qCAAqC,CAAC,IAC9ClC,YAAY,CAACoD,IAAI,GAEpB;IACDC,IAAI,EAAEjD,gBAAiB;IACvBkD,OAAO,EAAEjD,mBAAoB;IAC7BkD,SAAS,EAAEA,CAAA,KAAMxB,gBAAgB,CAAC/B,YAAY,CAACtB,EAAE,CAAE;IAAAmE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAElD1F,IAAI,CAAC0E,CAAC,CAAC,uCAAuC,CAC9B,CAAC,eACpBrG,KAAA,CAAA8G,aAAA,CAACjF,SAAS;IACR2F,IAAI,EAAEnD,aAAc;IACpBoD,OAAO,EAAE9B,oBAAqB;IAC9B,mBAAgB,mBAAmB;IACnC5C,MAAM,EAAEkB,YAAY,IAAIA,YAAY,CAACpB,EAAG;IAAAmE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACzC,CAAC,eACFrH,KAAA,CAAA8G,aAAA,CAACa,UAAU;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACTrH,KAAA,CAAA8G,aAAA,CAACc,KAAK;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE1F,IAAI,CAAC0E,CAAC,CAAC,aAAa,CAAS,CAAC,eACtCrG,KAAA,CAAA8G,aAAA,CAACe,wBAAwB;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrH,KAAA,CAAA8G,aAAA,CAAChG,SAAS;IACRgH,WAAW,EAAEnG,IAAI,CAAC0E,CAAC,CAAC,4BAA4B,CAAE;IAClDjE,IAAI,EAAC,QAAQ;IACb2D,KAAK,EAAEtB,WAAY;IACnBsD,QAAQ,EAAEnC,YAAa;IACvBoC,UAAU,EAAE;MACVC,cAAc,eACZjI,KAAA,CAAA8G,aAAA,CAAC/F,cAAc;QAACmH,QAAQ,EAAC,OAAO;QAAAlB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBAC9BrH,KAAA,CAAA8G,aAAA,CAACjG,UAAU;QAACsH,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAApB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CACzB;IAEpB,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACH,CAAC,eACFrH,KAAA,CAAA8G,aAAA,CAACuB,MAAM;IACLC,OAAO,EAAC,WAAW;IACnBF,KAAK,EAAC,SAAS;IACfG,OAAO,EAAE7C,mBAAoB;IAAAsB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAE5B1F,IAAI,CAAC0E,CAAC,CAAC,mBAAmB,CACrB,CACgB,CAChB,CAAC,eACbrG,KAAA,CAAA8G,aAAA,CAACxG,KAAK;IACJkI,SAAS,EAAE9E,OAAO,CAACP,SAAU;IAC7BmF,OAAO,EAAC,UAAU;IAClBG,QAAQ,EAAEjC,YAAa;IAAAQ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvBrH,KAAA,CAAA8G,aAAA,CAACvG,KAAK;IAACmI,IAAI,EAAC,OAAO;IAAA1B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjBrH,KAAA,CAAA8G,aAAA,CAACpG,SAAS;IAAAsG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRrH,KAAA,CAAA8G,aAAA,CAACnG,QAAQ;IAAAqG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACPrH,KAAA,CAAA8G,aAAA,CAACrG,SAAS;IAACkI,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE1F,IAAI,CAAC0E,CAAC,CAAC,kBAAkB,CAAa,CAAC,eAClErG,KAAA,CAAA8G,aAAA,CAACrG,SAAS;IAACkI,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtB1F,IAAI,CAAC0E,CAAC,CAAC,mBAAmB,CAClB,CAAC,eACZrG,KAAA,CAAA8G,aAAA,CAACrG,SAAS;IAACkI,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtB1F,IAAI,CAAC0E,CAAC,CAAC,qBAAqB,CACpB,CAAC,eACZrG,KAAA,CAAA8G,aAAA,CAACrG,SAAS;IAACkI,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtB1F,IAAI,CAAC0E,CAAC,CAAC,qBAAqB,CACpB,CACH,CACD,CAAC,eACZrG,KAAA,CAAA8G,aAAA,CAACtG,SAAS;IAAAwG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACRrH,KAAA,CAAA8G,aAAA,CAAA9G,KAAA,CAAA4I,QAAA,QACGvG,KAAK,CAACwG,GAAG,CAAEpG,IAAI,iBACdzC,KAAA,CAAA8G,aAAA,CAACnG,QAAQ;IAACmI,GAAG,EAAErG,IAAI,CAACI,EAAG;IAAAmE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrBrH,KAAA,CAAA8G,aAAA,CAACrG,SAAS;IAACkI,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE5E,IAAI,CAAC8E,IAAgB,CAAC,eACjDvH,KAAA,CAAA8G,aAAA,CAACrG,SAAS;IAACkI,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE5E,IAAI,CAACsG,KAAiB,CAAC,eAClD/I,KAAA,CAAA8G,aAAA,CAACrG,SAAS;IAACkI,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE5E,IAAI,CAACuG,OAAmB,CAAC,eACpDhJ,KAAA,CAAA8G,aAAA,CAACrG,SAAS;IAACkI,KAAK,EAAC,QAAQ;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBrH,KAAA,CAAA8G,aAAA,CAAClG,UAAU;IACT8H,IAAI,EAAC,OAAO;IACZH,OAAO,EAAEA,CAAA,KAAMtC,cAAc,CAACxD,IAAI,CAAE;IAAAuE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpCrH,KAAA,CAAA8G,aAAA,CAACzF,QAAQ;IAAA2F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACD,CAAC,eAEbrH,KAAA,CAAA8G,aAAA,CAAClG,UAAU;IACT8H,IAAI,EAAC,OAAO;IACZH,OAAO,EAAG9B,CAAC,IAAK;MACdjC,mBAAmB,CAAC,IAAI,CAAC;MACzBJ,eAAe,CAAC3B,IAAI,CAAC;IACvB,CAAE;IAAAuE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFrH,KAAA,CAAA8G,aAAA,CAAC1F,iBAAiB;IAAA4F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACV,CACH,CACH,CACX,CAAC,EACD1D,OAAO,iBAAI3D,KAAA,CAAA8G,aAAA,CAAClF,gBAAgB;IAACqH,OAAO,EAAE,CAAE;IAAAjC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAC3C,CACO,CACN,CACF,CACM,CAAC;AAEpB,CAAC;AAED,eAAe5D,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}