{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@date-io/date-fns": "^2.14.0", "@material-ui/core": "4.12.3", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.56", "@material-ui/pickers": "^3.3.10", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.0.4", "@testing-library/user-event": "^12.1.7", "axios": "^0.21.1", "bootstrap": "^5.2.3", "compressorjs": "^1.2.1", "context": "^4.0.0", "date-fns": "^2.16.1", "emoji-mart": "^3.0.0", "formik": "^2.2.0", "formik-material-ui": "^3.0.1", "gn-api-sdk-node": "^3.0.2", "i18next": "^19.8.2", "i18next-browser-languagedetector": "^6.0.1", "markdown-to-jsx": "^7.1.0", "material-ui-color": "^1.2.0", "material-ui-popup-state": "^1.7.0", "mic-recorder-to-mp3": "^2.2.2", "moment": "^2.29.1", "qrcode.react": "^1.0.0", "query-string": "^7.0.0", "react": "^16.13.1", "react-bootstrap": "^2.7.0", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.1.0", "react-currency-format": "^1.1.0", "react-dom": "^16.13.1", "react-facebook-login": "^4.1.1", "react-icons": "^4.4.0", "react-input-mask": "^2.0.4", "react-modal-image": "^2.5.0", "react-number-format": "^4.6.4", "react-qr-code": "^2.0.7", "react-router-dom": "^5.2.0", "react-scripts": "3.4.3", "react-text-mask": "^5.5.0", "react-toastify": "9.0.0", "recharts": "^2.0.2", "socket.io-client": "^3.0.5", "styled-components": "^5.3.5", "text-mask-addons": "^3.8.0", "use-debounce": "^7.0.0", "use-sound": "^2.0.1", "uuid": "^8.3.2", "yup": "^0.32.8"}, "scripts": {"start": "react-scripts start", "legacy": "react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "build:dev": "dotenv -e .env.development react-scripts build", "winBuild": "set \"GENERATE_SOURCEMAP=false\" && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"dotenv-cli": "^7.2.1"}}