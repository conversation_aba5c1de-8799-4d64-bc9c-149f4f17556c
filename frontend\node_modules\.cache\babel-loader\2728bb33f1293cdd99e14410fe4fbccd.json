{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\components\\\\ContactNotesDialogListItem\\\\index.js\";\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport IconButton from '@material-ui/core/IconButton';\nimport ListItem from '@material-ui/core/ListItem';\nimport ListItemText from '@material-ui/core/ListItemText';\nimport ListItemAvatar from '@material-ui/core/ListItemAvatar';\nimport ListItemSecondaryAction from '@material-ui/core/ListItemSecondaryAction';\nimport Avatar from '@material-ui/core/Avatar';\nimport Typography from '@material-ui/core/Typography';\nimport { makeStyles } from '@material-ui/core/styles';\nimport DeleteIcon from '@material-ui/icons/Delete';\nimport moment from 'moment';\nconst useStyles = makeStyles(theme => ({\n  inline: {\n    width: '100%'\n  }\n}));\nexport default function ContactNotesDialogListItem(props) {\n  const {\n    note,\n    deleteItem\n  } = props;\n  const classes = useStyles();\n  const handleDelete = item => {\n    deleteItem(item);\n  };\n  return /*#__PURE__*/React.createElement(ListItem, {\n    alignItems: \"flex-start\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(ListItemAvatar, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(Avatar, {\n    alt: note.user.name,\n    src: \"/static/images/avatar/1.jpg\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(ListItemText, {\n    primary: /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Typography, {\n      component: \"span\",\n      variant: \"body2\",\n      className: classes.inline,\n      color: \"textPrimary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 25\n      }\n    }, note.note)),\n    secondary: /*#__PURE__*/React.createElement(React.Fragment, null, note.user.name, \", \", moment(note.createdAt).format('DD/MM/YY HH:mm')),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(ListItemSecondaryAction, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(IconButton, {\n    onClick: () => handleDelete(note),\n    edge: \"end\",\n    \"aria-label\": \"delete\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(DeleteIcon, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 21\n    }\n  }))));\n}\nContactNotesDialogListItem.propTypes = {\n  note: PropTypes.object.isRequired,\n  deleteItem: PropTypes.func.isRequired\n};", "map": {"version": 3, "names": ["React", "PropTypes", "IconButton", "ListItem", "ListItemText", "ListItemAvatar", "ListItemSecondaryAction", "Avatar", "Typography", "makeStyles", "DeleteIcon", "moment", "useStyles", "theme", "inline", "width", "ContactNotesDialogListItem", "props", "note", "deleteItem", "classes", "handleDelete", "item", "createElement", "alignItems", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "alt", "user", "name", "src", "primary", "Fragment", "component", "variant", "className", "color", "secondary", "createdAt", "format", "onClick", "edge", "propTypes", "object", "isRequired", "func"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/components/ContactNotesDialogListItem/index.js"], "sourcesContent": ["import React from 'react';\r\nimport PropTypes from 'prop-types';\r\nimport IconButton from '@material-ui/core/IconButton';\r\nimport ListItem from '@material-ui/core/ListItem';\r\nimport ListItemText from '@material-ui/core/ListItemText';\r\nimport ListItemAvatar from '@material-ui/core/ListItemAvatar';\r\nimport ListItemSecondaryAction from '@material-ui/core/ListItemSecondaryAction';\r\nimport Avatar from '@material-ui/core/Avatar';\r\nimport Typography from '@material-ui/core/Typography';\r\nimport { makeStyles } from '@material-ui/core/styles';\r\nimport DeleteIcon from '@material-ui/icons/Delete';\r\nimport moment from 'moment';\r\n\r\nconst useStyles = makeStyles((theme) => ({\r\n    inline: {\r\n        width: '100%'\r\n    }\r\n}));\r\n\r\nexport default function ContactNotesDialogListItem (props) {\r\n    const { note, deleteItem } = props;\r\n    const classes = useStyles();\r\n\r\n    const handleDelete = (item) => {\r\n        deleteItem(item);\r\n    }\r\n\r\n    return (\r\n        <ListItem alignItems=\"flex-start\">\r\n            <ListItemAvatar>\r\n                <Avatar alt={note.user.name} src=\"/static/images/avatar/1.jpg\" />\r\n            </ListItemAvatar>\r\n            <ListItemText\r\n                primary={\r\n                    <>\r\n                        <Typography\r\n                            component=\"span\"\r\n                            variant=\"body2\"\r\n                            className={classes.inline}\r\n                            color=\"textPrimary\"\r\n                        >\r\n                            {note.note}\r\n                        </Typography>\r\n                    </>\r\n                }\r\n                secondary={\r\n                    <>\r\n                        {note.user.name}, {moment(note.createdAt).format('DD/MM/YY HH:mm')}\r\n                    </>\r\n                }\r\n            />\r\n            <ListItemSecondaryAction>\r\n                <IconButton onClick={() => handleDelete(note)} edge=\"end\" aria-label=\"delete\">\r\n                    <DeleteIcon />\r\n                </IconButton>\r\n            </ListItemSecondaryAction>\r\n        </ListItem>\r\n    )   \r\n}\r\n\r\nContactNotesDialogListItem.propTypes = {\r\n    note: PropTypes.object.isRequired,\r\n    deleteItem: PropTypes.func.isRequired\r\n}"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,uBAAuB,MAAM,2CAA2C;AAC/E,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,OAAOC,UAAU,MAAM,8BAA8B;AACrD,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,MAAM,MAAM,QAAQ;AAE3B,MAAMC,SAAS,GAAGH,UAAU,CAAEI,KAAK,KAAM;EACrCC,MAAM,EAAE;IACJC,KAAK,EAAE;EACX;AACJ,CAAC,CAAC,CAAC;AAEH,eAAe,SAASC,0BAA0BA,CAAEC,KAAK,EAAE;EACvD,MAAM;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGF,KAAK;EAClC,MAAMG,OAAO,GAAGR,SAAS,CAAC,CAAC;EAE3B,MAAMS,YAAY,GAAIC,IAAI,IAAK;IAC3BH,UAAU,CAACG,IAAI,CAAC;EACpB,CAAC;EAED,oBACItB,KAAA,CAAAuB,aAAA,CAACpB,QAAQ;IAACqB,UAAU,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B9B,KAAA,CAAAuB,aAAA,CAAClB,cAAc;IAAAoB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACX9B,KAAA,CAAAuB,aAAA,CAAChB,MAAM;IAACwB,GAAG,EAAEb,IAAI,CAACc,IAAI,CAACC,IAAK;IAACC,GAAG,EAAC,6BAA6B;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACpD,CAAC,eACjB9B,KAAA,CAAAuB,aAAA,CAACnB,YAAY;IACT+B,OAAO,eACHnC,KAAA,CAAAuB,aAAA,CAAAvB,KAAA,CAAAoC,QAAA,qBACIpC,KAAA,CAAAuB,aAAA,CAACf,UAAU;MACP6B,SAAS,EAAC,MAAM;MAChBC,OAAO,EAAC,OAAO;MACfC,SAAS,EAAEnB,OAAO,CAACN,MAAO;MAC1B0B,KAAK,EAAC,aAAa;MAAAf,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAElBZ,IAAI,CAACA,IACE,CACd,CACL;IACDuB,SAAS,eACLzC,KAAA,CAAAuB,aAAA,CAAAvB,KAAA,CAAAoC,QAAA,QACKlB,IAAI,CAACc,IAAI,CAACC,IAAI,EAAC,IAAE,EAACtB,MAAM,CAACO,IAAI,CAACwB,SAAS,CAAC,CAACC,MAAM,CAAC,gBAAgB,CACnE,CACL;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACJ,CAAC,eACF9B,KAAA,CAAAuB,aAAA,CAACjB,uBAAuB;IAAAmB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpB9B,KAAA,CAAAuB,aAAA,CAACrB,UAAU;IAAC0C,OAAO,EAAEA,CAAA,KAAMvB,YAAY,CAACH,IAAI,CAAE;IAAC2B,IAAI,EAAC,KAAK;IAAC,cAAW,QAAQ;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzE9B,KAAA,CAAAuB,aAAA,CAACb,UAAU;IAAAe,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACL,CACS,CACnB,CAAC;AAEnB;AAEAd,0BAA0B,CAAC8B,SAAS,GAAG;EACnC5B,IAAI,EAAEjB,SAAS,CAAC8C,MAAM,CAACC,UAAU;EACjC7B,UAAU,EAAElB,SAAS,CAACgD,IAAI,CAACD;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}