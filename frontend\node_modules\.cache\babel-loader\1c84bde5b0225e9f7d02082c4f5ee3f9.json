{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M7.3 13.77L5.54 12l1.76-1.77-1.76-1.77L2 12l3.54 3.54zm8.24 4.69l-1.77-1.76L12 18.46l-1.77-1.76-1.77 1.76L12 22zm2.92-2.92L22 12l-3.54-3.54-1.76 1.77L18.46 12l-1.76 1.77zM12 5.54l1.77 1.76 1.77-1.76L12 2 8.46 5.54l1.77 1.76z\"\n}), /*#__PURE__*/React.createElement(\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\"\n})), 'ControlCameraTwoTone');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "Fragment", "d", "cx", "cy", "r"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/ControlCameraTwoTone.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M7.3 13.77L5.54 12l1.76-1.77-1.76-1.77L2 12l3.54 3.54zm8.24 4.69l-1.77-1.76L12 18.46l-1.77-1.76-1.77 1.76L12 22zm2.92-2.92L22 12l-3.54-3.54-1.76 1.77L18.46 12l-1.76 1.77zM12 5.54l1.77 1.76 1.77-1.76L12 2 8.46 5.54l1.77 1.76z\"\n}), /*#__PURE__*/React.createElement(\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\"\n})), 'ControlCameraTwoTone');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAACF,KAAK,CAACG,QAAQ,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC5HE,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,aAAaJ,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAC7CG,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,CAAC,CAAC,EAAE,sBAAsB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}