{"ast": null, "code": "import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\nimport { ColorWrap, Saturation, Hue } from '../common';\nimport GooglePointerCircle from './GooglePointerCircle';\nimport GooglePointer from './GooglePointer';\nimport GoogleFields from './GoogleFields';\nexport var Google = function Google(_ref) {\n  var width = _ref.width,\n    onChange = _ref.onChange,\n    rgb = _ref.rgb,\n    hsl = _ref.hsl,\n    hsv = _ref.hsv,\n    hex = _ref.hex,\n    header = _ref.header,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var styles = reactCSS(merge({\n    'default': {\n      picker: {\n        width: width,\n        background: '#fff',\n        border: '1px solid #dfe1e5',\n        boxSizing: 'initial',\n        display: 'flex',\n        flexWrap: 'wrap',\n        borderRadius: '8px 8px 0px 0px'\n      },\n      head: {\n        height: '57px',\n        width: '100%',\n        paddingTop: '16px',\n        paddingBottom: '16px',\n        paddingLeft: '16px',\n        fontSize: '20px',\n        boxSizing: 'border-box',\n        fontFamily: 'Roboto-Regular,HelveticaNeue,Arial,sans-serif'\n      },\n      saturation: {\n        width: '70%',\n        padding: '0px',\n        position: 'relative',\n        overflow: 'hidden'\n      },\n      swatch: {\n        width: '30%',\n        height: '228px',\n        padding: '0px',\n        background: 'rgba(' + rgb.r + ', ' + rgb.g + ', ' + rgb.b + ', 1)',\n        position: 'relative',\n        overflow: 'hidden'\n      },\n      body: {\n        margin: 'auto',\n        width: '95%'\n      },\n      controls: {\n        display: 'flex',\n        boxSizing: 'border-box',\n        height: '52px',\n        paddingTop: '22px'\n      },\n      color: {\n        width: '32px'\n      },\n      hue: {\n        height: '8px',\n        position: 'relative',\n        margin: '0px 16px 0px 16px',\n        width: '100%'\n      },\n      Hue: {\n        radius: '2px'\n      }\n    }\n  }, passedStyles));\n  return React.createElement('div', {\n    style: styles.picker,\n    className: 'google-picker ' + className\n  }, React.createElement('div', {\n    style: styles.head\n  }, header), React.createElement('div', {\n    style: styles.swatch\n  }), React.createElement('div', {\n    style: styles.saturation\n  }, React.createElement(Saturation, {\n    hsl: hsl,\n    hsv: hsv,\n    pointer: GooglePointerCircle,\n    onChange: onChange\n  })), React.createElement('div', {\n    style: styles.body\n  }, React.createElement('div', {\n    style: styles.controls,\n    className: 'flexbox-fix'\n  }, React.createElement('div', {\n    style: styles.hue\n  }, React.createElement(Hue, {\n    style: styles.Hue,\n    hsl: hsl,\n    radius: '4px',\n    pointer: GooglePointer,\n    onChange: onChange\n  }))), React.createElement(GoogleFields, {\n    rgb: rgb,\n    hsl: hsl,\n    hex: hex,\n    hsv: hsv,\n    onChange: onChange\n  })));\n};\nGoogle.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  styles: PropTypes.object,\n  header: PropTypes.string\n};\nGoogle.defaultProps = {\n  width: 652,\n  styles: {},\n  header: 'Color picker'\n};\nexport default ColorWrap(Google);", "map": {"version": 3, "names": ["React", "PropTypes", "reactCSS", "merge", "ColorWrap", "Saturation", "<PERSON><PERSON>", "GooglePointerCircle", "GooglePointer", "GoogleFields", "Google", "_ref", "width", "onChange", "rgb", "hsl", "hsv", "hex", "header", "_ref$styles", "styles", "passedStyles", "undefined", "_ref$className", "className", "picker", "background", "border", "boxSizing", "display", "flexWrap", "borderRadius", "head", "height", "paddingTop", "paddingBottom", "paddingLeft", "fontSize", "fontFamily", "saturation", "padding", "position", "overflow", "swatch", "r", "g", "b", "body", "margin", "controls", "color", "hue", "radius", "createElement", "style", "pointer", "propTypes", "oneOfType", "string", "number", "object", "defaultProps"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/google/Google.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\n\nimport { ColorWrap, Saturation, Hue } from '../common';\nimport GooglePointerCircle from './GooglePointerCircle';\nimport GooglePointer from './GooglePointer';\nimport GoogleFields from './GoogleFields';\n\nexport var Google = function Google(_ref) {\n  var width = _ref.width,\n      onChange = _ref.onChange,\n      rgb = _ref.rgb,\n      hsl = _ref.hsl,\n      hsv = _ref.hsv,\n      hex = _ref.hex,\n      header = _ref.header,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var styles = reactCSS(merge({\n    'default': {\n      picker: {\n        width: width,\n        background: '#fff',\n        border: '1px solid #dfe1e5',\n        boxSizing: 'initial',\n        display: 'flex',\n        flexWrap: 'wrap',\n        borderRadius: '8px 8px 0px 0px'\n      },\n      head: {\n        height: '57px',\n        width: '100%',\n        paddingTop: '16px',\n        paddingBottom: '16px',\n        paddingLeft: '16px',\n        fontSize: '20px',\n        boxSizing: 'border-box',\n        fontFamily: 'Roboto-Regular,HelveticaNeue,Arial,sans-serif'\n      },\n      saturation: {\n        width: '70%',\n        padding: '0px',\n        position: 'relative',\n        overflow: 'hidden'\n      },\n      swatch: {\n        width: '30%',\n        height: '228px',\n        padding: '0px',\n        background: 'rgba(' + rgb.r + ', ' + rgb.g + ', ' + rgb.b + ', 1)',\n        position: 'relative',\n        overflow: 'hidden'\n      },\n      body: {\n        margin: 'auto',\n        width: '95%'\n      },\n      controls: {\n        display: 'flex',\n        boxSizing: 'border-box',\n        height: '52px',\n        paddingTop: '22px'\n      },\n      color: {\n        width: '32px'\n      },\n      hue: {\n        height: '8px',\n        position: 'relative',\n        margin: '0px 16px 0px 16px',\n        width: '100%'\n      },\n      Hue: {\n        radius: '2px'\n      }\n    }\n  }, passedStyles));\n  return React.createElement(\n    'div',\n    { style: styles.picker, className: 'google-picker ' + className },\n    React.createElement(\n      'div',\n      { style: styles.head },\n      header\n    ),\n    React.createElement('div', { style: styles.swatch }),\n    React.createElement(\n      'div',\n      { style: styles.saturation },\n      React.createElement(Saturation, {\n        hsl: hsl,\n        hsv: hsv,\n        pointer: GooglePointerCircle,\n        onChange: onChange\n      })\n    ),\n    React.createElement(\n      'div',\n      { style: styles.body },\n      React.createElement(\n        'div',\n        { style: styles.controls, className: 'flexbox-fix' },\n        React.createElement(\n          'div',\n          { style: styles.hue },\n          React.createElement(Hue, {\n            style: styles.Hue,\n            hsl: hsl,\n            radius: '4px',\n            pointer: GooglePointer,\n            onChange: onChange\n          })\n        )\n      ),\n      React.createElement(GoogleFields, {\n        rgb: rgb,\n        hsl: hsl,\n        hex: hex,\n        hsv: hsv,\n        onChange: onChange\n      })\n    )\n  );\n};\n\nGoogle.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  styles: PropTypes.object,\n  header: PropTypes.string\n\n};\n\nGoogle.defaultProps = {\n  width: 652,\n  styles: {},\n  header: 'Color picker'\n};\n\nexport default ColorWrap(Google);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,SAAS,EAAEC,UAAU,EAAEC,GAAG,QAAQ,WAAW;AACtD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,YAAY,MAAM,gBAAgB;AAEzC,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACxC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,GAAG,GAAGH,IAAI,CAACG,GAAG;IACdC,GAAG,GAAGJ,IAAI,CAACI,GAAG;IACdC,GAAG,GAAGL,IAAI,CAACK,GAAG;IACdC,GAAG,GAAGN,IAAI,CAACM,GAAG;IACdC,MAAM,GAAGP,IAAI,CAACO,MAAM;IACpBC,WAAW,GAAGR,IAAI,CAACS,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,cAAc,GAAGZ,IAAI,CAACa,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKD,SAAS,GAAG,EAAE,GAAGC,cAAc;EAElE,IAAIH,MAAM,GAAGlB,QAAQ,CAACC,KAAK,CAAC;IAC1B,SAAS,EAAE;MACTsB,MAAM,EAAE;QACNb,KAAK,EAAEA,KAAK;QACZc,UAAU,EAAE,MAAM;QAClBC,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE,SAAS;QACpBC,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,MAAM;QAChBC,YAAY,EAAE;MAChB,CAAC;MACDC,IAAI,EAAE;QACJC,MAAM,EAAE,MAAM;QACdrB,KAAK,EAAE,MAAM;QACbsB,UAAU,EAAE,MAAM;QAClBC,aAAa,EAAE,MAAM;QACrBC,WAAW,EAAE,MAAM;QACnBC,QAAQ,EAAE,MAAM;QAChBT,SAAS,EAAE,YAAY;QACvBU,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACV3B,KAAK,EAAE,KAAK;QACZ4B,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDC,MAAM,EAAE;QACN/B,KAAK,EAAE,KAAK;QACZqB,MAAM,EAAE,OAAO;QACfO,OAAO,EAAE,KAAK;QACdd,UAAU,EAAE,OAAO,GAAGZ,GAAG,CAAC8B,CAAC,GAAG,IAAI,GAAG9B,GAAG,CAAC+B,CAAC,GAAG,IAAI,GAAG/B,GAAG,CAACgC,CAAC,GAAG,MAAM;QAClEL,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDK,IAAI,EAAE;QACJC,MAAM,EAAE,MAAM;QACdpC,KAAK,EAAE;MACT,CAAC;MACDqC,QAAQ,EAAE;QACRpB,OAAO,EAAE,MAAM;QACfD,SAAS,EAAE,YAAY;QACvBK,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE;MACd,CAAC;MACDgB,KAAK,EAAE;QACLtC,KAAK,EAAE;MACT,CAAC;MACDuC,GAAG,EAAE;QACHlB,MAAM,EAAE,KAAK;QACbQ,QAAQ,EAAE,UAAU;QACpBO,MAAM,EAAE,mBAAmB;QAC3BpC,KAAK,EAAE;MACT,CAAC;MACDN,GAAG,EAAE;QACH8C,MAAM,EAAE;MACV;IACF;EACF,CAAC,EAAE/B,YAAY,CAAC,CAAC;EACjB,OAAOrB,KAAK,CAACqD,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAACK,MAAM;IAAED,SAAS,EAAE,gBAAgB,GAAGA;EAAU,CAAC,EACjExB,KAAK,CAACqD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAACY;EAAK,CAAC,EACtBd,MACF,CAAC,EACDlB,KAAK,CAACqD,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAElC,MAAM,CAACuB;EAAO,CAAC,CAAC,EACpD3C,KAAK,CAACqD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAACmB;EAAW,CAAC,EAC5BvC,KAAK,CAACqD,aAAa,CAAChD,UAAU,EAAE;IAC9BU,GAAG,EAAEA,GAAG;IACRC,GAAG,EAAEA,GAAG;IACRuC,OAAO,EAAEhD,mBAAmB;IAC5BM,QAAQ,EAAEA;EACZ,CAAC,CACH,CAAC,EACDb,KAAK,CAACqD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAAC2B;EAAK,CAAC,EACtB/C,KAAK,CAACqD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAAC6B,QAAQ;IAAEzB,SAAS,EAAE;EAAc,CAAC,EACpDxB,KAAK,CAACqD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAElC,MAAM,CAAC+B;EAAI,CAAC,EACrBnD,KAAK,CAACqD,aAAa,CAAC/C,GAAG,EAAE;IACvBgD,KAAK,EAAElC,MAAM,CAACd,GAAG;IACjBS,GAAG,EAAEA,GAAG;IACRqC,MAAM,EAAE,KAAK;IACbG,OAAO,EAAE/C,aAAa;IACtBK,QAAQ,EAAEA;EACZ,CAAC,CACH,CACF,CAAC,EACDb,KAAK,CAACqD,aAAa,CAAC5C,YAAY,EAAE;IAChCK,GAAG,EAAEA,GAAG;IACRC,GAAG,EAAEA,GAAG;IACRE,GAAG,EAAEA,GAAG;IACRD,GAAG,EAAEA,GAAG;IACRH,QAAQ,EAAEA;EACZ,CAAC,CACH,CACF,CAAC;AACH,CAAC;AAEDH,MAAM,CAAC8C,SAAS,GAAG;EACjB5C,KAAK,EAAEX,SAAS,CAACwD,SAAS,CAAC,CAACxD,SAAS,CAACyD,MAAM,EAAEzD,SAAS,CAAC0D,MAAM,CAAC,CAAC;EAChEvC,MAAM,EAAEnB,SAAS,CAAC2D,MAAM;EACxB1C,MAAM,EAAEjB,SAAS,CAACyD;AAEpB,CAAC;AAEDhD,MAAM,CAACmD,YAAY,GAAG;EACpBjD,KAAK,EAAE,GAAG;EACVQ,MAAM,EAAE,CAAC,CAAC;EACVF,MAAM,EAAE;AACV,CAAC;AAED,eAAed,SAAS,CAACM,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}