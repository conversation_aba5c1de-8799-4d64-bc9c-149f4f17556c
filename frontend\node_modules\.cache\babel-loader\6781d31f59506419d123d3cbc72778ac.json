{"ast": null, "code": "import rng from './rng.js';\nimport stringify from './stringify.js';\nfunction v4(options, buf, offset) {\n  options = options || {};\n  var rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n    for (var i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n    return buf;\n  }\n  return stringify(rnds);\n}\nexport default v4;", "map": {"version": 3, "names": ["rng", "stringify", "v4", "options", "buf", "offset", "rnds", "random", "i"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/uuid/dist/esm-browser/v4.js"], "sourcesContent": ["import rng from './rng.js';\nimport stringify from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  options = options || {};\n  var rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (var i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return stringify(rnds);\n}\n\nexport default v4;"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,OAAOC,SAAS,MAAM,gBAAgB;AAEtC,SAASC,EAAEA,CAACC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAChCF,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIG,IAAI,GAAGH,OAAO,CAACI,MAAM,IAAI,CAACJ,OAAO,CAACH,GAAG,IAAIA,GAAG,EAAE,CAAC,CAAC,CAAC;;EAErDM,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;EAC/BA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;EAEjC,IAAIF,GAAG,EAAE;IACPC,MAAM,GAAGA,MAAM,IAAI,CAAC;IAEpB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MAC3BJ,GAAG,CAACC,MAAM,GAAGG,CAAC,CAAC,GAAGF,IAAI,CAACE,CAAC,CAAC;IAC3B;IAEA,OAAOJ,GAAG;EACZ;EAEA,OAAOH,SAAS,CAACK,IAAI,CAAC;AACxB;AAEA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module"}