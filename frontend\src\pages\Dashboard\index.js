import React, { useState, useEffect } from "react";

import Paper from "@material-ui/core/Paper";
import Grid from "@material-ui/core/Grid";
import MenuItem from "@material-ui/core/MenuItem";
import FormControl from "@material-ui/core/FormControl";
import InputLabel from "@material-ui/core/InputLabel";
import Select from "@material-ui/core/Select";
import TextField from "@material-ui/core/TextField";
import FormHelperText from "@material-ui/core/FormHelperText";
import Typography from "@material-ui/core/Typography";
import Box from "@material-ui/core/Box";

import SpeedIcon from "@material-ui/icons/Speed";
import GroupIcon from "@material-ui/icons/Group";
import AssignmentIcon from "@material-ui/icons/Assignment";
import PersonIcon from "@material-ui/icons/Person";
import TodayIcon from '@material-ui/icons/Today';
import BlockIcon from '@material-ui/icons/Block';
import DoneIcon from '@material-ui/icons/Done';
import TrendingUpIcon from '@material-ui/icons/TrendingUp';
import BarChartIcon from '@material-ui/icons/BarChart';

import { makeStyles } from "@material-ui/core/styles";
import { toast } from "react-toastify";

import Chart from "./Chart";
import ButtonWithSpinner from "../../components/ButtonWithSpinner";
import ModernPageContainer from "../../components/ModernPageContainer";
import ModernCard from "../../components/ModernCard";
import ModernButton from "../../components/ModernButton";

import CardCounter from "../../components/Dashboard/CardCounter";
import TableAttendantsStatus from "../../components/Dashboard/TableAttendantsStatus";
import { isArray } from "lodash";

import useDashboard from "../../hooks/useDashboard";
import useCompanies from "../../hooks/useCompanies";

import { isEmpty } from "lodash";
import moment from "moment";

const useStyles = makeStyles((theme) => ({
  statsGrid: {
    marginBottom: theme.spacing(4),
  },
  modernPaper: {
    padding: theme.spacing(3),
    borderRadius: 16,
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    border: '1px solid rgba(0,0,0,0.04)',
    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
    height: '100%',
  },
  chartPaper: {
    padding: theme.spacing(3),
    borderRadius: 16,
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    border: '1px solid rgba(0,0,0,0.04)',
    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
    minHeight: 400,
  },
  filtersPaper: {
    padding: theme.spacing(3),
    borderRadius: 16,
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    border: '1px solid rgba(0,0,0,0.04)',
    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
    marginBottom: theme.spacing(3),
  },
  sectionTitle: {
    fontSize: '1.25rem',
    fontWeight: 600,
    color: theme.palette.text.primary,
    marginBottom: theme.spacing(2),
    display: 'flex',
    alignItems: 'center',
    '& svg': {
      marginRight: theme.spacing(1),
      color: theme.palette.primary.main,
    }
  },
  filterRow: {
    display: 'flex',
    gap: theme.spacing(2),
    alignItems: 'center',
    flexWrap: 'wrap',
    [theme.breakpoints.down('sm')]: {
      flexDirection: 'column',
      alignItems: 'stretch',
    }
  },
  filterControl: {
    minWidth: 200,
    [theme.breakpoints.down('sm')]: {
      minWidth: '100%',
    },
    '& .MuiOutlinedInput-root': {
      borderRadius: 8,
    }
  },
  dateField: {
    '& .MuiOutlinedInput-root': {
      borderRadius: 8,
    }
  },
  updateButton: {
    height: 40,
    minWidth: 120,
  },
  welcomeSection: {
    marginBottom: theme.spacing(4),
    padding: theme.spacing(3),
    borderRadius: 16,
    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',
    border: '1px solid rgba(102, 126, 234, 0.1)',
  },
  welcomeTitle: {
    fontSize: '1.5rem',
    fontWeight: 700,
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    backgroundClip: 'text',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    marginBottom: theme.spacing(1),
  },
  welcomeSubtitle: {
    color: theme.palette.text.secondary,
    fontSize: '1rem',
  }
}));

const Dashboard = () => {
  const classes = useStyles();
  const [counters, setCounters] = useState({});
  const [attendants, setAttendants] = useState([]);
  const [filterType, setFilterType] = useState(1);
  const [period, setPeriod] = useState(0);
  const [companyDueDate, setCompanyDueDate] = useState();
  const [dateFrom, setDateFrom] = useState(
    moment("1", "D").format("YYYY-MM-DD")
  );
  const [dateTo, setDateTo] = useState(moment().format("YYYY-MM-DD"));
  const [loading, setLoading] = useState(false);
  const { find } = useDashboard();
  const { finding } = useCompanies();
  useEffect(() => {
    async function firstLoad() {
      await fetchData();
    }
    setTimeout(() => {
      firstLoad();
    }, 1000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  async function handleChangePeriod(value) {
    setPeriod(value);
  }

  async function handleChangeFilterType(value) {
    setFilterType(value);
    if (value === 1) {
      setPeriod(0);
    } else {
      setDateFrom("");
      setDateTo("");
    }
  }

  async function fetchData() {
    setLoading(true);

    let params = {};

    if (period > 0) {
      params = {
        days: period,
      };
    }

    if (!isEmpty(dateFrom) && moment(dateFrom).isValid()) {
      params = {
        ...params,
        date_from: moment(dateFrom).format("YYYY-MM-DD"),
      };
    }

    if (!isEmpty(dateTo) && moment(dateTo).isValid()) {
      params = {
        ...params,
        date_to: moment(dateTo).format("YYYY-MM-DD"),
      };
    }

    if (Object.keys(params).length === 0) {
      toast.error("Parametrize o filtro");
      setLoading(false);
      return;
    }

    const data = await find(params);



    setCounters(data.counters);
    if (isArray(data.attendants)) {
      setAttendants(data.attendants);
    } else {
      setAttendants([]);
    }

    setLoading(false);
  }

  useEffect(() => {
    async function fetchData() {
      await loadCompanies();
    }
    fetchData();
  }, [])
  //let companyDueDate = localStorage.getItem("companyDueDate");
  //const companyDueDate = localStorage.getItem("companyDueDate").toString();
  const companyId = localStorage.getItem("companyId");
  const loadCompanies = async () => {
    setLoading(true);
    try {
      const companiesList = await finding(companyId);
      setCompanyDueDate(moment(companiesList.dueDate).format("DD/MM/yyyy"));
    } catch (e) {
      console.log("🚀 Console Log : e", e);
      // toast.error("Não foi possível carregar a lista de registros");
    }
    setLoading(false);
  };

  function formatTime(minutes) {
    return moment()
      .startOf("day")
      .add(minutes, "minutes")
      .format("HH[h] mm[m]");
  }

  function renderFilters() {
    if (filterType === 1) {
      return (
        <>
          <TextField
            label="Data Inicial"
            type="date"
            value={dateFrom}
            onChange={(e) => setDateFrom(e.target.value)}
            variant="outlined"
            size="small"
            className={classes.dateField}
            InputLabelProps={{
              shrink: true,
            }}
          />
          <TextField
            label="Data Final"
            type="date"
            value={dateTo}
            onChange={(e) => setDateTo(e.target.value)}
            variant="outlined"
            size="small"
            className={classes.dateField}
            InputLabelProps={{
              shrink: true,
            }}
          />
        </>
      );
    } else {
      return (
        <FormControl variant="outlined" size="small" className={classes.filterControl}>
          <InputLabel id="period-selector-label">Período</InputLabel>
          <Select
            labelId="period-selector-label"
            id="period-selector"
            value={period}
            onChange={(e) => handleChangePeriod(e.target.value)}
            label="Período"
          >
            <MenuItem value={0}>Nenhum selecionado</MenuItem>
            <MenuItem value={3}>Últimos 3 dias</MenuItem>
            <MenuItem value={7}>Últimos 7 dias</MenuItem>
            <MenuItem value={15}>Últimos 15 dias</MenuItem>
            <MenuItem value={30}>Últimos 30 dias</MenuItem>
            <MenuItem value={60}>Últimos 60 dias</MenuItem>
            <MenuItem value={90}>Últimos 90 dias</MenuItem>
          </Select>
        </FormControl>
      );
    }
  }

  return (
    <ModernPageContainer
      title="Dashboard"
      subtitle="Visão geral do sistema e métricas de atendimento"
      breadcrumbs={[
        { label: 'Dashboard', href: '/dashboard' }
      ]}
    >
      {/* Welcome Section */}
      <Box className={classes.welcomeSection}>
        <Typography className={classes.welcomeTitle}>
          Bem-vindo ao Dashboard
        </Typography>
        <Typography className={classes.welcomeSubtitle}>
          Acompanhe as métricas e performance do seu atendimento em tempo real
        </Typography>
      </Box>

      {/* Filters Section */}
      <Paper className={classes.filtersPaper}>
        <Typography className={classes.sectionTitle}>
          <BarChartIcon />
          Filtros e Configurações
        </Typography>

        <div className={classes.filterRow}>
          <FormControl variant="outlined" size="small" className={classes.filterControl}>
            <InputLabel id="filter-type-label">Tipo de Filtro</InputLabel>
            <Select
              labelId="filter-type-label"
              value={filterType}
              onChange={(e) => handleChangeFilterType(e.target.value)}
              label="Tipo de Filtro"
            >
              <MenuItem value={1}>Filtro por Data</MenuItem>
              <MenuItem value={2}>Filtro por Período</MenuItem>
            </Select>
          </FormControl>

          {renderFilters()}

          <ModernButton
            variant="primary"
            onClick={() => fetchData()}
            loading={loading}
            className={classes.updateButton}
            startIcon={<TrendingUpIcon />}
          >
            Atualizar
          </ModernButton>
        </div>
      </Paper>

      {/* Stats Cards */}
      <Grid container spacing={3} className={classes.statsGrid}>
        <Grid item xs={12} sm={6} md={3}>
          <CardCounter
            icon={<TodayIcon fontSize="inherit" />}
            title="Data Vencimento"
            value={companyDueDate}
            loading={loading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <CardCounter
            icon={<GroupIcon fontSize="inherit" />}
            title="Atd. Pendentes"
            value={counters.supportPending}
            loading={loading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <CardCounter
            icon={<DoneIcon fontSize="inherit" />}
            title="Atd. Acontecendo"
            value={counters.supportHappening}
            loading={loading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <CardCounter
            icon={<AssignmentIcon fontSize="inherit" />}
            title="Atd. Realizados"
            value={counters.supportFinished}
            loading={loading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <CardCounter
            icon={<PersonIcon fontSize="inherit" />}
            title="Leads"
            value={counters.leads}
            loading={loading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <CardCounter
            icon={<SpeedIcon fontSize="inherit" />}
            title="T.M. de Atendimento"
            value={formatTime(counters.avgSupportTime)}
            loading={loading}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <CardCounter
            icon={<SpeedIcon fontSize="inherit" />}
            title="T.M. de Espera"
            value={formatTime(counters.avgWaitTime)}
            loading={loading}
          />
        </Grid>
      </Grid>

      {/* Chart Section */}
      <Paper className={classes.chartPaper}>
        <Typography className={classes.sectionTitle}>
          <TrendingUpIcon />
          Gráfico de Performance
        </Typography>
        <Chart />
      </Paper>

      {/* Attendants Table */}
      {attendants.length > 0 && (
        <Paper className={classes.modernPaper}>
          <Typography className={classes.sectionTitle}>
            <GroupIcon />
            Status dos Atendentes
          </Typography>
          <TableAttendantsStatus
            attendants={attendants}
            loading={loading}
          />
        </Paper>
      )}
    </ModernPageContainer>
  );
};

export default Dashboard;
