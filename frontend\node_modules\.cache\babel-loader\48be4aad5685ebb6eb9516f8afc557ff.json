{"ast": null, "code": "import store from './store';\nvar DEFAULTS = ['+1', 'grinning', 'kissing_heart', 'heart_eyes', 'laughing', 'stuck_out_tongue_winking_eye', 'sweat_smile', 'joy', 'scream', 'disappointed', 'unamused', 'weary', 'sob', 'sunglasses', 'heart', 'poop'];\nvar frequently, initialized;\nvar defaults = {};\nfunction init() {\n  initialized = true;\n  frequently = store.get('frequently');\n}\nfunction add(emoji) {\n  if (!initialized) init();\n  var id = emoji.id;\n  frequently || (frequently = defaults);\n  frequently[id] || (frequently[id] = 0);\n  frequently[id] += 1;\n  store.set('last', id);\n  store.set('frequently', frequently);\n}\nfunction get(perLine) {\n  if (!initialized) init();\n  if (!frequently) {\n    defaults = {};\n    var result = [];\n    for (var i = 0; i < perLine; i++) {\n      defaults[DEFAULTS[i]] = perLine - i;\n      result.push(DEFAULTS[i]);\n    }\n    return result;\n  }\n  var quantity = perLine * 4;\n  var frequentlyKeys = [];\n  for (var key in frequently) {\n    if (frequently.hasOwnProperty(key)) {\n      frequentlyKeys.push(key);\n    }\n  }\n  var sorted = frequentlyKeys.sort(function (a, b) {\n    return frequently[a] - frequently[b];\n  }).reverse();\n  var sliced = sorted.slice(0, quantity);\n  var last = store.get('last');\n  if (last && sliced.indexOf(last) == -1) {\n    sliced.pop();\n    sliced.push(last);\n  }\n  return sliced;\n}\nexport default {\n  add: add,\n  get: get\n};", "map": {"version": 3, "names": ["store", "DEFAULTS", "frequently", "initialized", "defaults", "init", "get", "add", "emoji", "id", "set", "perLine", "result", "i", "push", "quantity", "frequentlyKeys", "key", "hasOwnProperty", "sorted", "sort", "a", "b", "reverse", "sliced", "slice", "last", "indexOf", "pop"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/emoji-mart/dist-es/utils/frequently.js"], "sourcesContent": ["import store from './store';\nvar DEFAULTS = ['+1', 'grinning', 'kissing_heart', 'heart_eyes', 'laughing', 'stuck_out_tongue_winking_eye', 'sweat_smile', 'joy', 'scream', 'disappointed', 'unamused', 'weary', 'sob', 'sunglasses', 'heart', 'poop'];\nvar frequently, initialized;\nvar defaults = {};\n\nfunction init() {\n  initialized = true;\n  frequently = store.get('frequently');\n}\n\nfunction add(emoji) {\n  if (!initialized) init();\n  var id = emoji.id;\n  frequently || (frequently = defaults);\n  frequently[id] || (frequently[id] = 0);\n  frequently[id] += 1;\n  store.set('last', id);\n  store.set('frequently', frequently);\n}\n\nfunction get(perLine) {\n  if (!initialized) init();\n\n  if (!frequently) {\n    defaults = {};\n    var result = [];\n\n    for (var i = 0; i < perLine; i++) {\n      defaults[DEFAULTS[i]] = perLine - i;\n      result.push(DEFAULTS[i]);\n    }\n\n    return result;\n  }\n\n  var quantity = perLine * 4;\n  var frequentlyKeys = [];\n\n  for (var key in frequently) {\n    if (frequently.hasOwnProperty(key)) {\n      frequentlyKeys.push(key);\n    }\n  }\n\n  var sorted = frequentlyKeys.sort(function (a, b) {\n    return frequently[a] - frequently[b];\n  }).reverse();\n  var sliced = sorted.slice(0, quantity);\n  var last = store.get('last');\n\n  if (last && sliced.indexOf(last) == -1) {\n    sliced.pop();\n    sliced.push(last);\n  }\n\n  return sliced;\n}\n\nexport default {\n  add: add,\n  get: get\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,IAAIC,QAAQ,GAAG,CAAC,IAAI,EAAE,UAAU,EAAE,eAAe,EAAE,YAAY,EAAE,UAAU,EAAE,8BAA8B,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,CAAC;AACvN,IAAIC,UAAU,EAAEC,WAAW;AAC3B,IAAIC,QAAQ,GAAG,CAAC,CAAC;AAEjB,SAASC,IAAIA,CAAA,EAAG;EACdF,WAAW,GAAG,IAAI;EAClBD,UAAU,GAAGF,KAAK,CAACM,GAAG,CAAC,YAAY,CAAC;AACtC;AAEA,SAASC,GAAGA,CAACC,KAAK,EAAE;EAClB,IAAI,CAACL,WAAW,EAAEE,IAAI,CAAC,CAAC;EACxB,IAAII,EAAE,GAAGD,KAAK,CAACC,EAAE;EACjBP,UAAU,KAAKA,UAAU,GAAGE,QAAQ,CAAC;EACrCF,UAAU,CAACO,EAAE,CAAC,KAAKP,UAAU,CAACO,EAAE,CAAC,GAAG,CAAC,CAAC;EACtCP,UAAU,CAACO,EAAE,CAAC,IAAI,CAAC;EACnBT,KAAK,CAACU,GAAG,CAAC,MAAM,EAAED,EAAE,CAAC;EACrBT,KAAK,CAACU,GAAG,CAAC,YAAY,EAAER,UAAU,CAAC;AACrC;AAEA,SAASI,GAAGA,CAACK,OAAO,EAAE;EACpB,IAAI,CAACR,WAAW,EAAEE,IAAI,CAAC,CAAC;EAExB,IAAI,CAACH,UAAU,EAAE;IACfE,QAAQ,GAAG,CAAC,CAAC;IACb,IAAIQ,MAAM,GAAG,EAAE;IAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,EAAEE,CAAC,EAAE,EAAE;MAChCT,QAAQ,CAACH,QAAQ,CAACY,CAAC,CAAC,CAAC,GAAGF,OAAO,GAAGE,CAAC;MACnCD,MAAM,CAACE,IAAI,CAACb,QAAQ,CAACY,CAAC,CAAC,CAAC;IAC1B;IAEA,OAAOD,MAAM;EACf;EAEA,IAAIG,QAAQ,GAAGJ,OAAO,GAAG,CAAC;EAC1B,IAAIK,cAAc,GAAG,EAAE;EAEvB,KAAK,IAAIC,GAAG,IAAIf,UAAU,EAAE;IAC1B,IAAIA,UAAU,CAACgB,cAAc,CAACD,GAAG,CAAC,EAAE;MAClCD,cAAc,CAACF,IAAI,CAACG,GAAG,CAAC;IAC1B;EACF;EAEA,IAAIE,MAAM,GAAGH,cAAc,CAACI,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC/C,OAAOpB,UAAU,CAACmB,CAAC,CAAC,GAAGnB,UAAU,CAACoB,CAAC,CAAC;EACtC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACZ,IAAIC,MAAM,GAAGL,MAAM,CAACM,KAAK,CAAC,CAAC,EAAEV,QAAQ,CAAC;EACtC,IAAIW,IAAI,GAAG1B,KAAK,CAACM,GAAG,CAAC,MAAM,CAAC;EAE5B,IAAIoB,IAAI,IAAIF,MAAM,CAACG,OAAO,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;IACtCF,MAAM,CAACI,GAAG,CAAC,CAAC;IACZJ,MAAM,CAACV,IAAI,CAACY,IAAI,CAAC;EACnB;EAEA,OAAOF,MAAM;AACf;AAEA,eAAe;EACbjB,GAAG,EAAEA,GAAG;EACRD,GAAG,EAAEA;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}