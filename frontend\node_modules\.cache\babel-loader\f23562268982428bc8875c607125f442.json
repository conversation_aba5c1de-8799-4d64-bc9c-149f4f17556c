{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(\"path\", {\n  d: \"M20 19.59V8l-6-6H4v20l15.57-.02-4.81-4.81c-.8.52-1.74.83-2.76.83-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5c0 1.02-.31 1.96-.83 2.75L20 19.59zM9 13c0 1.66 1.34 3 3 3s3-1.34 3-3-1.34-3-3-3-3 1.34-3 3z\"\n}), 'FindInPageSharp');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "d"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/FindInPageSharp.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M20 19.59V8l-6-6H4v20l15.57-.02-4.81-4.81c-.8.52-1.74.83-2.76.83-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5c0 1.02-.31 1.96-.83 2.75L20 19.59zM9 13c0 1.66 1.34 3 3 3s3-1.34 3-3-1.34-3-3-3-3 1.34-3 3z\"\n}), 'FindInPageSharp');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EACrEC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}