import React, { useState, useEffect } from "react";
import Routes from "./routes";
import "react-toastify/dist/ReactToastify.css";
import "./styles/global.css";

import { createTheme, ThemeProvider } from "@material-ui/core/styles";
import { ptBR } from "@material-ui/core/locale";

const App = () => {
	const [locale, setLocale] = useState();

  const theme = createTheme(
    {
      scrollbarStyles: {
        '&::-webkit-scrollbar': {
          width: '6px',
          height: '6px',
        },
        '&::-webkit-scrollbar-track': {
          background: '#f1f1f1',
          borderRadius: '10px',
        },
        '&::-webkit-scrollbar-thumb': {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '10px',
          border: '1px solid #f1f1f1',
        },
        '&::-webkit-scrollbar-thumb:hover': {
          background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
        },
      },
      palette: {
        primary: {
          main: '#667eea',
          light: '#8fa4f3',
          dark: '#4c63d2',
          contrastText: '#ffffff'
        },
        secondary: {
          main: '#764ba2',
          light: '#9575cd',
          dark: '#512da8',
          contrastText: '#ffffff'
        },
        background: {
          default: '#f8fafc',
          paper: '#ffffff'
        },
        text: {
          primary: '#2d3748',
          secondary: '#4a5568'
        },
        success: {
          main: '#48bb78',
          light: '#68d391',
          dark: '#38a169'
        },
        warning: {
          main: '#ed8936',
          light: '#f6ad55',
          dark: '#dd6b20'
        },
        error: {
          main: '#f56565',
          light: '#fc8181',
          dark: '#e53e3e'
        },
        info: {
          main: '#4299e1',
          light: '#63b3ed',
          dark: '#3182ce'
        }
      },
      typography: {
        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
        h1: {
          fontWeight: 700,
          fontSize: '2.5rem',
          lineHeight: 1.2,
        },
        h2: {
          fontWeight: 600,
          fontSize: '2rem',
          lineHeight: 1.3,
        },
        h3: {
          fontWeight: 600,
          fontSize: '1.75rem',
          lineHeight: 1.3,
        },
        h4: {
          fontWeight: 600,
          fontSize: '1.5rem',
          lineHeight: 1.4,
        },
        h5: {
          fontWeight: 600,
          fontSize: '1.25rem',
          lineHeight: 1.4,
        },
        h6: {
          fontWeight: 600,
          fontSize: '1.125rem',
          lineHeight: 1.4,
        },
        body1: {
          fontSize: '1rem',
          lineHeight: 1.6,
        },
        body2: {
          fontSize: '0.875rem',
          lineHeight: 1.6,
        }
      },
      shape: {
        borderRadius: 12,
      },
      shadows: [
        'none',
        '0px 2px 4px rgba(0, 0, 0, 0.05)',
        '0px 4px 8px rgba(0, 0, 0, 0.08)',
        '0px 8px 16px rgba(0, 0, 0, 0.1)',
        '0px 12px 24px rgba(0, 0, 0, 0.12)',
        '0px 16px 32px rgba(0, 0, 0, 0.15)',
        '0px 20px 40px rgba(0, 0, 0, 0.18)',
        '0px 24px 48px rgba(0, 0, 0, 0.2)',
        '0px 28px 56px rgba(0, 0, 0, 0.22)',
        '0px 32px 64px rgba(0, 0, 0, 0.24)',
        '0px 36px 72px rgba(0, 0, 0, 0.26)',
        '0px 40px 80px rgba(0, 0, 0, 0.28)',
        '0px 44px 88px rgba(0, 0, 0, 0.3)',
        '0px 48px 96px rgba(0, 0, 0, 0.32)',
        '0px 52px 104px rgba(0, 0, 0, 0.34)',
        '0px 56px 112px rgba(0, 0, 0, 0.36)',
        '0px 60px 120px rgba(0, 0, 0, 0.38)',
        '0px 64px 128px rgba(0, 0, 0, 0.4)',
        '0px 68px 136px rgba(0, 0, 0, 0.42)',
        '0px 72px 144px rgba(0, 0, 0, 0.44)',
        '0px 76px 152px rgba(0, 0, 0, 0.46)',
        '0px 80px 160px rgba(0, 0, 0, 0.48)',
        '0px 84px 168px rgba(0, 0, 0, 0.5)',
        '0px 88px 176px rgba(0, 0, 0, 0.52)',
        '0px 92px 184px rgba(0, 0, 0, 0.54)'
      ],
      overrides: {
        MuiButton: {
          root: {
            textTransform: 'none',
            fontWeight: 600,
            borderRadius: 8,
            padding: '10px 24px',
            fontSize: '0.875rem',
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              transform: 'translateY(-1px)',
              boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.15)',
            }
          },
          contained: {
            boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
            '&:hover': {
              boxShadow: '0px 4px 16px rgba(0, 0, 0, 0.2)',
            }
          }
        },
        MuiPaper: {
          root: {
            borderRadius: 12,
          },
          elevation1: {
            boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.08)',
          },
          elevation2: {
            boxShadow: '0px 4px 16px rgba(0, 0, 0, 0.12)',
          },
          elevation3: {
            boxShadow: '0px 8px 24px rgba(0, 0, 0, 0.15)',
          }
        },
        MuiCard: {
          root: {
            borderRadius: 16,
            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.08)',
            transition: 'all 0.3s ease-in-out',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.15)',
            }
          }
        },
        MuiTextField: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 8,
              '& fieldset': {
                borderColor: '#e2e8f0',
              },
              '&:hover fieldset': {
                borderColor: '#cbd5e0',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#667eea',
                borderWidth: 2,
              }
            }
          }
        },
        MuiAppBar: {
          root: {
            boxShadow: '0px 2px 12px rgba(0, 0, 0, 0.08)',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          }
        },
        MuiDrawer: {
          paper: {
            borderRight: 'none',
            boxShadow: '2px 0px 12px rgba(0, 0, 0, 0.08)',
          }
        }
      }
    },
    locale
  );

	useEffect(() => {
		const i18nlocale = localStorage.getItem("i18nextLng");
		const browserLocale =
			i18nlocale.substring(0, 2) + i18nlocale.substring(3, 5);

		if (browserLocale === "ptBR") {
			setLocale(ptBR);
		}
	}, []);

	return (
		<ThemeProvider theme={theme}>
			<Routes />
		</ThemeProvider>
	);
};

export default App;
