{"ast": null, "code": "import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport map from 'lodash-es/map';\nimport merge from 'lodash-es/merge';\nimport * as color from '../../helpers/color';\nimport { ColorWrap, EditableInput, Swatch } from '../common';\nexport var Twitter = function Twitter(_ref) {\n  var onChange = _ref.onChange,\n    onSwatchHover = _ref.onSwatchHover,\n    hex = _ref.hex,\n    colors = _ref.colors,\n    width = _ref.width,\n    triangle = _ref.triangle,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var styles = reactCSS(merge({\n    'default': {\n      card: {\n        width: width,\n        background: '#fff',\n        border: '0 solid rgba(0,0,0,0.25)',\n        boxShadow: '0 1px 4px rgba(0,0,0,0.25)',\n        borderRadius: '4px',\n        position: 'relative'\n      },\n      body: {\n        padding: '15px 9px 9px 15px'\n      },\n      label: {\n        fontSize: '18px',\n        color: '#fff'\n      },\n      triangle: {\n        width: '0px',\n        height: '0px',\n        borderStyle: 'solid',\n        borderWidth: '0 9px 10px 9px',\n        borderColor: 'transparent transparent #fff transparent',\n        position: 'absolute'\n      },\n      triangleShadow: {\n        width: '0px',\n        height: '0px',\n        borderStyle: 'solid',\n        borderWidth: '0 9px 10px 9px',\n        borderColor: 'transparent transparent rgba(0,0,0,.1) transparent',\n        position: 'absolute'\n      },\n      hash: {\n        background: '#F0F0F0',\n        height: '30px',\n        width: '30px',\n        borderRadius: '4px 0 0 4px',\n        float: 'left',\n        color: '#98A1A4',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      input: {\n        width: '100px',\n        fontSize: '14px',\n        color: '#666',\n        border: '0px',\n        outline: 'none',\n        height: '28px',\n        boxShadow: 'inset 0 0 0 1px #F0F0F0',\n        boxSizing: 'content-box',\n        borderRadius: '0 4px 4px 0',\n        float: 'left',\n        paddingLeft: '8px'\n      },\n      swatch: {\n        width: '30px',\n        height: '30px',\n        float: 'left',\n        borderRadius: '4px',\n        margin: '0 6px 6px 0'\n      },\n      clear: {\n        clear: 'both'\n      }\n    },\n    'hide-triangle': {\n      triangle: {\n        display: 'none'\n      },\n      triangleShadow: {\n        display: 'none'\n      }\n    },\n    'top-left-triangle': {\n      triangle: {\n        top: '-10px',\n        left: '12px'\n      },\n      triangleShadow: {\n        top: '-11px',\n        left: '12px'\n      }\n    },\n    'top-right-triangle': {\n      triangle: {\n        top: '-10px',\n        right: '12px'\n      },\n      triangleShadow: {\n        top: '-11px',\n        right: '12px'\n      }\n    }\n  }, passedStyles), {\n    'hide-triangle': triangle === 'hide',\n    'top-left-triangle': triangle === 'top-left',\n    'top-right-triangle': triangle === 'top-right'\n  });\n  var handleChange = function handleChange(hexcode, e) {\n    color.isValidHex(hexcode) && onChange({\n      hex: hexcode,\n      source: 'hex'\n    }, e);\n  };\n  return React.createElement('div', {\n    style: styles.card,\n    className: 'twitter-picker ' + className\n  }, React.createElement('div', {\n    style: styles.triangleShadow\n  }), React.createElement('div', {\n    style: styles.triangle\n  }), React.createElement('div', {\n    style: styles.body\n  }, map(colors, function (c, i) {\n    return React.createElement(Swatch, {\n      key: i,\n      color: c,\n      hex: c,\n      style: styles.swatch,\n      onClick: handleChange,\n      onHover: onSwatchHover,\n      focusStyle: {\n        boxShadow: '0 0 4px ' + c\n      }\n    });\n  }), React.createElement('div', {\n    style: styles.hash\n  }, '#'), React.createElement(EditableInput, {\n    label: null,\n    style: {\n      input: styles.input\n    },\n    value: hex.replace('#', ''),\n    onChange: handleChange\n  }), React.createElement('div', {\n    style: styles.clear\n  })));\n};\nTwitter.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  triangle: PropTypes.oneOf(['hide', 'top-left', 'top-right']),\n  colors: PropTypes.arrayOf(PropTypes.string),\n  styles: PropTypes.object\n};\nTwitter.defaultProps = {\n  width: 276,\n  colors: ['#FF6900', '#FCB900', '#7BDCB5', '#00D084', '#8ED1FC', '#0693E3', '#ABB8C3', '#EB144C', '#F78DA7', '#9900EF'],\n  triangle: 'top-left',\n  styles: {}\n};\nexport default ColorWrap(Twitter);", "map": {"version": 3, "names": ["React", "PropTypes", "reactCSS", "map", "merge", "color", "ColorWrap", "EditableInput", "Swatch", "Twitter", "_ref", "onChange", "onSwatchHover", "hex", "colors", "width", "triangle", "_ref$styles", "styles", "passedStyles", "undefined", "_ref$className", "className", "card", "background", "border", "boxShadow", "borderRadius", "position", "body", "padding", "label", "fontSize", "height", "borderStyle", "borderWidth", "borderColor", "triangleShadow", "hash", "float", "display", "alignItems", "justifyContent", "input", "outline", "boxSizing", "paddingLeft", "swatch", "margin", "clear", "top", "left", "right", "handleChange", "hexcode", "e", "isValidHex", "source", "createElement", "style", "c", "i", "key", "onClick", "onHover", "focusStyle", "value", "replace", "propTypes", "oneOfType", "string", "number", "oneOf", "arrayOf", "object", "defaultProps"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/twitter/Twitter.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport map from 'lodash-es/map';\nimport merge from 'lodash-es/merge';\nimport * as color from '../../helpers/color';\n\nimport { ColorWrap, EditableInput, Swatch } from '../common';\n\nexport var Twitter = function Twitter(_ref) {\n  var onChange = _ref.onChange,\n      onSwatchHover = _ref.onSwatchHover,\n      hex = _ref.hex,\n      colors = _ref.colors,\n      width = _ref.width,\n      triangle = _ref.triangle,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var styles = reactCSS(merge({\n    'default': {\n      card: {\n        width: width,\n        background: '#fff',\n        border: '0 solid rgba(0,0,0,0.25)',\n        boxShadow: '0 1px 4px rgba(0,0,0,0.25)',\n        borderRadius: '4px',\n        position: 'relative'\n      },\n      body: {\n        padding: '15px 9px 9px 15px'\n      },\n      label: {\n        fontSize: '18px',\n        color: '#fff'\n      },\n      triangle: {\n        width: '0px',\n        height: '0px',\n        borderStyle: 'solid',\n        borderWidth: '0 9px 10px 9px',\n        borderColor: 'transparent transparent #fff transparent',\n        position: 'absolute'\n      },\n      triangleShadow: {\n        width: '0px',\n        height: '0px',\n        borderStyle: 'solid',\n        borderWidth: '0 9px 10px 9px',\n        borderColor: 'transparent transparent rgba(0,0,0,.1) transparent',\n        position: 'absolute'\n      },\n      hash: {\n        background: '#F0F0F0',\n        height: '30px',\n        width: '30px',\n        borderRadius: '4px 0 0 4px',\n        float: 'left',\n        color: '#98A1A4',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      input: {\n        width: '100px',\n        fontSize: '14px',\n        color: '#666',\n        border: '0px',\n        outline: 'none',\n        height: '28px',\n        boxShadow: 'inset 0 0 0 1px #F0F0F0',\n        boxSizing: 'content-box',\n        borderRadius: '0 4px 4px 0',\n        float: 'left',\n        paddingLeft: '8px'\n      },\n      swatch: {\n        width: '30px',\n        height: '30px',\n        float: 'left',\n        borderRadius: '4px',\n        margin: '0 6px 6px 0'\n      },\n      clear: {\n        clear: 'both'\n      }\n    },\n    'hide-triangle': {\n      triangle: {\n        display: 'none'\n      },\n      triangleShadow: {\n        display: 'none'\n      }\n    },\n    'top-left-triangle': {\n      triangle: {\n        top: '-10px',\n        left: '12px'\n      },\n      triangleShadow: {\n        top: '-11px',\n        left: '12px'\n      }\n    },\n    'top-right-triangle': {\n      triangle: {\n        top: '-10px',\n        right: '12px'\n      },\n      triangleShadow: {\n        top: '-11px',\n        right: '12px'\n      }\n    }\n  }, passedStyles), {\n    'hide-triangle': triangle === 'hide',\n    'top-left-triangle': triangle === 'top-left',\n    'top-right-triangle': triangle === 'top-right'\n  });\n\n  var handleChange = function handleChange(hexcode, e) {\n    color.isValidHex(hexcode) && onChange({\n      hex: hexcode,\n      source: 'hex'\n    }, e);\n  };\n\n  return React.createElement(\n    'div',\n    { style: styles.card, className: 'twitter-picker ' + className },\n    React.createElement('div', { style: styles.triangleShadow }),\n    React.createElement('div', { style: styles.triangle }),\n    React.createElement(\n      'div',\n      { style: styles.body },\n      map(colors, function (c, i) {\n        return React.createElement(Swatch, {\n          key: i,\n          color: c,\n          hex: c,\n          style: styles.swatch,\n          onClick: handleChange,\n          onHover: onSwatchHover,\n          focusStyle: {\n            boxShadow: '0 0 4px ' + c\n          }\n        });\n      }),\n      React.createElement(\n        'div',\n        { style: styles.hash },\n        '#'\n      ),\n      React.createElement(EditableInput, {\n        label: null,\n        style: { input: styles.input },\n        value: hex.replace('#', ''),\n        onChange: handleChange\n      }),\n      React.createElement('div', { style: styles.clear })\n    )\n  );\n};\n\nTwitter.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  triangle: PropTypes.oneOf(['hide', 'top-left', 'top-right']),\n  colors: PropTypes.arrayOf(PropTypes.string),\n  styles: PropTypes.object\n};\n\nTwitter.defaultProps = {\n  width: 276,\n  colors: ['#FF6900', '#FCB900', '#7BDCB5', '#00D084', '#8ED1FC', '#0693E3', '#ABB8C3', '#EB144C', '#F78DA7', '#9900EF'],\n  triangle: 'top-left',\n  styles: {}\n};\n\nexport default ColorWrap(Twitter);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,GAAG,MAAM,eAAe;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAO,KAAKC,KAAK,MAAM,qBAAqB;AAE5C,SAASC,SAAS,EAAEC,aAAa,EAAEC,MAAM,QAAQ,WAAW;AAE5D,OAAO,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;EAC1C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,aAAa,GAAGF,IAAI,CAACE,aAAa;IAClCC,GAAG,GAAGH,IAAI,CAACG,GAAG;IACdC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,KAAK,GAAGL,IAAI,CAACK,KAAK;IAClBC,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IACxBC,WAAW,GAAGP,IAAI,CAACQ,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,cAAc,GAAGX,IAAI,CAACY,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKD,SAAS,GAAG,EAAE,GAAGC,cAAc;EAElE,IAAIH,MAAM,GAAGhB,QAAQ,CAACE,KAAK,CAAC;IAC1B,SAAS,EAAE;MACTmB,IAAI,EAAE;QACJR,KAAK,EAAEA,KAAK;QACZS,UAAU,EAAE,MAAM;QAClBC,MAAM,EAAE,0BAA0B;QAClCC,SAAS,EAAE,4BAA4B;QACvCC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDC,IAAI,EAAE;QACJC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,QAAQ,EAAE,MAAM;QAChB3B,KAAK,EAAE;MACT,CAAC;MACDW,QAAQ,EAAE;QACRD,KAAK,EAAE,KAAK;QACZkB,MAAM,EAAE,KAAK;QACbC,WAAW,EAAE,OAAO;QACpBC,WAAW,EAAE,gBAAgB;QAC7BC,WAAW,EAAE,0CAA0C;QACvDR,QAAQ,EAAE;MACZ,CAAC;MACDS,cAAc,EAAE;QACdtB,KAAK,EAAE,KAAK;QACZkB,MAAM,EAAE,KAAK;QACbC,WAAW,EAAE,OAAO;QACpBC,WAAW,EAAE,gBAAgB;QAC7BC,WAAW,EAAE,oDAAoD;QACjER,QAAQ,EAAE;MACZ,CAAC;MACDU,IAAI,EAAE;QACJd,UAAU,EAAE,SAAS;QACrBS,MAAM,EAAE,MAAM;QACdlB,KAAK,EAAE,MAAM;QACbY,YAAY,EAAE,aAAa;QAC3BY,KAAK,EAAE,MAAM;QACblC,KAAK,EAAE,SAAS;QAChBmC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE;MAClB,CAAC;MACDC,KAAK,EAAE;QACL5B,KAAK,EAAE,OAAO;QACdiB,QAAQ,EAAE,MAAM;QAChB3B,KAAK,EAAE,MAAM;QACboB,MAAM,EAAE,KAAK;QACbmB,OAAO,EAAE,MAAM;QACfX,MAAM,EAAE,MAAM;QACdP,SAAS,EAAE,yBAAyB;QACpCmB,SAAS,EAAE,aAAa;QACxBlB,YAAY,EAAE,aAAa;QAC3BY,KAAK,EAAE,MAAM;QACbO,WAAW,EAAE;MACf,CAAC;MACDC,MAAM,EAAE;QACNhC,KAAK,EAAE,MAAM;QACbkB,MAAM,EAAE,MAAM;QACdM,KAAK,EAAE,MAAM;QACbZ,YAAY,EAAE,KAAK;QACnBqB,MAAM,EAAE;MACV,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE;MACT;IACF,CAAC;IACD,eAAe,EAAE;MACfjC,QAAQ,EAAE;QACRwB,OAAO,EAAE;MACX,CAAC;MACDH,cAAc,EAAE;QACdG,OAAO,EAAE;MACX;IACF,CAAC;IACD,mBAAmB,EAAE;MACnBxB,QAAQ,EAAE;QACRkC,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE;MACR,CAAC;MACDd,cAAc,EAAE;QACda,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE;MACR;IACF,CAAC;IACD,oBAAoB,EAAE;MACpBnC,QAAQ,EAAE;QACRkC,GAAG,EAAE,OAAO;QACZE,KAAK,EAAE;MACT,CAAC;MACDf,cAAc,EAAE;QACda,GAAG,EAAE,OAAO;QACZE,KAAK,EAAE;MACT;IACF;EACF,CAAC,EAAEjC,YAAY,CAAC,EAAE;IAChB,eAAe,EAAEH,QAAQ,KAAK,MAAM;IACpC,mBAAmB,EAAEA,QAAQ,KAAK,UAAU;IAC5C,oBAAoB,EAAEA,QAAQ,KAAK;EACrC,CAAC,CAAC;EAEF,IAAIqC,YAAY,GAAG,SAASA,YAAYA,CAACC,OAAO,EAAEC,CAAC,EAAE;IACnDlD,KAAK,CAACmD,UAAU,CAACF,OAAO,CAAC,IAAI3C,QAAQ,CAAC;MACpCE,GAAG,EAAEyC,OAAO;MACZG,MAAM,EAAE;IACV,CAAC,EAAEF,CAAC,CAAC;EACP,CAAC;EAED,OAAOvD,KAAK,CAAC0D,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAEzC,MAAM,CAACK,IAAI;IAAED,SAAS,EAAE,iBAAiB,GAAGA;EAAU,CAAC,EAChEtB,KAAK,CAAC0D,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEzC,MAAM,CAACmB;EAAe,CAAC,CAAC,EAC5DrC,KAAK,CAAC0D,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEzC,MAAM,CAACF;EAAS,CAAC,CAAC,EACtDhB,KAAK,CAAC0D,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEzC,MAAM,CAACW;EAAK,CAAC,EACtB1B,GAAG,CAACW,MAAM,EAAE,UAAU8C,CAAC,EAAEC,CAAC,EAAE;IAC1B,OAAO7D,KAAK,CAAC0D,aAAa,CAAClD,MAAM,EAAE;MACjCsD,GAAG,EAAED,CAAC;MACNxD,KAAK,EAAEuD,CAAC;MACR/C,GAAG,EAAE+C,CAAC;MACND,KAAK,EAAEzC,MAAM,CAAC6B,MAAM;MACpBgB,OAAO,EAAEV,YAAY;MACrBW,OAAO,EAAEpD,aAAa;MACtBqD,UAAU,EAAE;QACVvC,SAAS,EAAE,UAAU,GAAGkC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,EACF5D,KAAK,CAAC0D,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEzC,MAAM,CAACoB;EAAK,CAAC,EACtB,GACF,CAAC,EACDtC,KAAK,CAAC0D,aAAa,CAACnD,aAAa,EAAE;IACjCwB,KAAK,EAAE,IAAI;IACX4B,KAAK,EAAE;MAAEhB,KAAK,EAAEzB,MAAM,CAACyB;IAAM,CAAC;IAC9BuB,KAAK,EAAErD,GAAG,CAACsD,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IAC3BxD,QAAQ,EAAE0C;EACZ,CAAC,CAAC,EACFrD,KAAK,CAAC0D,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEzC,MAAM,CAAC+B;EAAM,CAAC,CACpD,CACF,CAAC;AACH,CAAC;AAEDxC,OAAO,CAAC2D,SAAS,GAAG;EAClBrD,KAAK,EAAEd,SAAS,CAACoE,SAAS,CAAC,CAACpE,SAAS,CAACqE,MAAM,EAAErE,SAAS,CAACsE,MAAM,CAAC,CAAC;EAChEvD,QAAQ,EAAEf,SAAS,CAACuE,KAAK,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;EAC5D1D,MAAM,EAAEb,SAAS,CAACwE,OAAO,CAACxE,SAAS,CAACqE,MAAM,CAAC;EAC3CpD,MAAM,EAAEjB,SAAS,CAACyE;AACpB,CAAC;AAEDjE,OAAO,CAACkE,YAAY,GAAG;EACrB5D,KAAK,EAAE,GAAG;EACVD,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACtHE,QAAQ,EAAE,UAAU;EACpBE,MAAM,EAAE,CAAC;AACX,CAAC;AAED,eAAeZ,SAAS,CAACG,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}