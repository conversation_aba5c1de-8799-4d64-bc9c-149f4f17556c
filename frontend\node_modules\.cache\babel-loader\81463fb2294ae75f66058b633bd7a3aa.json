{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\pages\\\\Login\\\\index.js\";\nimport React, { useState, useContext } from \"react\";\nimport { Link as RouterLink } from \"react-router-dom\";\nimport CssBaseline from \"@material-ui/core/CssBaseline\";\nimport TextField from \"@material-ui/core/TextField\";\nimport Link from \"@material-ui/core/Link\";\nimport Typography from \"@material-ui/core/Typography\";\nimport { makeStyles } from \"@material-ui/core/styles\";\nimport Container from \"@material-ui/core/Container\";\nimport Paper from \"@material-ui/core/Paper\";\nimport { LockOutlined, Email, Visibility, VisibilityOff } from \"@material-ui/icons\";\nimport { IconButton, InputAdornment } from \"@material-ui/core\";\nimport { i18n } from \"../../translate/i18n\";\nimport { AuthContext } from \"../../context/Auth/AuthContext\";\nimport ModernButton from \"../../components/ModernButton\";\nimport logo from \"../../assets/logologin.png\";\n\n// const Copyright = () => {\n// \treturn (\n// \t\t<Typography variant=\"body2\" color=\"textSecondary\" align=\"center\">\n// \t\t\t{\"Copyleft \"}\n// \t\t\t<Link color=\"inherit\" href=\"https://github.com/canove\">\n// \t\t\t\tCanove\n// \t\t\t</Link>{\" \"}\n// \t\t\t{new Date().getFullYear()}\n// \t\t\t{\".\"}\n// \t\t</Typography>\n// \t);\n// };\n\nconst useStyles = makeStyles(theme => ({\n  root: {\n    width: \"100vw\",\n    height: \"100vh\",\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    position: 'relative',\n    overflow: 'hidden',\n    '&::before': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: 'url(\"data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")'\n    }\n  },\n  container: {\n    position: 'relative',\n    zIndex: 1\n  },\n  paper: {\n    backgroundColor: \"white\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    padding: theme.spacing(6, 4),\n    borderRadius: 24,\n    boxShadow: \"0 20px 60px rgba(0, 0, 0, 0.15)\",\n    backdropFilter: 'blur(10px)',\n    border: '1px solid rgba(255, 255, 255, 0.2)',\n    maxWidth: 400,\n    width: '100%'\n  },\n  logoContainer: {\n    marginBottom: theme.spacing(3),\n    padding: theme.spacing(2),\n    borderRadius: 16,\n    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n  },\n  logo: {\n    height: 60,\n    width: 'auto',\n    maxWidth: '100%'\n  },\n  title: {\n    fontSize: '1.75rem',\n    fontWeight: 700,\n    color: theme.palette.text.primary,\n    marginBottom: theme.spacing(1),\n    textAlign: 'center'\n  },\n  subtitle: {\n    fontSize: '0.875rem',\n    color: theme.palette.text.secondary,\n    marginBottom: theme.spacing(4),\n    textAlign: 'center'\n  },\n  form: {\n    width: \"100%\",\n    marginTop: theme.spacing(1)\n  },\n  textField: {\n    marginBottom: theme.spacing(2),\n    '& .MuiOutlinedInput-root': {\n      borderRadius: 12,\n      backgroundColor: 'rgba(102, 126, 234, 0.02)',\n      '& fieldset': {\n        borderColor: 'rgba(102, 126, 234, 0.2)'\n      },\n      '&:hover fieldset': {\n        borderColor: 'rgba(102, 126, 234, 0.4)'\n      },\n      '&.Mui-focused fieldset': {\n        borderColor: theme.palette.primary.main,\n        borderWidth: 2\n      }\n    },\n    '& .MuiInputLabel-root': {\n      color: theme.palette.text.secondary\n    }\n  },\n  submitButton: {\n    marginTop: theme.spacing(3),\n    marginBottom: theme.spacing(2),\n    height: 48,\n    borderRadius: 12\n  },\n  linkContainer: {\n    textAlign: 'center',\n    marginTop: theme.spacing(2)\n  },\n  link: {\n    color: theme.palette.primary.main,\n    textDecoration: 'none',\n    fontSize: '0.875rem',\n    fontWeight: 500,\n    '&:hover': {\n      textDecoration: 'underline'\n    }\n  },\n  copyright: {\n    marginTop: theme.spacing(4),\n    color: 'rgba(255, 255, 255, 0.8)',\n    fontSize: '0.875rem',\n    textAlign: 'center'\n  },\n  iconAdornment: {\n    color: theme.palette.text.secondary\n  }\n}));\nconst Login = () => {\n  const classes = useStyles();\n  const [user, setUser] = useState({\n    email: \"\",\n    password: \"\"\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const {\n    handleLogin\n  } = useContext(AuthContext);\n  const handleChangeInput = e => {\n    setUser({\n      ...user,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handlSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      await handleLogin(user);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleTogglePassword = () => {\n    setShowPassword(!showPassword);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.root,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(CssBaseline, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(Container, {\n    component: \"main\",\n    maxWidth: \"sm\",\n    className: classes.container,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(Paper, {\n    className: classes.paper,\n    elevation: 0,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.logoContainer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    className: classes.logo,\n    src: logo,\n    alt: \"WhatTicket Logo\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 13\n    }\n  })), /*#__PURE__*/React.createElement(Typography, {\n    className: classes.title,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 11\n    }\n  }, \"Bem-vindo de volta\"), /*#__PURE__*/React.createElement(Typography, {\n    className: classes.subtitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 11\n    }\n  }, \"Fa\\xE7a login para acessar sua conta\"), /*#__PURE__*/React.createElement(\"form\", {\n    className: classes.form,\n    noValidate: true,\n    onSubmit: handlSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(TextField, {\n    variant: \"outlined\",\n    required: true,\n    fullWidth: true,\n    id: \"email\",\n    label: i18n.t(\"login.form.email\"),\n    name: \"email\",\n    value: user.email,\n    onChange: handleChangeInput,\n    autoComplete: \"email\",\n    autoFocus: true,\n    className: classes.textField,\n    InputProps: {\n      startAdornment: /*#__PURE__*/React.createElement(InputAdornment, {\n        position: \"start\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 19\n        }\n      }, /*#__PURE__*/React.createElement(Email, {\n        className: classes.iconAdornment,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 21\n        }\n      }))\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(TextField, {\n    variant: \"outlined\",\n    required: true,\n    fullWidth: true,\n    name: \"password\",\n    label: i18n.t(\"login.form.password\"),\n    type: showPassword ? \"text\" : \"password\",\n    id: \"password\",\n    value: user.password,\n    onChange: handleChangeInput,\n    autoComplete: \"current-password\",\n    className: classes.textField,\n    InputProps: {\n      startAdornment: /*#__PURE__*/React.createElement(InputAdornment, {\n        position: \"start\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 19\n        }\n      }, /*#__PURE__*/React.createElement(LockOutlined, {\n        className: classes.iconAdornment,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 21\n        }\n      })),\n      endAdornment: /*#__PURE__*/React.createElement(InputAdornment, {\n        position: \"end\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 19\n        }\n      }, /*#__PURE__*/React.createElement(IconButton, {\n        onClick: handleTogglePassword,\n        edge: \"end\",\n        size: \"small\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 21\n        }\n      }, showPassword ? /*#__PURE__*/React.createElement(VisibilityOff, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 39\n        }\n      }) : /*#__PURE__*/React.createElement(Visibility, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 59\n        }\n      })))\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(ModernButton, {\n    type: \"submit\",\n    fullWidth: true,\n    variant: \"primary\",\n    className: classes.submitButton,\n    loading: loading,\n    disabled: !user.email || !user.password,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 13\n    }\n  }, loading ? \"Entrando...\" : \"Entrar\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.linkContainer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(Link, {\n    component: RouterLink,\n    to: \"/signup\",\n    className: classes.link,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 15\n    }\n  }, \"Ainda n\\xE3o tem uma conta? Registre-se\")))), /*#__PURE__*/React.createElement(Typography, {\n    className: classes.copyright,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 9\n    }\n  }, \"\\xA9 2024 WhatTicket - Sistema de Atendimento\")));\n};\nexport default Login;", "map": {"version": 3, "names": ["React", "useState", "useContext", "Link", "RouterLink", "CssBaseline", "TextField", "Typography", "makeStyles", "Container", "Paper", "LockOutlined", "Email", "Visibility", "VisibilityOff", "IconButton", "InputAdornment", "i18n", "AuthContext", "ModernButton", "logo", "useStyles", "theme", "root", "width", "height", "background", "display", "alignItems", "justifyContent", "position", "overflow", "content", "top", "left", "right", "bottom", "container", "zIndex", "paper", "backgroundColor", "flexDirection", "padding", "spacing", "borderRadius", "boxShadow", "<PERSON><PERSON>ilter", "border", "max<PERSON><PERSON><PERSON>", "logoContainer", "marginBottom", "title", "fontSize", "fontWeight", "color", "palette", "text", "primary", "textAlign", "subtitle", "secondary", "form", "marginTop", "textField", "borderColor", "main", "borderWidth", "submitButton", "linkContainer", "link", "textDecoration", "copyright", "iconAdornment", "<PERSON><PERSON>", "classes", "user", "setUser", "email", "password", "showPassword", "setShowPassword", "loading", "setLoading", "handleLogin", "handleChangeInput", "e", "target", "name", "value", "handlSubmit", "preventDefault", "handleTogglePassword", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "elevation", "src", "alt", "noValidate", "onSubmit", "variant", "required", "fullWidth", "id", "label", "t", "onChange", "autoComplete", "autoFocus", "InputProps", "startAdornment", "type", "endAdornment", "onClick", "edge", "size", "disabled", "to"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/pages/Login/index.js"], "sourcesContent": ["import React, { useState, useContext } from \"react\";\r\nimport { Link as RouterLink } from \"react-router-dom\";\r\n\r\nimport CssBaseline from \"@material-ui/core/CssBaseline\";\r\nimport TextField from \"@material-ui/core/TextField\";\r\nimport Link from \"@material-ui/core/Link\";\r\nimport Typography from \"@material-ui/core/Typography\";\r\nimport { makeStyles } from \"@material-ui/core/styles\";\r\nimport Container from \"@material-ui/core/Container\";\r\nimport Paper from \"@material-ui/core/Paper\";\r\nimport { LockOutlined, Email, Visibility, VisibilityOff } from \"@material-ui/icons\";\r\nimport { IconButton, InputAdornment } from \"@material-ui/core\";\r\n\r\nimport { i18n } from \"../../translate/i18n\";\r\nimport { AuthContext } from \"../../context/Auth/AuthContext\";\r\nimport ModernButton from \"../../components/ModernButton\";\r\nimport logo from \"../../assets/logologin.png\";\r\n\r\n// const Copyright = () => {\r\n// \treturn (\r\n// \t\t<Typography variant=\"body2\" color=\"textSecondary\" align=\"center\">\r\n// \t\t\t{\"Copyleft \"}\r\n// \t\t\t<Link color=\"inherit\" href=\"https://github.com/canove\">\r\n// \t\t\t\tCanove\r\n// \t\t\t</Link>{\" \"}\r\n// \t\t\t{new Date().getFullYear()}\r\n// \t\t\t{\".\"}\r\n// \t\t</Typography>\r\n// \t);\r\n// };\r\n\r\nconst useStyles = makeStyles(theme => ({\r\n  root: {\r\n    width: \"100vw\",\r\n    height: \"100vh\",\r\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n    position: 'relative',\r\n    overflow: 'hidden',\r\n    '&::before': {\r\n      content: '\"\"',\r\n      position: 'absolute',\r\n      top: 0,\r\n      left: 0,\r\n      right: 0,\r\n      bottom: 0,\r\n      background: 'url(\"data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")',\r\n    }\r\n  },\r\n  container: {\r\n    position: 'relative',\r\n    zIndex: 1,\r\n  },\r\n  paper: {\r\n    backgroundColor: \"white\",\r\n    display: \"flex\",\r\n    flexDirection: \"column\",\r\n    alignItems: \"center\",\r\n    padding: theme.spacing(6, 4),\r\n    borderRadius: 24,\r\n    boxShadow: \"0 20px 60px rgba(0, 0, 0, 0.15)\",\r\n    backdropFilter: 'blur(10px)',\r\n    border: '1px solid rgba(255, 255, 255, 0.2)',\r\n    maxWidth: 400,\r\n    width: '100%',\r\n  },\r\n  logoContainer: {\r\n    marginBottom: theme.spacing(3),\r\n    padding: theme.spacing(2),\r\n    borderRadius: 16,\r\n    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\r\n  },\r\n  logo: {\r\n    height: 60,\r\n    width: 'auto',\r\n    maxWidth: '100%',\r\n  },\r\n  title: {\r\n    fontSize: '1.75rem',\r\n    fontWeight: 700,\r\n    color: theme.palette.text.primary,\r\n    marginBottom: theme.spacing(1),\r\n    textAlign: 'center',\r\n  },\r\n  subtitle: {\r\n    fontSize: '0.875rem',\r\n    color: theme.palette.text.secondary,\r\n    marginBottom: theme.spacing(4),\r\n    textAlign: 'center',\r\n  },\r\n  form: {\r\n    width: \"100%\",\r\n    marginTop: theme.spacing(1)\r\n  },\r\n  textField: {\r\n    marginBottom: theme.spacing(2),\r\n    '& .MuiOutlinedInput-root': {\r\n      borderRadius: 12,\r\n      backgroundColor: 'rgba(102, 126, 234, 0.02)',\r\n      '& fieldset': {\r\n        borderColor: 'rgba(102, 126, 234, 0.2)',\r\n      },\r\n      '&:hover fieldset': {\r\n        borderColor: 'rgba(102, 126, 234, 0.4)',\r\n      },\r\n      '&.Mui-focused fieldset': {\r\n        borderColor: theme.palette.primary.main,\r\n        borderWidth: 2,\r\n      }\r\n    },\r\n    '& .MuiInputLabel-root': {\r\n      color: theme.palette.text.secondary,\r\n    }\r\n  },\r\n  submitButton: {\r\n    marginTop: theme.spacing(3),\r\n    marginBottom: theme.spacing(2),\r\n    height: 48,\r\n    borderRadius: 12,\r\n  },\r\n  linkContainer: {\r\n    textAlign: 'center',\r\n    marginTop: theme.spacing(2),\r\n  },\r\n  link: {\r\n    color: theme.palette.primary.main,\r\n    textDecoration: 'none',\r\n    fontSize: '0.875rem',\r\n    fontWeight: 500,\r\n    '&:hover': {\r\n      textDecoration: 'underline',\r\n    }\r\n  },\r\n  copyright: {\r\n    marginTop: theme.spacing(4),\r\n    color: 'rgba(255, 255, 255, 0.8)',\r\n    fontSize: '0.875rem',\r\n    textAlign: 'center',\r\n  },\r\n  iconAdornment: {\r\n    color: theme.palette.text.secondary,\r\n  }\r\n}));\r\n\r\nconst Login = () => {\r\n  const classes = useStyles();\r\n\r\n  const [user, setUser] = useState({ email: \"\", password: \"\" });\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const { handleLogin } = useContext(AuthContext);\r\n\r\n  const handleChangeInput = e => {\r\n    setUser({ ...user, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  const handlSubmit = async e => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    try {\r\n      await handleLogin(user);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleTogglePassword = () => {\r\n    setShowPassword(!showPassword);\r\n  };\r\n\r\n  return (\r\n    <div className={classes.root}>\r\n      <CssBaseline />\r\n      <Container component=\"main\" maxWidth=\"sm\" className={classes.container}>\r\n        <Paper className={classes.paper} elevation={0}>\r\n          {/* Logo */}\r\n          <div className={classes.logoContainer}>\r\n            <img\r\n              className={classes.logo}\r\n              src={logo}\r\n              alt=\"WhatTicket Logo\"\r\n            />\r\n          </div>\r\n\r\n          {/* Title */}\r\n          <Typography className={classes.title}>\r\n            Bem-vindo de volta\r\n          </Typography>\r\n          <Typography className={classes.subtitle}>\r\n            Faça login para acessar sua conta\r\n          </Typography>\r\n\r\n          {/* Form */}\r\n          <form className={classes.form} noValidate onSubmit={handlSubmit}>\r\n            <TextField\r\n              variant=\"outlined\"\r\n              required\r\n              fullWidth\r\n              id=\"email\"\r\n              label={i18n.t(\"login.form.email\")}\r\n              name=\"email\"\r\n              value={user.email}\r\n              onChange={handleChangeInput}\r\n              autoComplete=\"email\"\r\n              autoFocus\r\n              className={classes.textField}\r\n              InputProps={{\r\n                startAdornment: (\r\n                  <InputAdornment position=\"start\">\r\n                    <Email className={classes.iconAdornment} />\r\n                  </InputAdornment>\r\n                ),\r\n              }}\r\n            />\r\n\r\n            <TextField\r\n              variant=\"outlined\"\r\n              required\r\n              fullWidth\r\n              name=\"password\"\r\n              label={i18n.t(\"login.form.password\")}\r\n              type={showPassword ? \"text\" : \"password\"}\r\n              id=\"password\"\r\n              value={user.password}\r\n              onChange={handleChangeInput}\r\n              autoComplete=\"current-password\"\r\n              className={classes.textField}\r\n              InputProps={{\r\n                startAdornment: (\r\n                  <InputAdornment position=\"start\">\r\n                    <LockOutlined className={classes.iconAdornment} />\r\n                  </InputAdornment>\r\n                ),\r\n                endAdornment: (\r\n                  <InputAdornment position=\"end\">\r\n                    <IconButton\r\n                      onClick={handleTogglePassword}\r\n                      edge=\"end\"\r\n                      size=\"small\"\r\n                    >\r\n                      {showPassword ? <VisibilityOff /> : <Visibility />}\r\n                    </IconButton>\r\n                  </InputAdornment>\r\n                ),\r\n              }}\r\n            />\r\n\r\n            <ModernButton\r\n              type=\"submit\"\r\n              fullWidth\r\n              variant=\"primary\"\r\n              className={classes.submitButton}\r\n              loading={loading}\r\n              disabled={!user.email || !user.password}\r\n            >\r\n              {loading ? \"Entrando...\" : \"Entrar\"}\r\n            </ModernButton>\r\n\r\n            <div className={classes.linkContainer}>\r\n              <Link\r\n                component={RouterLink}\r\n                to=\"/signup\"\r\n                className={classes.link}\r\n              >\r\n                Ainda não tem uma conta? Registre-se\r\n              </Link>\r\n            </div>\r\n          </form>\r\n        </Paper>\r\n\r\n        {/* Copyright */}\r\n        <Typography className={classes.copyright}>\r\n          © 2024 WhatTicket - Sistema de Atendimento\r\n        </Typography>\r\n      </Container>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SAASC,IAAI,IAAIC,UAAU,QAAQ,kBAAkB;AAErD,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOH,IAAI,MAAM,wBAAwB;AACzC,OAAOI,UAAU,MAAM,8BAA8B;AACrD,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,SAASC,YAAY,EAAEC,KAAK,EAAEC,UAAU,EAAEC,aAAa,QAAQ,oBAAoB;AACnF,SAASC,UAAU,EAAEC,cAAc,QAAQ,mBAAmB;AAE9D,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,IAAI,MAAM,4BAA4B;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,SAAS,GAAGb,UAAU,CAACc,KAAK,KAAK;EACrCC,IAAI,EAAE;IACJC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,OAAO;IACfC,UAAU,EAAE,mDAAmD;IAC/DC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,QAAQ;IAClB,WAAW,EAAE;MACXC,OAAO,EAAE,IAAI;MACbF,QAAQ,EAAE,UAAU;MACpBG,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTV,UAAU,EAAE;IACd;EACF,CAAC;EACDW,SAAS,EAAE;IACTP,QAAQ,EAAE,UAAU;IACpBQ,MAAM,EAAE;EACV,CAAC;EACDC,KAAK,EAAE;IACLC,eAAe,EAAE,OAAO;IACxBb,OAAO,EAAE,MAAM;IACfc,aAAa,EAAE,QAAQ;IACvBb,UAAU,EAAE,QAAQ;IACpBc,OAAO,EAAEpB,KAAK,CAACqB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5BC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,iCAAiC;IAC5CC,cAAc,EAAE,YAAY;IAC5BC,MAAM,EAAE,oCAAoC;IAC5CC,QAAQ,EAAE,GAAG;IACbxB,KAAK,EAAE;EACT,CAAC;EACDyB,aAAa,EAAE;IACbC,YAAY,EAAE5B,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;IAC9BD,OAAO,EAAEpB,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;IACzBC,YAAY,EAAE,EAAE;IAChBlB,UAAU,EAAE;EACd,CAAC;EACDN,IAAI,EAAE;IACJK,MAAM,EAAE,EAAE;IACVD,KAAK,EAAE,MAAM;IACbwB,QAAQ,EAAE;EACZ,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,GAAG;IACfC,KAAK,EAAEhC,KAAK,CAACiC,OAAO,CAACC,IAAI,CAACC,OAAO;IACjCP,YAAY,EAAE5B,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;IAC9Be,SAAS,EAAE;EACb,CAAC;EACDC,QAAQ,EAAE;IACRP,QAAQ,EAAE,UAAU;IACpBE,KAAK,EAAEhC,KAAK,CAACiC,OAAO,CAACC,IAAI,CAACI,SAAS;IACnCV,YAAY,EAAE5B,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;IAC9Be,SAAS,EAAE;EACb,CAAC;EACDG,IAAI,EAAE;IACJrC,KAAK,EAAE,MAAM;IACbsC,SAAS,EAAExC,KAAK,CAACqB,OAAO,CAAC,CAAC;EAC5B,CAAC;EACDoB,SAAS,EAAE;IACTb,YAAY,EAAE5B,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;IAC9B,0BAA0B,EAAE;MAC1BC,YAAY,EAAE,EAAE;MAChBJ,eAAe,EAAE,2BAA2B;MAC5C,YAAY,EAAE;QACZwB,WAAW,EAAE;MACf,CAAC;MACD,kBAAkB,EAAE;QAClBA,WAAW,EAAE;MACf,CAAC;MACD,wBAAwB,EAAE;QACxBA,WAAW,EAAE1C,KAAK,CAACiC,OAAO,CAACE,OAAO,CAACQ,IAAI;QACvCC,WAAW,EAAE;MACf;IACF,CAAC;IACD,uBAAuB,EAAE;MACvBZ,KAAK,EAAEhC,KAAK,CAACiC,OAAO,CAACC,IAAI,CAACI;IAC5B;EACF,CAAC;EACDO,YAAY,EAAE;IACZL,SAAS,EAAExC,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;IAC3BO,YAAY,EAAE5B,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;IAC9BlB,MAAM,EAAE,EAAE;IACVmB,YAAY,EAAE;EAChB,CAAC;EACDwB,aAAa,EAAE;IACbV,SAAS,EAAE,QAAQ;IACnBI,SAAS,EAAExC,KAAK,CAACqB,OAAO,CAAC,CAAC;EAC5B,CAAC;EACD0B,IAAI,EAAE;IACJf,KAAK,EAAEhC,KAAK,CAACiC,OAAO,CAACE,OAAO,CAACQ,IAAI;IACjCK,cAAc,EAAE,MAAM;IACtBlB,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,GAAG;IACf,SAAS,EAAE;MACTiB,cAAc,EAAE;IAClB;EACF,CAAC;EACDC,SAAS,EAAE;IACTT,SAAS,EAAExC,KAAK,CAACqB,OAAO,CAAC,CAAC,CAAC;IAC3BW,KAAK,EAAE,0BAA0B;IACjCF,QAAQ,EAAE,UAAU;IACpBM,SAAS,EAAE;EACb,CAAC;EACDc,aAAa,EAAE;IACblB,KAAK,EAAEhC,KAAK,CAACiC,OAAO,CAACC,IAAI,CAACI;EAC5B;AACF,CAAC,CAAC,CAAC;AAEH,MAAMa,KAAK,GAAGA,CAAA,KAAM;EAClB,MAAMC,OAAO,GAAGrD,SAAS,CAAC,CAAC;EAE3B,MAAM,CAACsD,IAAI,EAAEC,OAAO,CAAC,GAAG3E,QAAQ,CAAC;IAAE4E,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EAC7D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgF,OAAO,EAAEC,UAAU,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM;IAAEkF;EAAY,CAAC,GAAGjF,UAAU,CAACgB,WAAW,CAAC;EAE/C,MAAMkE,iBAAiB,GAAGC,CAAC,IAAI;IAC7BT,OAAO,CAAC;MAAE,GAAGD,IAAI;MAAE,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,WAAW,GAAG,MAAMJ,CAAC,IAAI;IAC7BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBR,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMC,WAAW,CAACR,IAAI,CAAC;IACzB,CAAC,SAAS;MACRO,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,oBAAoB,GAAGA,CAAA,KAAM;IACjCX,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,oBACE/E,KAAA,CAAA4F,aAAA;IAAKC,SAAS,EAAEnB,OAAO,CAACnD,IAAK;IAAAuE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BnG,KAAA,CAAA4F,aAAA,CAACvF,WAAW;IAAAyF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACfnG,KAAA,CAAA4F,aAAA,CAACnF,SAAS;IAAC2F,SAAS,EAAC,MAAM;IAACpD,QAAQ,EAAC,IAAI;IAAC6C,SAAS,EAAEnB,OAAO,CAACrC,SAAU;IAAAyD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrEnG,KAAA,CAAA4F,aAAA,CAAClF,KAAK;IAACmF,SAAS,EAAEnB,OAAO,CAACnC,KAAM;IAAC8D,SAAS,EAAE,CAAE;IAAAP,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE5CnG,KAAA,CAAA4F,aAAA;IAAKC,SAAS,EAAEnB,OAAO,CAACzB,aAAc;IAAA6C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpCnG,KAAA,CAAA4F,aAAA;IACEC,SAAS,EAAEnB,OAAO,CAACtD,IAAK;IACxBkF,GAAG,EAAElF,IAAK;IACVmF,GAAG,EAAC,iBAAiB;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACtB,CACE,CAAC,eAGNnG,KAAA,CAAA4F,aAAA,CAACrF,UAAU;IAACsF,SAAS,EAAEnB,OAAO,CAACvB,KAAM;IAAA2C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oBAE1B,CAAC,eACbnG,KAAA,CAAA4F,aAAA,CAACrF,UAAU;IAACsF,SAAS,EAAEnB,OAAO,CAACf,QAAS;IAAAmC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sCAE7B,CAAC,eAGbnG,KAAA,CAAA4F,aAAA;IAAMC,SAAS,EAAEnB,OAAO,CAACb,IAAK;IAAC2C,UAAU;IAACC,QAAQ,EAAEhB,WAAY;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9DnG,KAAA,CAAA4F,aAAA,CAACtF,SAAS;IACRoG,OAAO,EAAC,UAAU;IAClBC,QAAQ;IACRC,SAAS;IACTC,EAAE,EAAC,OAAO;IACVC,KAAK,EAAE7F,IAAI,CAAC8F,CAAC,CAAC,kBAAkB,CAAE;IAClCxB,IAAI,EAAC,OAAO;IACZC,KAAK,EAAEb,IAAI,CAACE,KAAM;IAClBmC,QAAQ,EAAE5B,iBAAkB;IAC5B6B,YAAY,EAAC,OAAO;IACpBC,SAAS;IACTrB,SAAS,EAAEnB,OAAO,CAACX,SAAU;IAC7BoD,UAAU,EAAE;MACVC,cAAc,eACZpH,KAAA,CAAA4F,aAAA,CAAC5E,cAAc;QAACc,QAAQ,EAAC,OAAO;QAAAgE,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBAC9BnG,KAAA,CAAA4F,aAAA,CAAChF,KAAK;QAACiF,SAAS,EAAEnB,OAAO,CAACF,aAAc;QAAAsB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAC5B;IAEpB,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACH,CAAC,eAEFnG,KAAA,CAAA4F,aAAA,CAACtF,SAAS;IACRoG,OAAO,EAAC,UAAU;IAClBC,QAAQ;IACRC,SAAS;IACTrB,IAAI,EAAC,UAAU;IACfuB,KAAK,EAAE7F,IAAI,CAAC8F,CAAC,CAAC,qBAAqB,CAAE;IACrCM,IAAI,EAAEtC,YAAY,GAAG,MAAM,GAAG,UAAW;IACzC8B,EAAE,EAAC,UAAU;IACbrB,KAAK,EAAEb,IAAI,CAACG,QAAS;IACrBkC,QAAQ,EAAE5B,iBAAkB;IAC5B6B,YAAY,EAAC,kBAAkB;IAC/BpB,SAAS,EAAEnB,OAAO,CAACX,SAAU;IAC7BoD,UAAU,EAAE;MACVC,cAAc,eACZpH,KAAA,CAAA4F,aAAA,CAAC5E,cAAc;QAACc,QAAQ,EAAC,OAAO;QAAAgE,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBAC9BnG,KAAA,CAAA4F,aAAA,CAACjF,YAAY;QAACkF,SAAS,EAAEnB,OAAO,CAACF,aAAc;QAAAsB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CACnC,CACjB;MACDmB,YAAY,eACVtH,KAAA,CAAA4F,aAAA,CAAC5E,cAAc;QAACc,QAAQ,EAAC,KAAK;QAAAgE,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBAC5BnG,KAAA,CAAA4F,aAAA,CAAC7E,UAAU;QACTwG,OAAO,EAAE5B,oBAAqB;QAC9B6B,IAAI,EAAC,KAAK;QACVC,IAAI,EAAC,OAAO;QAAA3B,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAEXpB,YAAY,gBAAG/E,KAAA,CAAA4F,aAAA,CAAC9E,aAAa;QAAAgF,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,gBAAGnG,KAAA,CAAA4F,aAAA,CAAC/E,UAAU;QAAAiF,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CACvC,CACE;IAEpB,CAAE;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACH,CAAC,eAEFnG,KAAA,CAAA4F,aAAA,CAACzE,YAAY;IACXkG,IAAI,EAAC,QAAQ;IACbT,SAAS;IACTF,OAAO,EAAC,SAAS;IACjBb,SAAS,EAAEnB,OAAO,CAACP,YAAa;IAChCc,OAAO,EAAEA,OAAQ;IACjByC,QAAQ,EAAE,CAAC/C,IAAI,CAACE,KAAK,IAAI,CAACF,IAAI,CAACG,QAAS;IAAAgB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEvClB,OAAO,GAAG,aAAa,GAAG,QACf,CAAC,eAEfjF,KAAA,CAAA4F,aAAA;IAAKC,SAAS,EAAEnB,OAAO,CAACN,aAAc;IAAA0B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpCnG,KAAA,CAAA4F,aAAA,CAACzF,IAAI;IACHiG,SAAS,EAAEhG,UAAW;IACtBuH,EAAE,EAAC,SAAS;IACZ9B,SAAS,EAAEnB,OAAO,CAACL,IAAK;IAAAyB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzB,yCAEK,CACH,CACD,CACD,CAAC,eAGRnG,KAAA,CAAA4F,aAAA,CAACrF,UAAU;IAACsF,SAAS,EAAEnB,OAAO,CAACH,SAAU;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+CAE9B,CACH,CACR,CAAC;AAEV,CAAC;AAED,eAAe1B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}