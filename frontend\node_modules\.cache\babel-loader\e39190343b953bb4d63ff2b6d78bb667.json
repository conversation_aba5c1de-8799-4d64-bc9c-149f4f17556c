{"ast": null, "code": "import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport map from 'lodash-es/map';\nimport merge from 'lodash-es/merge';\nimport * as material from 'material-colors';\nimport { ColorWrap } from '../common';\nimport CircleSwatch from './CircleSwatch';\nexport var Circle = function Circle(_ref) {\n  var width = _ref.width,\n    onChange = _ref.onChange,\n    onSwatchHover = _ref.onSwatchHover,\n    colors = _ref.colors,\n    hex = _ref.hex,\n    circleSize = _ref.circleSize,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    circleSpacing = _ref.circleSpacing,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var styles = reactCSS(merge({\n    'default': {\n      card: {\n        width: width,\n        display: 'flex',\n        flexWrap: 'wrap',\n        marginRight: -circleSpacing,\n        marginBottom: -circleSpacing\n      }\n    }\n  }, passedStyles));\n  var handleChange = function handleChange(hexCode, e) {\n    return onChange({\n      hex: hexCode,\n      source: 'hex'\n    }, e);\n  };\n  return React.createElement('div', {\n    style: styles.card,\n    className: 'circle-picker ' + className\n  }, map(colors, function (c) {\n    return React.createElement(CircleSwatch, {\n      key: c,\n      color: c,\n      onClick: handleChange,\n      onSwatchHover: onSwatchHover,\n      active: hex === c.toLowerCase(),\n      circleSize: circleSize,\n      circleSpacing: circleSpacing\n    });\n  }));\n};\nCircle.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  circleSize: PropTypes.number,\n  circleSpacing: PropTypes.number,\n  styles: PropTypes.object\n};\nCircle.defaultProps = {\n  width: 252,\n  circleSize: 28,\n  circleSpacing: 14,\n  colors: [material.red['500'], material.pink['500'], material.purple['500'], material.deepPurple['500'], material.indigo['500'], material.blue['500'], material.lightBlue['500'], material.cyan['500'], material.teal['500'], material.green['500'], material.lightGreen['500'], material.lime['500'], material.yellow['500'], material.amber['500'], material.orange['500'], material.deepOrange['500'], material.brown['500'], material.blueGrey['500']],\n  styles: {}\n};\nexport default ColorWrap(Circle);", "map": {"version": 3, "names": ["React", "PropTypes", "reactCSS", "map", "merge", "material", "ColorWrap", "CircleSwatch", "Circle", "_ref", "width", "onChange", "onSwatchHover", "colors", "hex", "circleSize", "_ref$styles", "styles", "passedStyles", "undefined", "circleSpacing", "_ref$className", "className", "card", "display", "flexWrap", "marginRight", "marginBottom", "handleChange", "hexCode", "e", "source", "createElement", "style", "c", "key", "color", "onClick", "active", "toLowerCase", "propTypes", "oneOfType", "string", "number", "object", "defaultProps", "red", "pink", "purple", "deepPurple", "indigo", "blue", "lightBlue", "cyan", "teal", "green", "lightGreen", "lime", "yellow", "amber", "orange", "deepOrange", "brown", "blue<PERSON>rey"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/circle/Circle.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport map from 'lodash-es/map';\nimport merge from 'lodash-es/merge';\nimport * as material from 'material-colors';\n\nimport { ColorWrap } from '../common';\nimport CircleSwatch from './CircleSwatch';\n\nexport var Circle = function Circle(_ref) {\n  var width = _ref.width,\n      onChange = _ref.onChange,\n      onSwatchHover = _ref.onSwatchHover,\n      colors = _ref.colors,\n      hex = _ref.hex,\n      circleSize = _ref.circleSize,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      circleSpacing = _ref.circleSpacing,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var styles = reactCSS(merge({\n    'default': {\n      card: {\n        width: width,\n        display: 'flex',\n        flexWrap: 'wrap',\n        marginRight: -circleSpacing,\n        marginBottom: -circleSpacing\n      }\n    }\n  }, passedStyles));\n\n  var handleChange = function handleChange(hexCode, e) {\n    return onChange({ hex: hexCode, source: 'hex' }, e);\n  };\n\n  return React.createElement(\n    'div',\n    { style: styles.card, className: 'circle-picker ' + className },\n    map(colors, function (c) {\n      return React.createElement(CircleSwatch, {\n        key: c,\n        color: c,\n        onClick: handleChange,\n        onSwatchHover: onSwatchHover,\n        active: hex === c.toLowerCase(),\n        circleSize: circleSize,\n        circleSpacing: circleSpacing\n      });\n    })\n  );\n};\n\nCircle.propTypes = {\n  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  circleSize: PropTypes.number,\n  circleSpacing: PropTypes.number,\n  styles: PropTypes.object\n};\n\nCircle.defaultProps = {\n  width: 252,\n  circleSize: 28,\n  circleSpacing: 14,\n  colors: [material.red['500'], material.pink['500'], material.purple['500'], material.deepPurple['500'], material.indigo['500'], material.blue['500'], material.lightBlue['500'], material.cyan['500'], material.teal['500'], material.green['500'], material.lightGreen['500'], material.lime['500'], material.yellow['500'], material.amber['500'], material.orange['500'], material.deepOrange['500'], material.brown['500'], material.blueGrey['500']],\n  styles: {}\n};\n\nexport default ColorWrap(Circle);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,GAAG,MAAM,eAAe;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAO,KAAKC,QAAQ,MAAM,iBAAiB;AAE3C,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,YAAY,MAAM,gBAAgB;AAEzC,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACxC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,aAAa,GAAGH,IAAI,CAACG,aAAa;IAClCC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,GAAG,GAAGL,IAAI,CAACK,GAAG;IACdC,UAAU,GAAGN,IAAI,CAACM,UAAU;IAC5BC,WAAW,GAAGP,IAAI,CAACQ,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,aAAa,GAAGX,IAAI,CAACW,aAAa;IAClCC,cAAc,GAAGZ,IAAI,CAACa,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKF,SAAS,GAAG,EAAE,GAAGE,cAAc;EAElE,IAAIJ,MAAM,GAAGf,QAAQ,CAACE,KAAK,CAAC;IAC1B,SAAS,EAAE;MACTmB,IAAI,EAAE;QACJb,KAAK,EAAEA,KAAK;QACZc,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,MAAM;QAChBC,WAAW,EAAE,CAACN,aAAa;QAC3BO,YAAY,EAAE,CAACP;MACjB;IACF;EACF,CAAC,EAAEF,YAAY,CAAC,CAAC;EAEjB,IAAIU,YAAY,GAAG,SAASA,YAAYA,CAACC,OAAO,EAAEC,CAAC,EAAE;IACnD,OAAOnB,QAAQ,CAAC;MAAEG,GAAG,EAAEe,OAAO;MAAEE,MAAM,EAAE;IAAM,CAAC,EAAED,CAAC,CAAC;EACrD,CAAC;EAED,OAAO9B,KAAK,CAACgC,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAEhB,MAAM,CAACM,IAAI;IAAED,SAAS,EAAE,gBAAgB,GAAGA;EAAU,CAAC,EAC/DnB,GAAG,CAACU,MAAM,EAAE,UAAUqB,CAAC,EAAE;IACvB,OAAOlC,KAAK,CAACgC,aAAa,CAACzB,YAAY,EAAE;MACvC4B,GAAG,EAAED,CAAC;MACNE,KAAK,EAAEF,CAAC;MACRG,OAAO,EAAET,YAAY;MACrBhB,aAAa,EAAEA,aAAa;MAC5B0B,MAAM,EAAExB,GAAG,KAAKoB,CAAC,CAACK,WAAW,CAAC,CAAC;MAC/BxB,UAAU,EAAEA,UAAU;MACtBK,aAAa,EAAEA;IACjB,CAAC,CAAC;EACJ,CAAC,CACH,CAAC;AACH,CAAC;AAEDZ,MAAM,CAACgC,SAAS,GAAG;EACjB9B,KAAK,EAAET,SAAS,CAACwC,SAAS,CAAC,CAACxC,SAAS,CAACyC,MAAM,EAAEzC,SAAS,CAAC0C,MAAM,CAAC,CAAC;EAChE5B,UAAU,EAAEd,SAAS,CAAC0C,MAAM;EAC5BvB,aAAa,EAAEnB,SAAS,CAAC0C,MAAM;EAC/B1B,MAAM,EAAEhB,SAAS,CAAC2C;AACpB,CAAC;AAEDpC,MAAM,CAACqC,YAAY,GAAG;EACpBnC,KAAK,EAAE,GAAG;EACVK,UAAU,EAAE,EAAE;EACdK,aAAa,EAAE,EAAE;EACjBP,MAAM,EAAE,CAACR,QAAQ,CAACyC,GAAG,CAAC,KAAK,CAAC,EAAEzC,QAAQ,CAAC0C,IAAI,CAAC,KAAK,CAAC,EAAE1C,QAAQ,CAAC2C,MAAM,CAAC,KAAK,CAAC,EAAE3C,QAAQ,CAAC4C,UAAU,CAAC,KAAK,CAAC,EAAE5C,QAAQ,CAAC6C,MAAM,CAAC,KAAK,CAAC,EAAE7C,QAAQ,CAAC8C,IAAI,CAAC,KAAK,CAAC,EAAE9C,QAAQ,CAAC+C,SAAS,CAAC,KAAK,CAAC,EAAE/C,QAAQ,CAACgD,IAAI,CAAC,KAAK,CAAC,EAAEhD,QAAQ,CAACiD,IAAI,CAAC,KAAK,CAAC,EAAEjD,QAAQ,CAACkD,KAAK,CAAC,KAAK,CAAC,EAAElD,QAAQ,CAACmD,UAAU,CAAC,KAAK,CAAC,EAAEnD,QAAQ,CAACoD,IAAI,CAAC,KAAK,CAAC,EAAEpD,QAAQ,CAACqD,MAAM,CAAC,KAAK,CAAC,EAAErD,QAAQ,CAACsD,KAAK,CAAC,KAAK,CAAC,EAAEtD,QAAQ,CAACuD,MAAM,CAAC,KAAK,CAAC,EAAEvD,QAAQ,CAACwD,UAAU,CAAC,KAAK,CAAC,EAAExD,QAAQ,CAACyD,KAAK,CAAC,KAAK,CAAC,EAAEzD,QAAQ,CAAC0D,QAAQ,CAAC,KAAK,CAAC,CAAC;EACzb9C,MAAM,EAAE,CAAC;AACX,CAAC;AAED,eAAeX,SAAS,CAACE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}