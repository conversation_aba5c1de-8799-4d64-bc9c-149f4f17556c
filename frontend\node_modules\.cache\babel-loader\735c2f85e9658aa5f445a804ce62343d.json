{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\pages\\\\TicketsCustom\\\\index.js\";\nimport React from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport Grid from \"@material-ui/core/Grid\";\nimport Paper from \"@material-ui/core/Paper\";\nimport Box from \"@material-ui/core/Box\";\nimport Typography from \"@material-ui/core/Typography\";\nimport { makeStyles } from \"@material-ui/core/styles\";\nimport TicketsManager from \"../../components/TicketsManagerTabs/\";\nimport Ticket from \"../../components/Ticket/\";\nimport ModernTicketArea from \"../../components/ModernTicketArea\";\nimport { i18n } from \"../../translate/i18n\";\nimport { ChatBubbleOutline } from \"@material-ui/icons\";\nconst useStyles = makeStyles(theme => ({\n  chatContainer: {\n    flex: 1,\n    // backgroundColor: \"#eee\",\n    padding: theme.spacing(4),\n    height: `calc(100% - 48px)`,\n    overflowY: \"hidden\",\n    backgroundImage: `url(${WhatsappBackground})`,\n    backgroundPosition: 'center',\n    backgroundSize: 'cover',\n    backgroundRepeat: 'no-repeat'\n  },\n  chatPapper: {\n    // backgroundColor: \"red\",\n    display: \"flex\",\n    height: \"100%\"\n  },\n  contactsWrapper: {\n    display: \"flex\",\n    height: \"100%\",\n    flexDirection: \"column\",\n    overflowY: \"hidden\"\n  },\n  messagesWrapper: {\n    display: \"flex\",\n    height: \"100%\",\n    flexDirection: \"column\"\n  },\n  welcomeMsg: {\n    backgroundColor: \"#eee\",\n    display: \"flex\",\n    justifyContent: \"space-evenly\",\n    alignItems: \"center\",\n    height: \"100%\",\n    textAlign: \"center\"\n  }\n}));\nconst TicketsCustom = () => {\n  const classes = useStyles();\n  const {\n    ticketId\n  } = useParams();\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.chatContainer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 3\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.chatPapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 4\n    }\n  }, /*#__PURE__*/React.createElement(Grid, {\n    container: true,\n    spacing: 0,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(Grid, {\n    item: true,\n    xs: 4,\n    className: classes.contactsWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 6\n    }\n  }, /*#__PURE__*/React.createElement(TicketsManager, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }\n  })), /*#__PURE__*/React.createElement(Grid, {\n    item: true,\n    xs: 8,\n    className: classes.messagesWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 6\n    }\n  }, ticketId ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Ticket, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 9\n    }\n  })) : /*#__PURE__*/React.createElement(Paper, {\n    square: true,\n    variant: \"outlined\",\n    className: classes.welcomeMsg,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 8\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 9\n    }\n  }, i18n.t(\"chat.noTicketMessage\")))))));\n};\nexport default TicketsCustom;", "map": {"version": 3, "names": ["React", "useParams", "Grid", "Paper", "Box", "Typography", "makeStyles", "TicketsManager", "Ticket", "ModernTicketArea", "i18n", "ChatBubbleOutline", "useStyles", "theme", "chatContainer", "flex", "padding", "spacing", "height", "overflowY", "backgroundImage", "WhatsappBackground", "backgroundPosition", "backgroundSize", "backgroundRepeat", "chatPapper", "display", "contactsWrapper", "flexDirection", "messagesWrapper", "welcomeMsg", "backgroundColor", "justifyContent", "alignItems", "textAlign", "TicketsCustom", "classes", "ticketId", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "item", "xs", "Fragment", "square", "variant", "t"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/pages/TicketsCustom/index.js"], "sourcesContent": ["import React from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport Grid from \"@material-ui/core/Grid\";\r\nimport Paper from \"@material-ui/core/Paper\";\r\nimport Box from \"@material-ui/core/Box\";\r\nimport Typography from \"@material-ui/core/Typography\";\r\nimport { makeStyles } from \"@material-ui/core/styles\";\r\n\r\nimport TicketsManager from \"../../components/TicketsManagerTabs/\";\r\nimport Ticket from \"../../components/Ticket/\";\r\nimport ModernTicketArea from \"../../components/ModernTicketArea\";\r\n\r\nimport { i18n } from \"../../translate/i18n\";\r\nimport { ChatBubbleOutline } from \"@material-ui/icons\";\r\n\r\nconst useStyles = makeStyles(theme => ({\r\n\tchatContainer: {\r\n\t\tflex: 1,\r\n\t\t// backgroundColor: \"#eee\",\r\n\t\tpadding: theme.spacing(4),\r\n\t\theight: `calc(100% - 48px)`,\r\n\t\toverflowY: \"hidden\",\r\n\t\tbackgroundImage: `url(${WhatsappBackground})`,\r\n\t\tbackgroundPosition: 'center', \r\n\t\tbackgroundSize: 'cover', \r\n\t\tbackgroundRepeat: 'no-repeat',\r\n\t},\r\n\r\n\tchatPapper: {\r\n\t\t// backgroundColor: \"red\",\r\n\t\tdisplay: \"flex\",\r\n\t\theight: \"100%\",\r\n\t},\r\n\r\n\tcontactsWrapper: {\r\n\t\tdisplay: \"flex\",\r\n\t\theight: \"100%\",\r\n\t\tflexDirection: \"column\",\r\n\t\toverflowY: \"hidden\",\r\n\t},\r\n\tmessagesWrapper: {\r\n\t\tdisplay: \"flex\",\r\n\t\theight: \"100%\",\r\n\t\tflexDirection: \"column\",\r\n\t},\r\n\twelcomeMsg: {\r\n\t\tbackgroundColor: \"#eee\",\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"space-evenly\",\r\n\t\talignItems: \"center\",\r\n\t\theight: \"100%\",\r\n\t\ttextAlign: \"center\",\r\n\t},\r\n}));\r\n\r\nconst TicketsCustom = () => {\r\n\tconst classes = useStyles();\r\n\tconst { ticketId } = useParams();\r\n\r\n\treturn (\r\n\t\t<div className={classes.chatContainer}>\r\n\t\t\t<div className={classes.chatPapper}>\r\n\t\t\t\t<Grid container spacing={0}>\r\n\t\t\t\t\t<Grid item xs={4} className={classes.contactsWrapper}>\r\n\t\t\t\t\t\t<TicketsManager />\r\n\t\t\t\t\t</Grid>\r\n\t\t\t\t\t<Grid item xs={8} className={classes.messagesWrapper}>\r\n\t\t\t\t\t\t{ticketId ? (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t<Ticket />\r\n\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t<Paper square variant=\"outlined\" className={classes.welcomeMsg}>\r\n\t\t\t\t\t\t\t\t<span>{i18n.t(\"chat.noTicketMessage\")}</span>\r\n\t\t\t\t\t\t\t</Paper>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Grid>\r\n\t\t\t\t</Grid>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default TicketsCustom;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,IAAI,MAAM,wBAAwB;AACzC,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,GAAG,MAAM,uBAAuB;AACvC,OAAOC,UAAU,MAAM,8BAA8B;AACrD,SAASC,UAAU,QAAQ,0BAA0B;AAErD,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,OAAOC,gBAAgB,MAAM,mCAAmC;AAEhE,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,iBAAiB,QAAQ,oBAAoB;AAEtD,MAAMC,SAAS,GAAGN,UAAU,CAACO,KAAK,KAAK;EACtCC,aAAa,EAAE;IACdC,IAAI,EAAE,CAAC;IACP;IACAC,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;IACzBC,MAAM,EAAE,mBAAmB;IAC3BC,SAAS,EAAE,QAAQ;IACnBC,eAAe,EAAE,OAAOC,kBAAkB,GAAG;IAC7CC,kBAAkB,EAAE,QAAQ;IAC5BC,cAAc,EAAE,OAAO;IACvBC,gBAAgB,EAAE;EACnB,CAAC;EAEDC,UAAU,EAAE;IACX;IACAC,OAAO,EAAE,MAAM;IACfR,MAAM,EAAE;EACT,CAAC;EAEDS,eAAe,EAAE;IAChBD,OAAO,EAAE,MAAM;IACfR,MAAM,EAAE,MAAM;IACdU,aAAa,EAAE,QAAQ;IACvBT,SAAS,EAAE;EACZ,CAAC;EACDU,eAAe,EAAE;IAChBH,OAAO,EAAE,MAAM;IACfR,MAAM,EAAE,MAAM;IACdU,aAAa,EAAE;EAChB,CAAC;EACDE,UAAU,EAAE;IACXC,eAAe,EAAE,MAAM;IACvBL,OAAO,EAAE,MAAM;IACfM,cAAc,EAAE,cAAc;IAC9BC,UAAU,EAAE,QAAQ;IACpBf,MAAM,EAAE,MAAM;IACdgB,SAAS,EAAE;EACZ;AACD,CAAC,CAAC,CAAC;AAEH,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC3B,MAAMC,OAAO,GAAGxB,SAAS,CAAC,CAAC;EAC3B,MAAM;IAAEyB;EAAS,CAAC,GAAGpC,SAAS,CAAC,CAAC;EAEhC,oBACCD,KAAA,CAAAsC,aAAA;IAAKC,SAAS,EAAEH,OAAO,CAACtB,aAAc;IAAA0B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrC7C,KAAA,CAAAsC,aAAA;IAAKC,SAAS,EAAEH,OAAO,CAACX,UAAW;IAAAe,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClC7C,KAAA,CAAAsC,aAAA,CAACpC,IAAI;IAAC4C,SAAS;IAAC7B,OAAO,EAAE,CAAE;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B7C,KAAA,CAAAsC,aAAA,CAACpC,IAAI;IAAC6C,IAAI;IAACC,EAAE,EAAE,CAAE;IAACT,SAAS,EAAEH,OAAO,CAACT,eAAgB;IAAAa,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpD7C,KAAA,CAAAsC,aAAA,CAAC/B,cAAc;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACZ,CAAC,eACP7C,KAAA,CAAAsC,aAAA,CAACpC,IAAI;IAAC6C,IAAI;IAACC,EAAE,EAAE,CAAE;IAACT,SAAS,EAAEH,OAAO,CAACP,eAAgB;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnDR,QAAQ,gBACRrC,KAAA,CAAAsC,aAAA,CAAAtC,KAAA,CAAAiD,QAAA,qBACCjD,KAAA,CAAAsC,aAAA,CAAC9B,MAAM;IAAAgC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACR,CAAC,gBAEH7C,KAAA,CAAAsC,aAAA,CAACnC,KAAK;IAAC+C,MAAM;IAACC,OAAO,EAAC,UAAU;IAACZ,SAAS,EAAEH,OAAO,CAACN,UAAW;IAAAU,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9D7C,KAAA,CAAAsC,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAOnC,IAAI,CAAC0C,CAAC,CAAC,sBAAsB,CAAQ,CACtC,CAEH,CACD,CACF,CACD,CAAC;AAER,CAAC;AAED,eAAejB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}