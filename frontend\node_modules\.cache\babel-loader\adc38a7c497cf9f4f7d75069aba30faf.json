{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\pages\\\\TicketsCustom\\\\index.js\";\nimport React from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport Grid from \"@material-ui/core/Grid\";\nimport Paper from \"@material-ui/core/Paper\";\nimport Box from \"@material-ui/core/Box\";\nimport Typography from \"@material-ui/core/Typography\";\nimport { makeStyles } from \"@material-ui/core/styles\";\nimport TicketsManager from \"../../components/TicketsManagerTabs/\";\nimport Ticket from \"../../components/Ticket/\";\nimport ModernTicketArea from \"../../components/ModernTicketArea\";\nimport { i18n } from \"../../translate/i18n\";\nimport { ChatBubbleOutline } from \"@material-ui/icons\";\nconst useStyles = makeStyles(theme => ({\n  modernContainer: {\n    height: `calc(100vh - 64px)`,\n    display: \"flex\",\n    backgroundColor: theme.palette.background.default,\n    overflow: \"hidden\"\n  },\n  ticketsPanel: {\n    width: 400,\n    borderRight: '1px solid rgba(0,0,0,0.06)',\n    backgroundColor: 'white',\n    display: \"flex\",\n    flexDirection: \"column\",\n    boxShadow: '2px 0 8px rgba(0,0,0,0.04)'\n  },\n  chatPanel: {\n    flex: 1,\n    display: \"flex\",\n    flexDirection: \"column\",\n    backgroundColor: theme.palette.background.default\n  },\n  welcomeContainer: {\n    flex: 1,\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    padding: theme.spacing(4),\n    textAlign: \"center\",\n    backgroundColor: 'white',\n    margin: theme.spacing(2),\n    borderRadius: 16,\n    boxShadow: '0 4px 20px rgba(0,0,0,0.08)'\n  },\n  welcomeIcon: {\n    fontSize: '4rem',\n    color: theme.palette.primary.main,\n    marginBottom: theme.spacing(2),\n    opacity: 0.7\n  },\n  welcomeTitle: {\n    fontSize: '1.5rem',\n    fontWeight: 600,\n    color: theme.palette.text.primary,\n    marginBottom: theme.spacing(1)\n  },\n  welcomeSubtitle: {\n    fontSize: '1rem',\n    color: theme.palette.text.secondary,\n    maxWidth: 400,\n    lineHeight: 1.6\n  },\n  // Manter estilos antigos para compatibilidade\n  chatContainer: {\n    flex: 1,\n    padding: theme.spacing(2),\n    height: `calc(100% - 64px)`,\n    overflowY: \"hidden\",\n    backgroundColor: theme.palette.background.default\n  },\n  chatPapper: {\n    display: \"flex\",\n    height: \"100%\",\n    borderRadius: 16,\n    overflow: \"hidden\",\n    boxShadow: '0 4px 20px rgba(0,0,0,0.08)'\n  },\n  contactsWrapper: {\n    display: \"flex\",\n    height: \"100%\",\n    flexDirection: \"column\",\n    overflowY: \"hidden\",\n    backgroundColor: 'white'\n  },\n  messagesWrapper: {\n    display: \"flex\",\n    height: \"100%\",\n    flexDirection: \"column\",\n    backgroundColor: 'white'\n  },\n  welcomeMsg: {\n    backgroundColor: theme.palette.background.default,\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    height: \"100%\",\n    textAlign: \"center\",\n    borderRadius: 0\n  }\n}));\nconst TicketsCustom = () => {\n  const classes = useStyles();\n  const {\n    ticketId\n  } = useParams();\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.chatContainer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 3\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.chatPapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 4\n    }\n  }, /*#__PURE__*/React.createElement(Grid, {\n    container: true,\n    spacing: 0,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(Grid, {\n    item: true,\n    xs: 4,\n    className: classes.contactsWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 6\n    }\n  }, /*#__PURE__*/React.createElement(TicketsManager, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }\n  })), /*#__PURE__*/React.createElement(Grid, {\n    item: true,\n    xs: 8,\n    className: classes.messagesWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 6\n    }\n  }, ticketId ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Ticket, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 9\n    }\n  })) : /*#__PURE__*/React.createElement(Paper, {\n    square: true,\n    variant: \"outlined\",\n    className: classes.welcomeMsg,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 8\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }\n  }, i18n.t(\"chat.noTicketMessage\")))))));\n};\nexport default TicketsCustom;", "map": {"version": 3, "names": ["React", "useParams", "Grid", "Paper", "Box", "Typography", "makeStyles", "TicketsManager", "Ticket", "ModernTicketArea", "i18n", "ChatBubbleOutline", "useStyles", "theme", "modernContainer", "height", "display", "backgroundColor", "palette", "background", "default", "overflow", "ticketsPanel", "width", "borderRight", "flexDirection", "boxShadow", "chatPanel", "flex", "<PERSON><PERSON><PERSON><PERSON>", "alignItems", "justifyContent", "padding", "spacing", "textAlign", "margin", "borderRadius", "welcomeIcon", "fontSize", "color", "primary", "main", "marginBottom", "opacity", "welcomeTitle", "fontWeight", "text", "welcomeSubtitle", "secondary", "max<PERSON><PERSON><PERSON>", "lineHeight", "chatContainer", "overflowY", "chatPapper", "contactsWrapper", "messagesWrapper", "welcomeMsg", "TicketsCustom", "classes", "ticketId", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "item", "xs", "Fragment", "square", "variant", "t"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/pages/TicketsCustom/index.js"], "sourcesContent": ["import React from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport Grid from \"@material-ui/core/Grid\";\r\nimport Paper from \"@material-ui/core/Paper\";\r\nimport Box from \"@material-ui/core/Box\";\r\nimport Typography from \"@material-ui/core/Typography\";\r\nimport { makeStyles } from \"@material-ui/core/styles\";\r\n\r\nimport TicketsManager from \"../../components/TicketsManagerTabs/\";\r\nimport Ticket from \"../../components/Ticket/\";\r\nimport ModernTicketArea from \"../../components/ModernTicketArea\";\r\n\r\nimport { i18n } from \"../../translate/i18n\";\r\nimport { ChatBubbleOutline } from \"@material-ui/icons\";\r\n\r\nconst useStyles = makeStyles(theme => ({\r\n\tmodernContainer: {\r\n\t\theight: `calc(100vh - 64px)`,\r\n\t\tdisplay: \"flex\",\r\n\t\tbackgroundColor: theme.palette.background.default,\r\n\t\toverflow: \"hidden\",\r\n\t},\r\n\tticketsPanel: {\r\n\t\twidth: 400,\r\n\t\tborderRight: '1px solid rgba(0,0,0,0.06)',\r\n\t\tbackgroundColor: 'white',\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\tboxShadow: '2px 0 8px rgba(0,0,0,0.04)',\r\n\t},\r\n\tchatPanel: {\r\n\t\tflex: 1,\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\tbackgroundColor: theme.palette.background.default,\r\n\t},\r\n\twelcomeContainer: {\r\n\t\tflex: 1,\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tpadding: theme.spacing(4),\r\n\t\ttextAlign: \"center\",\r\n\t\tbackgroundColor: 'white',\r\n\t\tmargin: theme.spacing(2),\r\n\t\tborderRadius: 16,\r\n\t\tboxShadow: '0 4px 20px rgba(0,0,0,0.08)',\r\n\t},\r\n\twelcomeIcon: {\r\n\t\tfontSize: '4rem',\r\n\t\tcolor: theme.palette.primary.main,\r\n\t\tmarginBottom: theme.spacing(2),\r\n\t\topacity: 0.7,\r\n\t},\r\n\twelcomeTitle: {\r\n\t\tfontSize: '1.5rem',\r\n\t\tfontWeight: 600,\r\n\t\tcolor: theme.palette.text.primary,\r\n\t\tmarginBottom: theme.spacing(1),\r\n\t},\r\n\twelcomeSubtitle: {\r\n\t\tfontSize: '1rem',\r\n\t\tcolor: theme.palette.text.secondary,\r\n\t\tmaxWidth: 400,\r\n\t\tlineHeight: 1.6,\r\n\t},\r\n\t// Manter estilos antigos para compatibilidade\r\n\tchatContainer: {\r\n\t\tflex: 1,\r\n\t\tpadding: theme.spacing(2),\r\n\t\theight: `calc(100% - 64px)`,\r\n\t\toverflowY: \"hidden\",\r\n\t\tbackgroundColor: theme.palette.background.default,\r\n\t},\r\n\tchatPapper: {\r\n\t\tdisplay: \"flex\",\r\n\t\theight: \"100%\",\r\n\t\tborderRadius: 16,\r\n\t\toverflow: \"hidden\",\r\n\t\tboxShadow: '0 4px 20px rgba(0,0,0,0.08)',\r\n\t},\r\n\tcontactsWrapper: {\r\n\t\tdisplay: \"flex\",\r\n\t\theight: \"100%\",\r\n\t\tflexDirection: \"column\",\r\n\t\toverflowY: \"hidden\",\r\n\t\tbackgroundColor: 'white',\r\n\t},\r\n\tmessagesWrapper: {\r\n\t\tdisplay: \"flex\",\r\n\t\theight: \"100%\",\r\n\t\tflexDirection: \"column\",\r\n\t\tbackgroundColor: 'white',\r\n\t},\r\n\twelcomeMsg: {\r\n\t\tbackgroundColor: theme.palette.background.default,\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\theight: \"100%\",\r\n\t\ttextAlign: \"center\",\r\n\t\tborderRadius: 0,\r\n\t},\r\n}));\r\n\r\nconst TicketsCustom = () => {\r\n\tconst classes = useStyles();\r\n\tconst { ticketId } = useParams();\r\n\r\n\treturn (\r\n\t\t<div className={classes.chatContainer}>\r\n\t\t\t<div className={classes.chatPapper}>\r\n\t\t\t\t<Grid container spacing={0}>\r\n\t\t\t\t\t<Grid item xs={4} className={classes.contactsWrapper}>\r\n\t\t\t\t\t\t<TicketsManager />\r\n\t\t\t\t\t</Grid>\r\n\t\t\t\t\t<Grid item xs={8} className={classes.messagesWrapper}>\r\n\t\t\t\t\t\t{ticketId ? (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t<Ticket />\r\n\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t<Paper square variant=\"outlined\" className={classes.welcomeMsg}>\r\n\t\t\t\t\t\t\t\t<span>{i18n.t(\"chat.noTicketMessage\")}</span>\r\n\t\t\t\t\t\t\t</Paper>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Grid>\r\n\t\t\t\t</Grid>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default TicketsCustom;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,IAAI,MAAM,wBAAwB;AACzC,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,GAAG,MAAM,uBAAuB;AACvC,OAAOC,UAAU,MAAM,8BAA8B;AACrD,SAASC,UAAU,QAAQ,0BAA0B;AAErD,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,OAAOC,gBAAgB,MAAM,mCAAmC;AAEhE,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,iBAAiB,QAAQ,oBAAoB;AAEtD,MAAMC,SAAS,GAAGN,UAAU,CAACO,KAAK,KAAK;EACtCC,eAAe,EAAE;IAChBC,MAAM,EAAE,oBAAoB;IAC5BC,OAAO,EAAE,MAAM;IACfC,eAAe,EAAEJ,KAAK,CAACK,OAAO,CAACC,UAAU,CAACC,OAAO;IACjDC,QAAQ,EAAE;EACX,CAAC;EACDC,YAAY,EAAE;IACbC,KAAK,EAAE,GAAG;IACVC,WAAW,EAAE,4BAA4B;IACzCP,eAAe,EAAE,OAAO;IACxBD,OAAO,EAAE,MAAM;IACfS,aAAa,EAAE,QAAQ;IACvBC,SAAS,EAAE;EACZ,CAAC;EACDC,SAAS,EAAE;IACVC,IAAI,EAAE,CAAC;IACPZ,OAAO,EAAE,MAAM;IACfS,aAAa,EAAE,QAAQ;IACvBR,eAAe,EAAEJ,KAAK,CAACK,OAAO,CAACC,UAAU,CAACC;EAC3C,CAAC;EACDS,gBAAgB,EAAE;IACjBD,IAAI,EAAE,CAAC;IACPZ,OAAO,EAAE,MAAM;IACfS,aAAa,EAAE,QAAQ;IACvBK,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,OAAO,EAAEnB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;IACzBC,SAAS,EAAE,QAAQ;IACnBjB,eAAe,EAAE,OAAO;IACxBkB,MAAM,EAAEtB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;IACxBG,YAAY,EAAE,EAAE;IAChBV,SAAS,EAAE;EACZ,CAAC;EACDW,WAAW,EAAE;IACZC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE1B,KAAK,CAACK,OAAO,CAACsB,OAAO,CAACC,IAAI;IACjCC,YAAY,EAAE7B,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;IAC9BU,OAAO,EAAE;EACV,CAAC;EACDC,YAAY,EAAE;IACbN,QAAQ,EAAE,QAAQ;IAClBO,UAAU,EAAE,GAAG;IACfN,KAAK,EAAE1B,KAAK,CAACK,OAAO,CAAC4B,IAAI,CAACN,OAAO;IACjCE,YAAY,EAAE7B,KAAK,CAACoB,OAAO,CAAC,CAAC;EAC9B,CAAC;EACDc,eAAe,EAAE;IAChBT,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE1B,KAAK,CAACK,OAAO,CAAC4B,IAAI,CAACE,SAAS;IACnCC,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE;EACb,CAAC;EACD;EACAC,aAAa,EAAE;IACdvB,IAAI,EAAE,CAAC;IACPI,OAAO,EAAEnB,KAAK,CAACoB,OAAO,CAAC,CAAC,CAAC;IACzBlB,MAAM,EAAE,mBAAmB;IAC3BqC,SAAS,EAAE,QAAQ;IACnBnC,eAAe,EAAEJ,KAAK,CAACK,OAAO,CAACC,UAAU,CAACC;EAC3C,CAAC;EACDiC,UAAU,EAAE;IACXrC,OAAO,EAAE,MAAM;IACfD,MAAM,EAAE,MAAM;IACdqB,YAAY,EAAE,EAAE;IAChBf,QAAQ,EAAE,QAAQ;IAClBK,SAAS,EAAE;EACZ,CAAC;EACD4B,eAAe,EAAE;IAChBtC,OAAO,EAAE,MAAM;IACfD,MAAM,EAAE,MAAM;IACdU,aAAa,EAAE,QAAQ;IACvB2B,SAAS,EAAE,QAAQ;IACnBnC,eAAe,EAAE;EAClB,CAAC;EACDsC,eAAe,EAAE;IAChBvC,OAAO,EAAE,MAAM;IACfD,MAAM,EAAE,MAAM;IACdU,aAAa,EAAE,QAAQ;IACvBR,eAAe,EAAE;EAClB,CAAC;EACDuC,UAAU,EAAE;IACXvC,eAAe,EAAEJ,KAAK,CAACK,OAAO,CAACC,UAAU,CAACC,OAAO;IACjDJ,OAAO,EAAE,MAAM;IACfe,cAAc,EAAE,QAAQ;IACxBD,UAAU,EAAE,QAAQ;IACpBf,MAAM,EAAE,MAAM;IACdmB,SAAS,EAAE,QAAQ;IACnBE,YAAY,EAAE;EACf;AACD,CAAC,CAAC,CAAC;AAEH,MAAMqB,aAAa,GAAGA,CAAA,KAAM;EAC3B,MAAMC,OAAO,GAAG9C,SAAS,CAAC,CAAC;EAC3B,MAAM;IAAE+C;EAAS,CAAC,GAAG1D,SAAS,CAAC,CAAC;EAEhC,oBACCD,KAAA,CAAA4D,aAAA;IAAKC,SAAS,EAAEH,OAAO,CAACP,aAAc;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrCnE,KAAA,CAAA4D,aAAA;IAAKC,SAAS,EAAEH,OAAO,CAACL,UAAW;IAAAS,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCnE,KAAA,CAAA4D,aAAA,CAAC1D,IAAI;IAACkE,SAAS;IAACnC,OAAO,EAAE,CAAE;IAAA6B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BnE,KAAA,CAAA4D,aAAA,CAAC1D,IAAI;IAACmE,IAAI;IAACC,EAAE,EAAE,CAAE;IAACT,SAAS,EAAEH,OAAO,CAACJ,eAAgB;IAAAQ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpDnE,KAAA,CAAA4D,aAAA,CAACrD,cAAc;IAAAuD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACZ,CAAC,eACPnE,KAAA,CAAA4D,aAAA,CAAC1D,IAAI;IAACmE,IAAI;IAACC,EAAE,EAAE,CAAE;IAACT,SAAS,EAAEH,OAAO,CAACH,eAAgB;IAAAO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnDR,QAAQ,gBACR3D,KAAA,CAAA4D,aAAA,CAAA5D,KAAA,CAAAuE,QAAA,qBACCvE,KAAA,CAAA4D,aAAA,CAACpD,MAAM;IAAAsD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACR,CAAC,gBAEHnE,KAAA,CAAA4D,aAAA,CAACzD,KAAK;IAACqE,MAAM;IAACC,OAAO,EAAC,UAAU;IAACZ,SAAS,EAAEH,OAAO,CAACF,UAAW;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9DnE,KAAA,CAAA4D,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAOzD,IAAI,CAACgE,CAAC,CAAC,sBAAsB,CAAQ,CACtC,CAEH,CACD,CACF,CACD,CAAC;AAER,CAAC;AAED,eAAejB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}