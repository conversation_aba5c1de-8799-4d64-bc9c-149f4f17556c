{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\dev\\\\whaticket\\\\frontend\\\\src\\\\components\\\\ModernPageContainer\\\\index.js\";\nimport React from 'react';\nimport { makeStyles } from '@material-ui/core/styles';\nimport { Container, Typography, Box, Breadcrumbs, Link, Paper } from '@material-ui/core';\nimport { NavigateNext, Home } from '@material-ui/icons';\nconst useStyles = makeStyles(theme => ({\n  pageContainer: {\n    minHeight: '100vh',\n    backgroundColor: theme.palette.background.default,\n    paddingTop: theme.spacing(3),\n    paddingBottom: theme.spacing(3)\n  },\n  pageHeader: {\n    marginBottom: theme.spacing(4),\n    padding: theme.spacing(3),\n    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n    borderRadius: 16,\n    border: '1px solid rgba(102, 126, 234, 0.1)'\n  },\n  pageTitle: {\n    fontSize: '2rem',\n    fontWeight: 700,\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n    marginBottom: theme.spacing(1)\n  },\n  pageSubtitle: {\n    fontSize: '1rem',\n    color: theme.palette.text.secondary,\n    marginBottom: theme.spacing(2)\n  },\n  breadcrumbs: {\n    '& .MuiBreadcrumbs-separator': {\n      color: theme.palette.text.secondary\n    },\n    '& .MuiLink-root': {\n      color: theme.palette.text.secondary,\n      textDecoration: 'none',\n      fontSize: '0.875rem',\n      '&:hover': {\n        color: theme.palette.primary.main,\n        textDecoration: 'underline'\n      }\n    },\n    '& .MuiTypography-root': {\n      color: theme.palette.primary.main,\n      fontSize: '0.875rem',\n      fontWeight: 500\n    }\n  },\n  contentArea: {\n    '& > *': {\n      marginBottom: theme.spacing(3)\n    }\n  },\n  modernPaper: {\n    padding: theme.spacing(3),\n    borderRadius: 16,\n    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n    border: '1px solid rgba(0,0,0,0.04)',\n    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'\n  },\n  actionBar: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: theme.spacing(3),\n    padding: theme.spacing(2),\n    backgroundColor: 'white',\n    borderRadius: 12,\n    boxShadow: '0 2px 8px rgba(0,0,0,0.04)'\n  },\n  homeIcon: {\n    fontSize: '1rem',\n    marginRight: theme.spacing(0.5)\n  }\n}));\nconst ModernPageContainer = ({\n  title,\n  subtitle,\n  breadcrumbs = [],\n  children,\n  maxWidth = 'lg',\n  showHeader = true,\n  actionBar,\n  ...props\n}) => {\n  const classes = useStyles();\n  const defaultBreadcrumbs = [{\n    label: 'Início',\n    href: '/dashboard',\n    icon: /*#__PURE__*/React.createElement(Home, {\n      className: classes.homeIcon,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 13\n      }\n    })\n  }, ...breadcrumbs];\n  return /*#__PURE__*/React.createElement(Container, Object.assign({\n    maxWidth: maxWidth,\n    className: classes.pageContainer\n  }, props, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 5\n    }\n  }), showHeader && /*#__PURE__*/React.createElement(Paper, {\n    className: classes.pageHeader,\n    elevation: 0,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 9\n    }\n  }, defaultBreadcrumbs.length > 1 && /*#__PURE__*/React.createElement(Breadcrumbs, {\n    separator: /*#__PURE__*/React.createElement(NavigateNext, {\n      fontSize: \"small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 26\n      }\n    }),\n    className: classes.breadcrumbs,\n    \"aria-label\": \"breadcrumb\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }\n  }, defaultBreadcrumbs.slice(0, -1).map((crumb, index) => /*#__PURE__*/React.createElement(Link, {\n    key: index,\n    href: crumb.href,\n    onClick: e => {\n      if (crumb.onClick) {\n        e.preventDefault();\n        crumb.onClick();\n      }\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(Box, {\n    display: \"flex\",\n    alignItems: \"center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 19\n    }\n  }, crumb.icon, crumb.label))), /*#__PURE__*/React.createElement(Typography, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 15\n    }\n  }, defaultBreadcrumbs[defaultBreadcrumbs.length - 1].label)), title && /*#__PURE__*/React.createElement(Typography, {\n    className: classes.pageTitle,\n    variant: \"h1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 13\n    }\n  }, title), subtitle && /*#__PURE__*/React.createElement(Typography, {\n    className: classes.pageSubtitle,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 13\n    }\n  }, subtitle)), actionBar && /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.actionBar,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 9\n    }\n  }, actionBar), /*#__PURE__*/React.createElement(\"div\", {\n    className: classes.contentArea,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }\n  }, children));\n};\nexport default ModernPageContainer;", "map": {"version": 3, "names": ["React", "makeStyles", "Container", "Typography", "Box", "Breadcrumbs", "Link", "Paper", "NavigateNext", "Home", "useStyles", "theme", "pageContainer", "minHeight", "backgroundColor", "palette", "background", "default", "paddingTop", "spacing", "paddingBottom", "pageHeader", "marginBottom", "padding", "borderRadius", "border", "pageTitle", "fontSize", "fontWeight", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "pageSubtitle", "color", "text", "secondary", "breadcrumbs", "textDecoration", "primary", "main", "contentArea", "modernPaper", "boxShadow", "actionBar", "display", "justifyContent", "alignItems", "homeIcon", "marginRight", "ModernPageContainer", "title", "subtitle", "children", "max<PERSON><PERSON><PERSON>", "showHeader", "props", "classes", "defaultBreadcrumbs", "label", "href", "icon", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Object", "assign", "elevation", "length", "separator", "slice", "map", "crumb", "index", "key", "onClick", "e", "preventDefault", "variant"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/src/components/ModernPageContainer/index.js"], "sourcesContent": ["import React from 'react';\nimport { makeStyles } from '@material-ui/core/styles';\nimport {\n  Container,\n  Typography,\n  Box,\n  Breadcrumbs,\n  Link,\n  Paper\n} from '@material-ui/core';\nimport { NavigateNext, Home } from '@material-ui/icons';\n\nconst useStyles = makeStyles((theme) => ({\n  pageContainer: {\n    minHeight: '100vh',\n    backgroundColor: theme.palette.background.default,\n    paddingTop: theme.spacing(3),\n    paddingBottom: theme.spacing(3),\n  },\n  pageHeader: {\n    marginBottom: theme.spacing(4),\n    padding: theme.spacing(3),\n    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n    borderRadius: 16,\n    border: '1px solid rgba(102, 126, 234, 0.1)',\n  },\n  pageTitle: {\n    fontSize: '2rem',\n    fontWeight: 700,\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n    marginBottom: theme.spacing(1),\n  },\n  pageSubtitle: {\n    fontSize: '1rem',\n    color: theme.palette.text.secondary,\n    marginBottom: theme.spacing(2),\n  },\n  breadcrumbs: {\n    '& .MuiBreadcrumbs-separator': {\n      color: theme.palette.text.secondary,\n    },\n    '& .MuiLink-root': {\n      color: theme.palette.text.secondary,\n      textDecoration: 'none',\n      fontSize: '0.875rem',\n      '&:hover': {\n        color: theme.palette.primary.main,\n        textDecoration: 'underline',\n      }\n    },\n    '& .MuiTypography-root': {\n      color: theme.palette.primary.main,\n      fontSize: '0.875rem',\n      fontWeight: 500,\n    }\n  },\n  contentArea: {\n    '& > *': {\n      marginBottom: theme.spacing(3),\n    }\n  },\n  modernPaper: {\n    padding: theme.spacing(3),\n    borderRadius: 16,\n    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n    border: '1px solid rgba(0,0,0,0.04)',\n    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\n  },\n  actionBar: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginBottom: theme.spacing(3),\n    padding: theme.spacing(2),\n    backgroundColor: 'white',\n    borderRadius: 12,\n    boxShadow: '0 2px 8px rgba(0,0,0,0.04)',\n  },\n  homeIcon: {\n    fontSize: '1rem',\n    marginRight: theme.spacing(0.5),\n  }\n}));\n\nconst ModernPageContainer = ({\n  title,\n  subtitle,\n  breadcrumbs = [],\n  children,\n  maxWidth = 'lg',\n  showHeader = true,\n  actionBar,\n  ...props\n}) => {\n  const classes = useStyles();\n\n  const defaultBreadcrumbs = [\n    {\n      label: 'Início',\n      href: '/dashboard',\n      icon: <Home className={classes.homeIcon} />\n    },\n    ...breadcrumbs\n  ];\n\n  return (\n    <Container maxWidth={maxWidth} className={classes.pageContainer} {...props}>\n      {showHeader && (\n        <Paper className={classes.pageHeader} elevation={0}>\n          {/* Breadcrumbs */}\n          {defaultBreadcrumbs.length > 1 && (\n            <Breadcrumbs\n              separator={<NavigateNext fontSize=\"small\" />}\n              className={classes.breadcrumbs}\n              aria-label=\"breadcrumb\"\n            >\n              {defaultBreadcrumbs.slice(0, -1).map((crumb, index) => (\n                <Link\n                  key={index}\n                  href={crumb.href}\n                  onClick={(e) => {\n                    if (crumb.onClick) {\n                      e.preventDefault();\n                      crumb.onClick();\n                    }\n                  }}\n                >\n                  <Box display=\"flex\" alignItems=\"center\">\n                    {crumb.icon}\n                    {crumb.label}\n                  </Box>\n                </Link>\n              ))}\n              <Typography>\n                {defaultBreadcrumbs[defaultBreadcrumbs.length - 1].label}\n              </Typography>\n            </Breadcrumbs>\n          )}\n\n          {/* Page Title */}\n          {title && (\n            <Typography className={classes.pageTitle} variant=\"h1\">\n              {title}\n            </Typography>\n          )}\n\n          {/* Page Subtitle */}\n          {subtitle && (\n            <Typography className={classes.pageSubtitle}>\n              {subtitle}\n            </Typography>\n          )}\n        </Paper>\n      )}\n\n      {/* Action Bar */}\n      {actionBar && (\n        <div className={classes.actionBar}>\n          {actionBar}\n        </div>\n      )}\n\n      {/* Content Area */}\n      <div className={classes.contentArea}>\n        {children}\n      </div>\n    </Container>\n  );\n};\n\nexport default ModernPageContainer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SACEC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,WAAW,EACXC,IAAI,EACJC,KAAK,QACA,mBAAmB;AAC1B,SAASC,YAAY,EAAEC,IAAI,QAAQ,oBAAoB;AAEvD,MAAMC,SAAS,GAAGT,UAAU,CAAEU,KAAK,KAAM;EACvCC,aAAa,EAAE;IACbC,SAAS,EAAE,OAAO;IAClBC,eAAe,EAAEH,KAAK,CAACI,OAAO,CAACC,UAAU,CAACC,OAAO;IACjDC,UAAU,EAAEP,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;IAC5BC,aAAa,EAAET,KAAK,CAACQ,OAAO,CAAC,CAAC;EAChC,CAAC;EACDE,UAAU,EAAE;IACVC,YAAY,EAAEX,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;IAC9BI,OAAO,EAAEZ,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;IACzBH,UAAU,EAAE,sFAAsF;IAClGQ,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE;EACV,CAAC;EACDC,SAAS,EAAE;IACTC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,GAAG;IACfZ,UAAU,EAAE,mDAAmD;IAC/Da,cAAc,EAAE,MAAM;IACtBC,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE,aAAa;IAClCT,YAAY,EAAEX,KAAK,CAACQ,OAAO,CAAC,CAAC;EAC/B,CAAC;EACDa,YAAY,EAAE;IACZL,QAAQ,EAAE,MAAM;IAChBM,KAAK,EAAEtB,KAAK,CAACI,OAAO,CAACmB,IAAI,CAACC,SAAS;IACnCb,YAAY,EAAEX,KAAK,CAACQ,OAAO,CAAC,CAAC;EAC/B,CAAC;EACDiB,WAAW,EAAE;IACX,6BAA6B,EAAE;MAC7BH,KAAK,EAAEtB,KAAK,CAACI,OAAO,CAACmB,IAAI,CAACC;IAC5B,CAAC;IACD,iBAAiB,EAAE;MACjBF,KAAK,EAAEtB,KAAK,CAACI,OAAO,CAACmB,IAAI,CAACC,SAAS;MACnCE,cAAc,EAAE,MAAM;MACtBV,QAAQ,EAAE,UAAU;MACpB,SAAS,EAAE;QACTM,KAAK,EAAEtB,KAAK,CAACI,OAAO,CAACuB,OAAO,CAACC,IAAI;QACjCF,cAAc,EAAE;MAClB;IACF,CAAC;IACD,uBAAuB,EAAE;MACvBJ,KAAK,EAAEtB,KAAK,CAACI,OAAO,CAACuB,OAAO,CAACC,IAAI;MACjCZ,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE;IACd;EACF,CAAC;EACDY,WAAW,EAAE;IACX,OAAO,EAAE;MACPlB,YAAY,EAAEX,KAAK,CAACQ,OAAO,CAAC,CAAC;IAC/B;EACF,CAAC;EACDsB,WAAW,EAAE;IACXlB,OAAO,EAAEZ,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;IACzBK,YAAY,EAAE,EAAE;IAChBkB,SAAS,EAAE,6BAA6B;IACxCjB,MAAM,EAAE,4BAA4B;IACpCT,UAAU,EAAE;EACd,CAAC;EACD2B,SAAS,EAAE;IACTC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBxB,YAAY,EAAEX,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;IAC9BI,OAAO,EAAEZ,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;IACzBL,eAAe,EAAE,OAAO;IACxBU,YAAY,EAAE,EAAE;IAChBkB,SAAS,EAAE;EACb,CAAC;EACDK,QAAQ,EAAE;IACRpB,QAAQ,EAAE,MAAM;IAChBqB,WAAW,EAAErC,KAAK,CAACQ,OAAO,CAAC,GAAG;EAChC;AACF,CAAC,CAAC,CAAC;AAEH,MAAM8B,mBAAmB,GAAGA,CAAC;EAC3BC,KAAK;EACLC,QAAQ;EACRf,WAAW,GAAG,EAAE;EAChBgB,QAAQ;EACRC,QAAQ,GAAG,IAAI;EACfC,UAAU,GAAG,IAAI;EACjBX,SAAS;EACT,GAAGY;AACL,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAG9C,SAAS,CAAC,CAAC;EAE3B,MAAM+C,kBAAkB,GAAG,CACzB;IACEC,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE,YAAY;IAClBC,IAAI,eAAE5D,KAAA,CAAA6D,aAAA,CAACpD,IAAI;MAACqD,SAAS,EAAEN,OAAO,CAACT,QAAS;MAAAgB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE;EAC5C,CAAC,EACD,GAAGhC,WAAW,CACf;EAED,oBACEpC,KAAA,CAAA6D,aAAA,CAAC3D,SAAS,EAAAmE,MAAA,CAAAC,MAAA;IAACjB,QAAQ,EAAEA,QAAS;IAACS,SAAS,EAAEN,OAAO,CAAC5C;EAAc,GAAK2C,KAAK;IAAAQ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,IACvEd,UAAU,iBACTtD,KAAA,CAAA6D,aAAA,CAACtD,KAAK;IAACuD,SAAS,EAAEN,OAAO,CAACnC,UAAW;IAACkD,SAAS,EAAE,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEhDX,kBAAkB,CAACe,MAAM,GAAG,CAAC,iBAC5BxE,KAAA,CAAA6D,aAAA,CAACxD,WAAW;IACVoE,SAAS,eAAEzE,KAAA,CAAA6D,aAAA,CAACrD,YAAY;MAACmB,QAAQ,EAAC,OAAO;MAAAoC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAC7CN,SAAS,EAAEN,OAAO,CAACpB,WAAY;IAC/B,cAAW,YAAY;IAAA2B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEtBX,kBAAkB,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAChD7E,KAAA,CAAA6D,aAAA,CAACvD,IAAI;IACHwE,GAAG,EAAED,KAAM;IACXlB,IAAI,EAAEiB,KAAK,CAACjB,IAAK;IACjBoB,OAAO,EAAGC,CAAC,IAAK;MACd,IAAIJ,KAAK,CAACG,OAAO,EAAE;QACjBC,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBL,KAAK,CAACG,OAAO,CAAC,CAAC;MACjB;IACF,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFpE,KAAA,CAAA6D,aAAA,CAACzD,GAAG;IAACwC,OAAO,EAAC,MAAM;IAACE,UAAU,EAAC,QAAQ;IAAAiB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpCQ,KAAK,CAAChB,IAAI,EACVgB,KAAK,CAAClB,KACJ,CACD,CACP,CAAC,eACF1D,KAAA,CAAA6D,aAAA,CAAC1D,UAAU;IAAA4D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACRX,kBAAkB,CAACA,kBAAkB,CAACe,MAAM,GAAG,CAAC,CAAC,CAACd,KACzC,CACD,CACd,EAGAR,KAAK,iBACJlD,KAAA,CAAA6D,aAAA,CAAC1D,UAAU;IAAC2D,SAAS,EAAEN,OAAO,CAAC9B,SAAU;IAACwD,OAAO,EAAC,IAAI;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnDlB,KACS,CACb,EAGAC,QAAQ,iBACPnD,KAAA,CAAA6D,aAAA,CAAC1D,UAAU;IAAC2D,SAAS,EAAEN,OAAO,CAACxB,YAAa;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzCjB,QACS,CAET,CACR,EAGAR,SAAS,iBACR3C,KAAA,CAAA6D,aAAA;IAAKC,SAAS,EAAEN,OAAO,CAACb,SAAU;IAAAoB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/BzB,SACE,CACN,eAGD3C,KAAA,CAAA6D,aAAA;IAAKC,SAAS,EAAEN,OAAO,CAAChB,WAAY;IAAAuB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjChB,QACE,CACI,CAAC;AAEhB,CAAC;AAED,eAAeH,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}