{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"circle\", {\n  cx: \"4.5\",\n  cy: \"9.5\",\n  r: \"1.5\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M22.17 9.17c0-3.91-3.19-7.06-7.11-7-3.83.06-6.99 3.37-6.88 7.19.09 3.38 2.58 6.16 5.83 6.7V20H6v-3h.5c.28 0 .5-.22.5-.5V13c0-.55-.45-1-1-1H3c-.55 0-1 .45-1 1v3.5c0 .28.22.5.5.5H3v4c0 .55.45 1 1 1h14c.55 0 1-.45 1-1s-.45-1-1-1h-2v-3.88c3.47-.41 6.17-3.36 6.17-6.95z\"\n})), 'NaturePeopleRounded');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "Fragment", "cx", "cy", "r", "d"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/NaturePeopleRounded.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"circle\", {\n  cx: \"4.5\",\n  cy: \"9.5\",\n  r: \"1.5\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M22.17 9.17c0-3.91-3.19-7.06-7.11-7-3.83.06-6.99 3.37-6.88 7.19.09 3.38 2.58 6.16 5.83 6.7V20H6v-3h.5c.28 0 .5-.22.5-.5V13c0-.55-.45-1-1-1H3c-.55 0-1 .45-1 1v3.5c0 .28.22.5.5.5H3v4c0 .55.45 1 1 1h14c.55 0 1-.45 1-1s-.45-1-1-1h-2v-3.88c3.47-.41 6.17-3.36 6.17-6.95z\"\n})), 'NaturePeopleRounded');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAACF,KAAK,CAACG,QAAQ,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAC9HE,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE,KAAK;EACTC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,aAAaN,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC3CK,CAAC,EAAE;AACL,CAAC,CAAC,CAAC,EAAE,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}