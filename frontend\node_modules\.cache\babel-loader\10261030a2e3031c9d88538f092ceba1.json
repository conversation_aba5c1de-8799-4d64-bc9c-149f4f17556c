{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon(/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M7 17h14v-6h-2v2h-2v-2h-2V9h2V7h2v2h2V3H7v14zm2-5h3v-1h-1c-1.1 0-2-.89-2-2V8c0-1.11.9-2 2-2h1c1.1 0 2 .89 2 2v4c0 1.11-.9 2-2 2H9v-2z\",\n  opacity: \".3\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M19 21H3V5H1v16c0 1.1.9 2 2 2h16v-2z\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M11 8h1v1h-1z\",\n  opacity: \".3\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M12 6h-1c-1.1 0-2 .89-2 2v1c0 1.11.9 2 2 2h1v1H9v2h3c1.1 0 2-.89 2-2V8c0-1.11-.9-2-2-2zm0 3h-1V8h1v1zm9-8H7c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 8h-2V7h-2v2h-2v2h2v2h2v-2h2v6H7V3h14v6z\"\n})), 'Filter9PlusTwoTone');", "map": {"version": 3, "names": ["React", "createSvgIcon", "createElement", "Fragment", "d", "opacity"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/@material-ui/icons/esm/Filter9PlusTwoTone.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nexport default createSvgIcon( /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M7 17h14v-6h-2v2h-2v-2h-2V9h2V7h2v2h2V3H7v14zm2-5h3v-1h-1c-1.1 0-2-.89-2-2V8c0-1.11.9-2 2-2h1c1.1 0 2 .89 2 2v4c0 1.11-.9 2-2 2H9v-2z\",\n  opacity: \".3\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M19 21H3V5H1v16c0 1.1.9 2 2 2h16v-2z\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M11 8h1v1h-1z\",\n  opacity: \".3\"\n}), /*#__PURE__*/React.createElement(\"path\", {\n  d: \"M12 6h-1c-1.1 0-2 .89-2 2v1c0 1.11.9 2 2 2h1v1H9v2h3c1.1 0 2-.89 2-2V8c0-1.11-.9-2-2-2zm0 3h-1V8h1v1zm9-8H7c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 8h-2V7h-2v2h-2v2h2v2h2v-2h2v6H7V3h14v6z\"\n})), 'Filter9PlusTwoTone');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,eAAeA,aAAa,CAAE,aAAaD,KAAK,CAACE,aAAa,CAACF,KAAK,CAACG,QAAQ,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC5HE,CAAC,EAAE,uIAAuI;EAC1IC,OAAO,EAAE;AACX,CAAC,CAAC,EAAE,aAAaL,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC3CE,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,aAAaJ,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC3CE,CAAC,EAAE,eAAe;EAClBC,OAAO,EAAE;AACX,CAAC,CAAC,EAAE,aAAaL,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC3CE,CAAC,EAAE;AACL,CAAC,CAAC,CAAC,EAAE,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}