{"ast": null, "code": "import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\nimport { ColorWrap, Hue } from '../common';\nimport SliderSwatches from './SliderSwatches';\nimport SliderPointer from './SliderPointer';\nexport var Slider = function Slider(_ref) {\n  var hsl = _ref.hsl,\n    onChange = _ref.onChange,\n    pointer = _ref.pointer,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var styles = reactCSS(merge({\n    'default': {\n      hue: {\n        height: '12px',\n        position: 'relative'\n      },\n      Hue: {\n        radius: '2px'\n      }\n    }\n  }, passedStyles));\n  return React.createElement('div', {\n    style: styles.wrap || {},\n    className: 'slider-picker ' + className\n  }, React.createElement('div', {\n    style: styles.hue\n  }, React.createElement(Hue, {\n    style: styles.Hue,\n    hsl: hsl,\n    pointer: pointer,\n    onChange: onChange\n  })), React.createElement('div', {\n    style: styles.swatches\n  }, React.createElement(SliderSwatches, {\n    hsl: hsl,\n    onClick: onChange\n  })));\n};\nSlider.propTypes = {\n  styles: PropTypes.object\n};\nSlider.defaultProps = {\n  pointer: SliderPointer,\n  styles: {}\n};\nexport default ColorWrap(Slider);", "map": {"version": 3, "names": ["React", "PropTypes", "reactCSS", "merge", "ColorWrap", "<PERSON><PERSON>", "SliderSwatches", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Slide<PERSON>", "_ref", "hsl", "onChange", "pointer", "_ref$styles", "styles", "passedStyles", "undefined", "_ref$className", "className", "hue", "height", "position", "radius", "createElement", "style", "wrap", "swatches", "onClick", "propTypes", "object", "defaultProps"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/react-color/es/components/slider/Slider.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\n\nimport { ColorWrap, Hue } from '../common';\nimport SliderSwatches from './SliderSwatches';\nimport SliderPointer from './SliderPointer';\n\nexport var Slider = function Slider(_ref) {\n  var hsl = _ref.hsl,\n      onChange = _ref.onChange,\n      pointer = _ref.pointer,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var styles = reactCSS(merge({\n    'default': {\n      hue: {\n        height: '12px',\n        position: 'relative'\n      },\n      Hue: {\n        radius: '2px'\n      }\n    }\n  }, passedStyles));\n\n  return React.createElement(\n    'div',\n    { style: styles.wrap || {}, className: 'slider-picker ' + className },\n    React.createElement(\n      'div',\n      { style: styles.hue },\n      React.createElement(Hue, {\n        style: styles.Hue,\n        hsl: hsl,\n        pointer: pointer,\n        onChange: onChange\n      })\n    ),\n    React.createElement(\n      'div',\n      { style: styles.swatches },\n      React.createElement(SliderSwatches, { hsl: hsl, onClick: onChange })\n    )\n  );\n};\n\nSlider.propTypes = {\n  styles: PropTypes.object\n};\nSlider.defaultProps = {\n  pointer: SliderPointer,\n  styles: {}\n};\n\nexport default ColorWrap(Slider);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,SAAS,EAAEC,GAAG,QAAQ,WAAW;AAC1C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACxC,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;IACdC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,WAAW,GAAGJ,IAAI,CAACK,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,cAAc,GAAGR,IAAI,CAACS,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKD,SAAS,GAAG,EAAE,GAAGC,cAAc;EAElE,IAAIH,MAAM,GAAGZ,QAAQ,CAACC,KAAK,CAAC;IAC1B,SAAS,EAAE;MACTgB,GAAG,EAAE;QACHC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE;MACZ,CAAC;MACDhB,GAAG,EAAE;QACHiB,MAAM,EAAE;MACV;IACF;EACF,CAAC,EAAEP,YAAY,CAAC,CAAC;EAEjB,OAAOf,KAAK,CAACuB,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAEV,MAAM,CAACW,IAAI,IAAI,CAAC,CAAC;IAAEP,SAAS,EAAE,gBAAgB,GAAGA;EAAU,CAAC,EACrElB,KAAK,CAACuB,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEV,MAAM,CAACK;EAAI,CAAC,EACrBnB,KAAK,CAACuB,aAAa,CAAClB,GAAG,EAAE;IACvBmB,KAAK,EAAEV,MAAM,CAACT,GAAG;IACjBK,GAAG,EAAEA,GAAG;IACRE,OAAO,EAAEA,OAAO;IAChBD,QAAQ,EAAEA;EACZ,CAAC,CACH,CAAC,EACDX,KAAK,CAACuB,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEV,MAAM,CAACY;EAAS,CAAC,EAC1B1B,KAAK,CAACuB,aAAa,CAACjB,cAAc,EAAE;IAAEI,GAAG,EAAEA,GAAG;IAAEiB,OAAO,EAAEhB;EAAS,CAAC,CACrE,CACF,CAAC;AACH,CAAC;AAEDH,MAAM,CAACoB,SAAS,GAAG;EACjBd,MAAM,EAAEb,SAAS,CAAC4B;AACpB,CAAC;AACDrB,MAAM,CAACsB,YAAY,GAAG;EACpBlB,OAAO,EAAEL,aAAa;EACtBO,MAAM,EAAE,CAAC;AACX,CAAC;AAED,eAAeV,SAAS,CAACI,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}