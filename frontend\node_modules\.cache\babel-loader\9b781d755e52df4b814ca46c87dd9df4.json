{"ast": null, "code": "import v35 from './v35.js';\nimport sha1 from './sha1.js';\nvar v5 = v35('v5', 0x50, sha1);\nexport default v5;", "map": {"version": 3, "names": ["v35", "sha1", "v5"], "sources": ["C:/Users/<USER>/dev/whaticket/frontend/node_modules/uuid/dist/esm-browser/v5.js"], "sourcesContent": ["import v35 from './v35.js';\nimport sha1 from './sha1.js';\nvar v5 = v35('v5', 0x50, sha1);\nexport default v5;"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,OAAOC,IAAI,MAAM,WAAW;AAC5B,IAAIC,EAAE,GAAGF,GAAG,CAAC,IAAI,EAAE,IAAI,EAAEC,IAAI,CAAC;AAC9B,eAAeC,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module"}